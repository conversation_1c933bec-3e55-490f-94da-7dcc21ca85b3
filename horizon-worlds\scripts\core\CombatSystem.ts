/**
 * Storm RPG - Combat System
 * Sistema de combate em tempo real com mecânicas d20
 */

import { PlayerManager, PlayerData } from './PlayerManager';
import { PlayerStats } from './AttributeSystem';
import { <PERSON><PERSON><PERSON>oller, DiceResult } from '../utils/DiceRoller';
import { stormEvents } from '../utils/EventSystem';
import { GAME_CONFIG } from '../utils/Constants';

export interface CombatResult {
    hit: boolean;
    damage: number;
    isCritical: boolean;
    diceResult: DiceResult;
    target: string;
    attacker: string;
}

export interface CombatEntity {
    id: string;
    name: string;
    stats: PlayerStats;
    position: { x: number; y: number; z: number };
    isPlayer: boolean;
    isAlive: boolean;
}

export class CombatSystem {
    private playerManager: PlayerManager;
    private combatEntities: Map<string, CombatEntity> = new Map();
    private activeCombats: Set<string> = new Set();

    constructor(playerManager: PlayerManager) {
        this.playerManager = playerManager;
        this.setupCombatEvents();
    }

    /**
     * Configura eventos de combate
     */
    private setupCombatEvents(): void {
        // Escutar eventos de dano
        stormEvents.onDamageDealt((attacker, target, damage) => {
            this.processDamageEffects(attacker, target, damage);
        });

        // Escutar eventos de crítico
        stormEvents.onCriticalHit((attacker, target, damage) => {
            this.processCriticalHitEffects(attacker, target, damage);
        });
    }

    /**
     * Registra uma entidade de combate
     */
    registerCombatEntity(entity: CombatEntity): void {
        this.combatEntities.set(entity.id, entity);
    }

    /**
     * Remove uma entidade de combate
     */
    unregisterCombatEntity(entityId: string): void {
        this.combatEntities.delete(entityId);
        this.activeCombats.delete(entityId);
    }

    /**
     * Realiza um ataque corpo a corpo
     */
    performMeleeAttack(
        attackerId: string, 
        targetId: string, 
        weaponDamage: string = "1d8"
    ): CombatResult | null {
        const attacker = this.getEntity(attackerId);
        const target = this.getEntity(targetId);

        if (!attacker || !target || !target.isAlive) {
            return null;
        }

        // Calcular bônus de ataque
        const attackBonus = this.calculateAttackBonus(attacker, 'melee');
        const targetAC = this.calculateArmorClass(target);

        // Rolar ataque
        const attackResult = DiceRoller.rollAttack(attackBonus, targetAC);

        let damage = 0;
        let isCritical = false;

        if (attackResult.hit) {
            // Calcular dano
            const baseDamage = DiceRoller.rollDamage(weaponDamage);
            const strengthMod = attacker.stats.getAttributeModifier('strength');
            damage = baseDamage + strengthMod;

            // Verificar crítico
            if (attackResult.result.isCriticalHit) {
                damage *= 2; // Dano dobrado em crítico
                isCritical = true;
            }

            // Aplicar dano
            this.applyDamage(targetId, damage, attackerId);
        }

        const result: CombatResult = {
            hit: attackResult.hit,
            damage,
            isCritical,
            diceResult: attackResult.result,
            target: targetId,
            attacker: attackerId
        };

        // Emitir eventos
        if (attackResult.hit) {
            if (isCritical) {
                stormEvents.emitCriticalHit(attackerId, targetId, damage);
            } else {
                stormEvents.emitDamageDealt(attackerId, targetId, damage);
            }
        }

        return result;
    }

    /**
     * Realiza um ataque à distância
     */
    performRangedAttack(
        attackerId: string, 
        targetId: string, 
        weaponDamage: string = "1d6",
        range: number = 30
    ): CombatResult | null {
        const attacker = this.getEntity(attackerId);
        const target = this.getEntity(targetId);

        if (!attacker || !target || !target.isAlive) {
            return null;
        }

        // Verificar alcance
        const distance = this.calculateDistance(attacker.position, target.position);
        if (distance > range) {
            console.log("Alvo fora de alcance");
            return null;
        }

        // Calcular bônus de ataque
        const attackBonus = this.calculateAttackBonus(attacker, 'ranged');
        const targetAC = this.calculateArmorClass(target);

        // Rolar ataque
        const attackResult = DiceRoller.rollAttack(attackBonus, targetAC);

        let damage = 0;
        let isCritical = false;

        if (attackResult.hit) {
            // Calcular dano
            const baseDamage = DiceRoller.rollDamage(weaponDamage);
            const dexterityMod = attacker.stats.getAttributeModifier('dexterity');
            damage = baseDamage + dexterityMod;

            // Verificar crítico
            if (attackResult.result.isCriticalHit) {
                damage *= 2;
                isCritical = true;
            }

            // Aplicar dano
            this.applyDamage(targetId, damage, attackerId);
        }

        const result: CombatResult = {
            hit: attackResult.hit,
            damage,
            isCritical,
            diceResult: attackResult.result,
            target: targetId,
            attacker: attackerId
        };

        // Emitir eventos
        if (attackResult.hit) {
            if (isCritical) {
                stormEvents.emitCriticalHit(attackerId, targetId, damage);
            } else {
                stormEvents.emitDamageDealt(attackerId, targetId, damage);
            }
        }

        return result;
    }

    /**
     * Calcula bônus de ataque
     */
    private calculateAttackBonus(entity: CombatEntity, attackType: 'melee' | 'ranged'): number {
        const baseAttackBonus = entity.stats.getBaseAttackBonus();
        
        let attributeBonus = 0;
        if (attackType === 'melee') {
            attributeBonus = entity.stats.getAttributeModifier('strength');
        } else {
            attributeBonus = entity.stats.getAttributeModifier('dexterity');
        }

        return baseAttackBonus + attributeBonus;
    }

    /**
     * Calcula classe de armadura
     */
    private calculateArmorClass(entity: CombatEntity): number {
        return entity.stats.derivedStats.armorClass;
    }

    /**
     * Calcula distância entre duas posições
     */
    private calculateDistance(pos1: { x: number; y: number; z: number }, pos2: { x: number; y: number; z: number }): number {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * Aplica dano a uma entidade
     */
    private applyDamage(targetId: string, damage: number, attackerId?: string): boolean {
        const target = this.getEntity(targetId);
        if (!target) return false;

        const actualDamage = target.stats.takeDamage(damage);
        
        // Verificar se morreu
        if (!target.stats.isAlive()) {
            this.handleEntityDeath(targetId, attackerId);
            return true; // Morreu
        }

        return false; // Ainda vivo
    }

    /**
     * Lida com a morte de uma entidade
     */
    private handleEntityDeath(entityId: string, killerId?: string): void {
        const entity = this.getEntity(entityId);
        if (!entity) return;

        entity.isAlive = false;
        this.activeCombats.delete(entityId);

        if (entity.isPlayer) {
            // Jogador morreu
            this.playerManager.onPlayerDied(entityId, killerId);
        } else {
            // Inimigo morreu
            if (killerId) {
                // Dar XP ao jogador que matou
                this.playerManager.addExperience(killerId, GAME_CONFIG.XP_PER_ENEMY_KILL);
            }
            
            // Remover entidade
            this.unregisterCombatEntity(entityId);
        }
    }

    /**
     * Obtém entidade por ID
     */
    private getEntity(entityId: string): CombatEntity | null {
        // Primeiro tenta encontrar nos jogadores
        const playerData = this.playerManager.getPlayerData(entityId);
        if (playerData) {
            return {
                id: playerData.id,
                name: playerData.name,
                stats: playerData.stats,
                position: { x: 0, y: 0, z: 0 }, // TODO: Obter posição real
                isPlayer: true,
                isAlive: playerData.isAlive
            };
        }

        // Depois tenta encontrar nas entidades de combate
        return this.combatEntities.get(entityId) || null;
    }

    /**
     * Processa efeitos de dano
     */
    private processDamageEffects(attackerId: string, targetId: string, damage: number): void {
        // Aqui podem ser adicionados efeitos especiais baseados no dano
        // Por exemplo: knockback, sangramento, etc.
    }

    /**
     * Processa efeitos de crítico
     */
    private processCriticalHitEffects(attackerId: string, targetId: string, damage: number): void {
        // Efeitos especiais de crítico
        // Por exemplo: atordoamento, efeitos visuais especiais, etc.
    }

    /**
     * Verifica se duas entidades estão em combate
     */
    areInCombat(entityId1: string, entityId2: string): boolean {
        return this.activeCombats.has(entityId1) && this.activeCombats.has(entityId2);
    }

    /**
     * Inicia combate entre entidades
     */
    startCombat(entityId1: string, entityId2: string): void {
        this.activeCombats.add(entityId1);
        this.activeCombats.add(entityId2);
        
        stormEvents.eventSystem.emit('combat_started', {
            participants: [entityId1, entityId2]
        });
    }

    /**
     * Termina combate
     */
    endCombat(entityId1: string, entityId2: string): void {
        this.activeCombats.delete(entityId1);
        this.activeCombats.delete(entityId2);
        
        stormEvents.eventSystem.emit('combat_ended', {
            participants: [entityId1, entityId2]
        });
    }

    /**
     * Obtém todas as entidades em combate
     */
    getEntitiesInCombat(): CombatEntity[] {
        const entities: CombatEntity[] = [];
        
        for (const entityId of this.activeCombats) {
            const entity = this.getEntity(entityId);
            if (entity) {
                entities.push(entity);
            }
        }
        
        return entities;
    }

    /**
     * Encontra inimigos próximos
     */
    findNearbyEnemies(entityId: string, radius: number = 10): CombatEntity[] {
        const entity = this.getEntity(entityId);
        if (!entity) return [];

        const nearbyEnemies: CombatEntity[] = [];

        // Verificar outras entidades
        for (const [id, otherEntity] of this.combatEntities) {
            if (id === entityId || !otherEntity.isAlive) continue;

            const distance = this.calculateDistance(entity.position, otherEntity.position);
            if (distance <= radius) {
                nearbyEnemies.push(otherEntity);
            }
        }

        return nearbyEnemies;
    }

    /**
     * Aplica cura a uma entidade
     */
    applyHealing(targetId: string, healing: number, healerId?: string): number {
        const target = this.getEntity(targetId);
        if (!target) return 0;

        const actualHealing = target.stats.heal(healing);
        
        // Emitir evento de cura
        stormEvents.eventSystem.emit('healing_applied', {
            targetId,
            healing: actualHealing,
            healerId
        });

        return actualHealing;
    }
}
