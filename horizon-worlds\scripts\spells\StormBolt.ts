/**
 * Storm RPG - Storm Bolt Spell
 * Raio da Tempestade - magia icônica do universo Storm
 */

import { BaseSpell, SpellTarget, SpellEffect } from './BaseSpell';
import { PlayerStats } from '../core/AttributeSystem';
import { SPELL_CONFIGS, ASSET_IDS } from '../utils/Constants';
import { stormEvents } from '../utils/EventSystem';
import { DiceRoller } from '../utils/DiceRoller';

export class StormBolt extends BaseSpell {
    constructor() {
        const config = SPELL_CONFIGS.STORM_BOLT;
        super(
            config.name,
            "Raio elétrico da tempestade que pode saltar entre inimigos",
            config.manaCost,
            config.castTime,
            config.cooldown,
            config.range,
            2 // Nível da magia
        );
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target || !target.position) {
            console.error("Storm Bolt requer um alvo com posição");
            return null;
        }

        // Criar raio elétrico
        this.createLightningBolt(caster, target.position);

        // Calcular dano
        const damage = this.calculateSpellDamage(caster, SPELL_CONFIGS.STORM_BOLT.damage);

        // Aplicar dano ao alvo principal
        this.applyLightningDamage(target, damage, caster);

        // Chance de saltar para outros inimigos
        this.attemptChainLightning(caster, target.position, damage);

        return {
            damage,
            statusEffect: 'stunned' // Chance de atordoar
        };
    }

    /**
     * Cria o efeito visual do raio
     */
    private createLightningBolt(caster: PlayerStats, targetPosition: { x: number; y: number; z: number }): void {
        try {
            // TODO: Obter posição real do conjurador
            const casterPosition = { x: 0, y: 1.5, z: 0 };

            // Criar entidade do raio
            const lightningEntity = Scene.createRootEntity();
            lightningEntity.setPosition(casterPosition.x, casterPosition.y, casterPosition.z);

            // Efeito de partículas do raio
            const particleComponent = lightningEntity.createComponent("engine:persistentParticleEffect", {
                assetId: ASSET_IDS.PARTICLES.LIGHTNING_BOLT
            });

            // Som do trovão
            const audioComponent = lightningEntity.createComponent("engine:audioSource", {
                assetId: ASSET_IDS.SOUNDS.THUNDER_CRACK,
                loop: false,
                volume: 1.0
            });
            audioComponent.play();

            // Animar raio do conjurador ao alvo
            this.animateLightningBolt(lightningEntity, casterPosition, targetPosition);

            // Criar flash de luz
            this.createLightningFlash(targetPosition);

        } catch (error) {
            console.error("Erro ao criar raio da tempestade:", error);
        }
    }

    /**
     * Anima o raio elétrico
     */
    private animateLightningBolt(
        lightningEntity: any,
        startPos: { x: number; y: number; z: number },
        endPos: { x: number; y: number; z: number }
    ): void {
        // Raio é instantâneo, mas vamos criar efeito visual
        const duration = 200; // Muito rápido
        const startTime = Date.now();

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress <= 1) {
                // Criar segmentos do raio com variação aleatória
                this.createLightningSegments(startPos, endPos, progress);
                requestAnimationFrame(animate);
            } else {
                // Raio terminou
                lightningEntity.destroy();
            }
        };

        animate();
    }

    /**
     * Cria segmentos do raio com variação
     */
    private createLightningSegments(
        startPos: { x: number; y: number; z: number },
        endPos: { x: number; y: number; z: number },
        progress: number
    ): void {
        // Criar pontos intermediários com variação aleatória
        const segments = 5;
        const variance = 0.5;

        for (let i = 0; i <= segments; i++) {
            const t = (i / segments) * progress;
            
            // Posição base interpolada
            const baseX = startPos.x + (endPos.x - startPos.x) * t;
            const baseY = startPos.y + (endPos.y - startPos.y) * t;
            const baseZ = startPos.z + (endPos.z - startPos.z) * t;

            // Adicionar variação aleatória (exceto nos pontos inicial e final)
            let finalX = baseX;
            let finalY = baseY;
            let finalZ = baseZ;

            if (i > 0 && i < segments) {
                finalX += (Math.random() - 0.5) * variance;
                finalY += (Math.random() - 0.5) * variance;
                finalZ += (Math.random() - 0.5) * variance;
            }

            // TODO: Criar segmento visual do raio
        }
    }

    /**
     * Cria flash de luz do impacto
     */
    private createLightningFlash(position: { x: number; y: number; z: number }): void {
        try {
            const flashEntity = Scene.createRootEntity();
            flashEntity.setPosition(position.x, position.y, position.z);

            // Luz intensa e breve
            const lightComponent = flashEntity.createComponent("engine:light", {
                type: "point",
                color: { r: 0.8, g: 0.9, b: 1.0 }, // Azul elétrico
                intensity: 10.0,
                range: 15.0
            });

            // Flash rápido
            setTimeout(() => {
                lightComponent.intensity = 0;
            }, 100);

            // Remover entidade
            setTimeout(() => {
                flashEntity.destroy();
            }, 500);

        } catch (error) {
            console.error("Erro ao criar flash de raio:", error);
        }
    }

    /**
     * Aplica dano elétrico ao alvo
     */
    private applyLightningDamage(target: SpellTarget, damage: number, caster: PlayerStats): void {
        if (!target.entity) return;

        try {
            const healthComponent = target.entity.getComponent("game:health");
            if (healthComponent) {
                // Teste de resistência (Reflexos para metade do dano)
                const saveSuccessful = this.rollSavingThrow(target.entity, 'reflex', 14);
                const finalDamage = saveSuccessful ? Math.floor(damage / 2) : damage;

                // Aplicar dano
                healthComponent.currentHealth -= finalDamage;

                // Chance de atordoar (10% base + 5% por nível do conjurador)
                const stunChance = 10 + (caster.level * 5);
                if (DiceRoller.rollPercentile() <= stunChance) {
                    this.applyStunEffect(target.entity);
                }

                // Emitir evento de dano
                stormEvents.emitDamageDealt(caster.playerClass, target.entityId || 'unknown', finalDamage);

                console.log(`Storm Bolt causou ${finalDamage} de dano elétrico`);

                // Verificar morte
                if (healthComponent.currentHealth <= 0) {
                    this.handleLightningDeath(target.entity, caster);
                }
            }
        } catch (error) {
            console.error("Erro ao aplicar dano elétrico:", error);
        }
    }

    /**
     * Aplica efeito de atordoamento
     */
    private applyStunEffect(entity: any): void {
        try {
            // TODO: Implementar sistema de status effects
            console.log("Alvo foi atordoado pelo raio!");
            
            // Efeito visual de atordoamento
            const position = entity.getPosition();
            this.createVisualEffect(position, "stun_effect", 3000);

        } catch (error) {
            console.error("Erro ao aplicar atordoamento:", error);
        }
    }

    /**
     * Tenta criar raio em cadeia
     */
    private attemptChainLightning(
        caster: PlayerStats,
        originPosition: { x: number; y: number; z: number },
        baseDamage: number
    ): void {
        // 30% de chance de saltar para outro inimigo
        if (DiceRoller.rollPercentile() <= 30) {
            const nearbyEnemies = this.findEntitiesInArea(originPosition, 10);
            
            if (nearbyEnemies.length > 0) {
                const randomEnemy = nearbyEnemies[Math.floor(Math.random() * nearbyEnemies.length)];
                const chainDamage = Math.floor(baseDamage * 0.7); // 70% do dano original

                // Criar raio secundário
                this.createSecondaryLightning(originPosition, randomEnemy.getPosition());

                // Aplicar dano reduzido
                setTimeout(() => {
                    this.applyLightningDamage(
                        { entity: randomEnemy, position: randomEnemy.getPosition() },
                        chainDamage,
                        caster
                    );
                }, 300);

                console.log("Storm Bolt saltou para outro inimigo!");
            }
        }
    }

    /**
     * Cria raio secundário (chain lightning)
     */
    private createSecondaryLightning(
        startPos: { x: number; y: number; z: number },
        endPos: { x: number; y: number; z: number }
    ): void {
        try {
            const chainEntity = Scene.createRootEntity();
            chainEntity.setPosition(startPos.x, startPos.y, startPos.z);

            // Efeito menor para o raio secundário
            const particleComponent = chainEntity.createComponent("engine:persistentParticleEffect", {
                assetId: ASSET_IDS.PARTICLES.LIGHTNING_BOLT
            });

            // Som mais suave
            const audioComponent = chainEntity.createComponent("engine:audioSource", {
                assetId: ASSET_IDS.SOUNDS.THUNDER_CRACK,
                loop: false,
                volume: 0.5
            });
            audioComponent.play();

            // Animar raio secundário
            this.animateLightningBolt(chainEntity, startPos, endPos);

        } catch (error) {
            console.error("Erro ao criar raio secundário:", error);
        }
    }

    /**
     * Lida com morte por raio
     */
    private handleLightningDeath(entity: any, caster: PlayerStats): void {
        try {
            const position = entity.getPosition();

            // Efeito especial de morte por eletricidade
            this.createVisualEffect(position, ASSET_IDS.PARTICLES.LIGHTNING_BOLT, 2000);
            this.playSoundEffect(ASSET_IDS.SOUNDS.THUNDER_CRACK, position);

            // Emitir evento
            stormEvents.eventSystem.emit('enemy_died', {
                entityId: entity.id,
                killer: caster.playerClass,
                cause: 'storm_bolt'
            });

        } catch (error) {
            console.error("Erro ao lidar com morte por raio:", error);
        }
    }

    /**
     * Sobrescreve a animação de conjuração
     */
    protected startCastingAnimation(targetPosition: { x: number; y: number; z: number }): void {
        console.log("Canalizando a energia da tempestade...");
        
        // TODO: Efeitos visuais de conjuração
        // Faíscas ao redor do conjurador, vento, etc.
    }
}

// Versão aprimorada: Tempestade de Raios
export class LightningStorm extends StormBolt {
    constructor() {
        super();
        this.name = "Tempestade de Raios";
        this.description = "Múltiplos raios atingem uma área";
        this.manaCost = 15;
        this.level = 6;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target || !target.position) return null;

        const damage = this.calculateSpellDamage(caster, "4d6");
        const stormRadius = 8;

        // Encontrar todas as entidades na área
        const entitiesInArea = this.findEntitiesInArea(target.position, stormRadius);

        // Criar múltiplos raios
        this.createLightningStorm(target.position, entitiesInArea.length + 1);

        // Aplicar dano a todas as entidades
        entitiesInArea.forEach((entity, index) => {
            setTimeout(() => {
                this.applyLightningDamage(
                    { entity, position: entity.getPosition() },
                    damage,
                    caster
                );
            }, index * 200); // Raios em sequência
        });

        return {
            damage,
            areaOfEffect: stormRadius,
            statusEffect: 'stunned'
        };
    }

    /**
     * Cria tempestade de raios
     */
    private createLightningStorm(center: { x: number; y: number; z: number }, boltCount: number): void {
        for (let i = 0; i < boltCount; i++) {
            setTimeout(() => {
                // Posição aleatória ao redor do centro
                const angle = (Math.PI * 2 * i) / boltCount;
                const radius = Math.random() * 6;
                const targetPos = {
                    x: center.x + Math.cos(angle) * radius,
                    y: center.y,
                    z: center.z + Math.sin(angle) * radius
                };

                // Criar raio do céu
                const skyPos = { x: targetPos.x, y: targetPos.y + 20, z: targetPos.z };
                this.createLightningBolt(null as any, targetPos);

            }, i * 200);
        }
    }
}

// Versão básica: Faísca
export class Spark extends StormBolt {
    constructor() {
        super();
        this.name = "Faísca";
        this.description = "Pequena descarga elétrica";
        this.manaCost = 1;
        this.level = 0; // Truque
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target) return null;

        const damage = this.calculateSpellDamage(caster, "1d3");
        
        // Efeito visual menor
        this.createSparkEffect(target.position);
        
        // Aplicar dano menor
        this.applyLightningDamage(target, damage, caster);

        return { damage };
    }

    /**
     * Cria efeito de faísca
     */
    private createSparkEffect(position: { x: number; y: number; z: number }): void {
        // Efeito visual simples
        this.createVisualEffect(position, "spark_effect", 500);
        this.playSoundEffect("spark_sound", position);
    }
}
