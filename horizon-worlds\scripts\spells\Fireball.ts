/**
 * Storm RPG - Fireball Spell
 * Magia de Bola de Fogo com efeitos visuais e dano em área
 */

import { BaseSpell, SpellTarget, SpellEffect } from './BaseSpell';
import { PlayerStats } from '../core/AttributeSystem';
import { SPELL_CONFIGS, ASSET_IDS } from '../utils/Constants';
import { stormEvents } from '../utils/EventSystem';

export class Fireball extends BaseSpell {
    private areaOfEffect: number;

    constructor() {
        const config = SPELL_CONFIGS.FIREBALL;
        super(
            config.name,
            "Uma explosão de fogo que causa dano em área aos inimigos",
            config.manaCost,
            config.castTime,
            config.cooldown,
            config.range,
            3 // Nível da magia
        );
        this.areaOfEffect = config.areaOfEffect;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target || !target.position) {
            console.error("Fireball requer um alvo com posição");
            return null;
        }

        // Criar efeito visual da bola de fogo voando
        this.createFireballProjectile(caster, target.position);

        // Aguardar chegada do projétil e então explodir
        setTimeout(() => {
            this.explodeFireball(caster, target.position);
        }, 1000);

        return {
            damage: this.calculateSpellDamage(caster, SPELL_CONFIGS.FIREBALL.damage),
            areaOfEffect: this.areaOfEffect
        };
    }

    /**
     * Cria o projétil da bola de fogo
     */
    private createFireballProjectile(caster: PlayerStats, targetPosition: { x: number; y: number; z: number }): void {
        try {
            // Criar entidade do projétil
            const projectile = Scene.createRootEntity();
            
            // TODO: Obter posição do conjurador
            const casterPosition = { x: 0, y: 1, z: 0 }; // Placeholder
            projectile.setPosition(casterPosition.x, casterPosition.y + 1.5, casterPosition.z);

            // Adicionar efeito de partículas
            const particleComponent = projectile.createComponent("engine:persistentParticleEffect", {
                assetId: ASSET_IDS.PARTICLES.FIREBALL_EXPLOSION
            });

            // Adicionar som de lançamento
            const audioComponent = projectile.createComponent("engine:audioSource", {
                assetId: ASSET_IDS.SOUNDS.FIREBALL_CAST,
                loop: false,
                volume: 0.8
            });
            audioComponent.play();

            // Animar movimento do projétil
            this.animateProjectile(projectile, casterPosition, targetPosition, 1000);

        } catch (error) {
            console.error("Erro ao criar projétil de bola de fogo:", error);
        }
    }

    /**
     * Anima o movimento do projétil
     */
    private animateProjectile(
        projectile: any,
        startPos: { x: number; y: number; z: number },
        endPos: { x: number; y: number; z: number },
        duration: number
    ): void {
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // Interpolação linear
            const currentX = startPos.x + (endPos.x - startPos.x) * progress;
            const currentY = startPos.y + (endPos.y - startPos.y) * progress;
            const currentZ = startPos.z + (endPos.z - startPos.z) * progress;

            projectile.setPosition(currentX, currentY, currentZ);

            if (progress < 1) {
                requestAnimationFrame(animate);
            } else {
                // Projétil chegou ao destino
                projectile.destroy();
            }
        };

        animate();
    }

    /**
     * Cria a explosão da bola de fogo
     */
    private explodeFireball(caster: PlayerStats, position: { x: number; y: number; z: number }): void {
        // Criar efeito visual da explosão
        this.createVisualEffect(
            position,
            ASSET_IDS.PARTICLES.FIREBALL_EXPLOSION,
            3000
        );

        // Reproduzir som da explosão
        this.playSoundEffect(ASSET_IDS.SOUNDS.FIREBALL_CAST, position);

        // Calcular dano
        const damage = this.calculateSpellDamage(caster, SPELL_CONFIGS.FIREBALL.damage);

        // Encontrar entidades na área de efeito
        const entitiesInArea = this.findEntitiesInArea(position, this.areaOfEffect);

        // Aplicar dano a cada entidade
        entitiesInArea.forEach(entity => {
            this.applyFireDamage(entity, damage, caster);
        });

        // Emitir evento de magia acertada
        stormEvents.eventSystem.emit('spell_hit', {
            spellName: this.name,
            caster: caster.playerClass,
            position,
            damage,
            entitiesAffected: entitiesInArea.length
        });

        console.log(`Fireball explodiu em (${position.x}, ${position.y}, ${position.z}) causando ${damage} de dano`);
    }

    /**
     * Aplica dano de fogo a uma entidade
     */
    private applyFireDamage(entity: any, damage: number, caster: PlayerStats): void {
        try {
            // Verificar se a entidade tem componente de vida
            const healthComponent = entity.getComponent("game:health");
            if (healthComponent) {
                // Teste de resistência (Reflexos para metade do dano)
                const saveSuccessful = this.rollSavingThrow(entity, 'reflex', 15);
                const finalDamage = saveSuccessful ? Math.floor(damage / 2) : damage;

                // Aplicar dano
                healthComponent.currentHealth -= finalDamage;

                // Emitir evento de dano
                stormEvents.emitDamageDealt(caster.playerClass, entity.id || 'unknown', finalDamage);

                // Verificar se a entidade morreu
                if (healthComponent.currentHealth <= 0) {
                    this.handleEntityDeath(entity, caster);
                }

                console.log(`Entidade ${entity.id} recebeu ${finalDamage} de dano de fogo`);
            }
        } catch (error) {
            console.error("Erro ao aplicar dano de fogo:", error);
        }
    }

    /**
     * Lida com a morte de uma entidade
     */
    private handleEntityDeath(entity: any, caster: PlayerStats): void {
        try {
            // Criar efeito de morte
            const position = entity.getPosition();
            this.createVisualEffect(
                position,
                ASSET_IDS.PARTICLES.DEATH_EXPLOSION,
                2000
            );

            // Reproduzir som de morte
            this.playSoundEffect(ASSET_IDS.SOUNDS.ENEMY_DEATH, position);

            // Emitir evento de morte
            stormEvents.eventSystem.emit('enemy_died', {
                entityId: entity.id,
                killer: caster.playerClass,
                cause: 'fireball'
            });

        } catch (error) {
            console.error("Erro ao lidar com morte da entidade:", error);
        }
    }

    /**
     * Sobrescreve a animação de conjuração
     */
    protected startCastingAnimation(targetPosition: { x: number; y: number; z: number }): void {
        console.log(`Conjurando Bola de Fogo em direção a (${targetPosition.x}, ${targetPosition.y}, ${targetPosition.z})...`);
        
        // TODO: Adicionar efeitos visuais de conjuração
        // Por exemplo: partículas de fogo ao redor das mãos do conjurador
    }
}

// Versão aprimorada da Bola de Fogo
export class GreaterFireball extends Fireball {
    constructor() {
        super();
        this.name = "Bola de Fogo Maior";
        this.description = "Uma versão mais poderosa da Bola de Fogo com maior dano e área";
        this.manaCost = 12;
        this.level = 6;
        this.areaOfEffect = 9; // Área maior
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        const result = super.executeSpell(caster, target);
        
        if (result) {
            // Dano aumentado para a versão maior
            result.damage = this.calculateSpellDamage(caster, "8d6");
            result.areaOfEffect = this.areaOfEffect;
        }

        return result;
    }
}

// Bola de Fogo da Tempestade (versão especial)
export class StormFireball extends Fireball {
    constructor() {
        super();
        this.name = "Bola de Fogo da Tempestade";
        this.description = "Bola de fogo imbuída com energia da Storm, causando dano adicional";
        this.manaCost = 8;
        this.level = 4;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        const result = super.executeSpell(caster, target);
        
        if (result && target) {
            // Chance de efeito caótico da Storm
            const chaosRoll = Math.random();
            if (chaosRoll <= 0.15) { // 15% de chance
                this.triggerChaosEffect(target.position);
            }
        }

        return result;
    }

    /**
     * Dispara efeito caótico da Storm
     */
    private triggerChaosEffect(position: { x: number; y: number; z: number }): void {
        const effects = [
            'chain_lightning',
            'frost_explosion',
            'healing_burst',
            'teleport_random',
            'time_slow'
        ];

        const randomEffect = effects[Math.floor(Math.random() * effects.length)];
        
        console.log(`Efeito caótico da Storm ativado: ${randomEffect}`);
        
        // Emitir evento de efeito caótico
        stormEvents.eventSystem.emit('chaos_effect', {
            effect: randomEffect,
            position,
            source: 'storm_fireball'
        });

        // TODO: Implementar cada efeito caótico
    }
}
