"""
Storm RPG - Mecânicas Básicas
Implementa testes, classes de dificuldade e tipos de ação
"""

from dataclasses import dataclass
from typing import Optional, List, Dict, Any
from enum import Enum
from .dice import roll_d20, DiceResult
from .attributes import AttributeType, AttributeSet


class DifficultyClass(Enum):
    """Classes de Dificuldade padrão"""
    TRIVIAL = 5
    EASY = 10
    MODERATE = 15
    HARD = 20
    EXTREME = 25
    LEGENDARY = 30


class ActionType(Enum):
    """Tipos de ação em combate"""
    STANDARD = "standard"      # Ação padrão
    MOVEMENT = "movement"      # Ação de movimento
    FULL_ROUND = "full_round"  # Ação completa
    FREE = "free"              # Ação livre
    REACTION = "reaction"      # Reação
    SWIFT = "swift"            # Ação rápida


class TestType(Enum):
    """Tipos de teste"""
    ATTRIBUTE = "attribute"    # Teste de atributo
    SKILL = "skill"           # Teste de perícia
    SAVE = "save"             # Teste de resistência
    ATTACK = "attack"         # Teste de ataque
    DAMAGE = "damage"         # Rolagem de dano


@dataclass
class TestResult:
    """Resultado de um teste"""
    dice_result: DiceResult
    total: int
    difficulty: int
    success: bool
    margin: int  # Diferença entre total e dificuldade
    critical_success: bool = False
    critical_failure: bool = False
    
    def __str__(self) -> str:
        status = "SUCESSO" if self.success else "FALHA"
        if self.critical_success:
            status = "SUCESSO CRÍTICO"
        elif self.critical_failure:
            status = "FALHA CRÍTICA"
        
        return f"{self.dice_result} vs CD {self.difficulty} = {status} (margem: {self.margin:+d})"


class Test:
    """Classe para realizar testes do sistema"""
    
    @staticmethod
    def make_test(modifier: int, difficulty: int, 
                  advantage: bool = False, disadvantage: bool = False,
                  auto_success_on_20: bool = True, 
                  auto_fail_on_1: bool = True) -> TestResult:
        """
        Realiza um teste básico d20
        
        Args:
            modifier: Modificador total do teste
            difficulty: Classe de dificuldade
            advantage: Rola com vantagem
            disadvantage: Rola com desvantagem
            auto_success_on_20: 20 natural sempre sucesso
            auto_fail_on_1: 1 natural sempre falha
        """
        dice_result = roll_d20(modifier, advantage, disadvantage)
        total = dice_result.total
        success = total >= difficulty
        margin = total - difficulty
        
        # Verificar sucessos/falhas críticas
        critical_success = False
        critical_failure = False
        
        if dice_result.is_natural_20 and auto_success_on_20:
            success = True
            critical_success = True
        elif dice_result.is_natural_1 and auto_fail_on_1:
            success = False
            critical_failure = True
        
        return TestResult(
            dice_result=dice_result,
            total=total,
            difficulty=difficulty,
            success=success,
            margin=margin,
            critical_success=critical_success,
            critical_failure=critical_failure
        )
    
    @staticmethod
    def attribute_test(attributes: AttributeSet, attr_type: AttributeType,
                      difficulty: int, level_bonus: int = 0,
                      advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Teste de atributo"""
        modifier = attributes.get_modifier(attr_type) + level_bonus
        return Test.make_test(modifier, difficulty, advantage, disadvantage)
    
    @staticmethod
    def skill_test(skill_modifier: int, difficulty: int,
                  advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Teste de perícia"""
        return Test.make_test(skill_modifier, difficulty, advantage, disadvantage)
    
    @staticmethod
    def saving_throw(save_modifier: int, difficulty: int,
                    advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Teste de resistência"""
        return Test.make_test(save_modifier, difficulty, advantage, disadvantage)
    
    @staticmethod
    def attack_roll(attack_bonus: int, armor_class: int,
                   advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Teste de ataque"""
        result = Test.make_test(attack_bonus, armor_class, advantage, disadvantage)
        # Ataques sempre acertam em 20 natural
        if result.dice_result.is_natural_20:
            result.success = True
            result.critical_success = True
        return result


class CombatManager:
    """Gerenciador de combate"""
    
    def __init__(self):
        self.initiative_order: List[Dict[str, Any]] = []
        self.current_turn = 0
        self.round_number = 1
    
    def roll_initiative(self, participants: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Rola iniciativa para todos os participantes
        
        Args:
            participants: Lista de dicionários com 'name', 'dex_modifier', 'level'
        """
        for participant in participants:
            dex_mod = participant.get('dex_modifier', 0)
            level = participant.get('level', 1)
            initiative_roll = roll_d20(dex_mod + level)
            participant['initiative'] = initiative_roll.total
            participant['initiative_roll'] = initiative_roll
        
        # Ordena por iniciativa (maior primeiro)
        self.initiative_order = sorted(participants, 
                                     key=lambda x: x['initiative'], 
                                     reverse=True)
        return self.initiative_order
    
    def next_turn(self) -> Dict[str, Any]:
        """Avança para o próximo turno"""
        if self.current_turn >= len(self.initiative_order) - 1:
            self.current_turn = 0
            self.round_number += 1
        else:
            self.current_turn += 1
        
        return self.get_current_participant()
    
    def get_current_participant(self) -> Dict[str, Any]:
        """Retorna o participante atual"""
        if self.initiative_order:
            return self.initiative_order[self.current_turn]
        return {}
    
    def get_initiative_order(self) -> List[Dict[str, Any]]:
        """Retorna a ordem de iniciativa"""
        return self.initiative_order.copy()


class HealthManager:
    """Gerenciador de pontos de vida"""
    
    def __init__(self, max_hp: int):
        self.max_hp = max_hp
        self.current_hp = max_hp
        self.temporary_hp = 0
        self.damage_taken = 0
    
    def take_damage(self, damage: int) -> int:
        """Aplica dano, retorna dano efetivo"""
        if damage <= 0:
            return 0
        
        # Primeiro remove HP temporário
        if self.temporary_hp > 0:
            temp_damage = min(damage, self.temporary_hp)
            self.temporary_hp -= temp_damage
            damage -= temp_damage
        
        # Depois remove HP normal
        actual_damage = min(damage, self.current_hp)
        self.current_hp -= actual_damage
        self.damage_taken += actual_damage
        
        return actual_damage
    
    def heal(self, healing: int) -> int:
        """Aplica cura, retorna cura efetiva"""
        if healing <= 0:
            return 0
        
        actual_healing = min(healing, self.max_hp - self.current_hp)
        self.current_hp += actual_healing
        self.damage_taken -= actual_healing
        
        return actual_healing
    
    def add_temporary_hp(self, temp_hp: int):
        """Adiciona HP temporário (não acumula)"""
        self.temporary_hp = max(self.temporary_hp, temp_hp)
    
    @property
    def total_hp(self) -> int:
        """HP total atual (normal + temporário)"""
        return self.current_hp + self.temporary_hp
    
    @property
    def is_alive(self) -> bool:
        """Verifica se está vivo"""
        return self.current_hp > 0
    
    @property
    def is_unconscious(self) -> bool:
        """Verifica se está inconsciente"""
        return self.current_hp <= 0
    
    def __str__(self) -> str:
        temp_str = f" (+{self.temporary_hp} temp)" if self.temporary_hp > 0 else ""
        return f"{self.current_hp}/{self.max_hp}{temp_str}"


# Teste de exemplo
if __name__ == "__main__":
    print("=== Testes das Mecânicas Básicas ===")
    
    # Teste básico
    result = Test.make_test(5, 15)
    print(f"Teste básico (+5 vs CD 15): {result}")
    
    # Teste com vantagem
    result = Test.make_test(3, 15, advantage=True)
    print(f"Teste com vantagem (+3 vs CD 15): {result}")
    
    # Teste de combate
    print(f"\n=== Teste de Combate ===")
    combat = CombatManager()
    participants = [
        {"name": "Herói", "dex_modifier": 3, "level": 5},
        {"name": "Goblin", "dex_modifier": 2, "level": 1},
        {"name": "Orc", "dex_modifier": 0, "level": 2}
    ]
    
    initiative = combat.roll_initiative(participants)
    print("Ordem de iniciativa:")
    for p in initiative:
        print(f"  {p['name']}: {p['initiative']} ({p['initiative_roll']})")
    
    # Teste de HP
    print(f"\n=== Teste de HP ===")
    hp = HealthManager(25)
    print(f"HP inicial: {hp}")
    
    damage = hp.take_damage(8)
    print(f"Recebeu 8 de dano (efetivo: {damage}): {hp}")
    
    healing = hp.heal(5)
    print(f"Curou 5 HP (efetivo: {healing}): {hp}")
    
    hp.add_temporary_hp(10)
    print(f"Ganhou 10 HP temporário: {hp}")
