"""
Storm RPG - <PERSON><PERSON>, rápidos e inventivos
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus, create_darkvision
from core.attributes import AttributeType


class Goblin(Race):
    """
    Goblins - <PERSON><PERSON><PERSON><PERSON>, rápidos e inventivos
    
    Os goblins são uma raça pequena mas engenhosa que vive principalmente
    em Zharak, a cidade-estado subterrânea. Apesar de seu tamanho diminuto,
    são conhecidos por sua agilidade, inventividade e capacidade de
    sobrevivência em situações adversas.
    """
    
    def get_name(self) -> str:
        return "Goblin"
    
    def get_description(self) -> str:
        return ("Pequenos e ágeis, os goblins compensam seu tamanho com "
                "engenhosidade e velocidade. Mestres da sobrevivência urbana.")
    
    def get_size(self) -> str:
        return "Pequeno"
    
    def get_base_speed(self) -> int:
        return 9  # Rápidos apesar do tamanho pequeno
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.DEXTERITY, 2, "Agilidade Goblin"),
            create_attribute_bonus(AttributeType.INTELLIGENCE, 1, "Engenhosidade"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Tamanho Pequeno",
                description="Recebe +1 CA e +1 em ataques devido ao tamanho pequeno",
                trait_type=TraitType.SIZE_MODIFIER,
                value={"ac": 1, "attack": 1, "stealth": 4}
            ),
            
            RacialTrait(
                name="Movimento Ágil",
                description="Velocidade aumentada em +3m",
                trait_type=TraitType.MOVEMENT,
                value=("base", 12)  # 9 + 3
            ),
            
            RacialTrait(
                name="Engenhosidade Caótica",
                description="Pode repetir uma falha em teste de Int ou Des 1x por dia",
                trait_type=TraitType.OTHER,
                value="reroll_ability"
            ),
            
            RacialTrait(
                name="Furtividade Natural",
                description="Recebe +4 em testes de Furtividade",
                trait_type=TraitType.SKILL_BONUS,
                value=("Furtividade", 4)
            ),
            
            RacialTrait(
                name="Sobrevivência Urbana",
                description="Recebe +2 em testes de Sobrevivência em ambientes urbanos",
                trait_type=TraitType.SKILL_BONUS,
                value=("Sobrevivência", 2)
            ),
            
            RacialTrait(
                name="Resistência a Medo",
                description="Recebe +2 em testes de resistência contra medo",
                trait_type=TraitType.RESISTANCE,
                value="fear"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Goblin"]
    
    def apply_to_character(self, character):
        """Aplica características goblin ao personagem"""
        super().apply_to_character(character)
        
        # Ajustar tamanho
        from core.character import CharacterSize
        character.size = CharacterSize.SMALL
        
        # Aplicar modificadores de tamanho
        character.armor_class += 1  # +1 CA por ser pequeno


class HobGoblin(Race):
    """
    Hobgoblins - Goblins militarizados e disciplinados
    
    Uma subraça goblin que desenvolveu uma sociedade militar
    rigorosa. São maiores e mais fortes que goblins comuns.
    """
    
    def get_name(self) -> str:
        return "Hobgoblin"
    
    def get_description(self) -> str:
        return ("Goblins militarizados com sociedade disciplinada. "
                "Maiores e mais fortes que goblins comuns.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CONSTITUTION, 2, "Resistência Militar"),
            create_attribute_bonus(AttributeType.INTELLIGENCE, 1, "Táticas de Guerra"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Treinamento Militar",
                description="Proficiência com armaduras leves e escudos",
                trait_type=TraitType.OTHER,
                value="military_training"
            ),
            
            RacialTrait(
                name="Formação de Combate",
                description="Recebe +1 em ataques quando adjacente a aliado",
                trait_type=TraitType.OTHER,
                value="formation_fighting"
            ),
            
            RacialTrait(
                name="Disciplina Marcial",
                description="Recebe +2 em testes de resistência contra medo e charme",
                trait_type=TraitType.RESISTANCE,
                value="fear_charm"
            ),
            
            RacialTrait(
                name="Conhecimento Tático",
                description="Recebe +2 em testes de Conhecimento (Guerra)",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Guerra", 2)
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Goblin", "Militar"]


class BugBear(Race):
    """
    Bugbears - Goblins grandes e furtivos
    
    A maior subraça goblin, conhecida por sua força
    e habilidade de se mover silenciosamente apesar do tamanho.
    """
    
    def get_name(self) -> str:
        return "Bugbear"
    
    def get_description(self) -> str:
        return ("A maior subraça goblin, combinando força com "
                "furtividade surpreendente para seu tamanho.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.STRENGTH, 2, "Força Bugbear"),
            create_attribute_bonus(AttributeType.DEXTERITY, 1, "Furtividade Surpreendente"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Alcance Longo",
                description="Braços longos concedem +1,5m de alcance em ataques corpo a corpo",
                trait_type=TraitType.OTHER,
                value="long_reach"
            ),
            
            RacialTrait(
                name="Furtividade Poderosa",
                description="Pode se mover furtivamente mesmo sendo grande",
                trait_type=TraitType.SKILL_BONUS,
                value=("Furtividade", 2)
            ),
            
            RacialTrait(
                name="Ataque Surpresa",
                description="Causa +2d6 de dano em ataques surpresa",
                trait_type=TraitType.OTHER,
                value="surprise_attack"
            ),
            
            RacialTrait(
                name="Força Bruta",
                description="Recebe +2 em testes de Força para quebrar objetos",
                trait_type=TraitType.SKILL_BONUS,
                value=("Atletismo", 2)
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Goblin"]


class GoblinTinker(Goblin):
    """
    Goblin Inventor - Especializado em engenhocas
    
    Uma variante de goblin focada em invenções e
    dispositivos mecânicos improvisados.
    """
    
    def get_name(self) -> str:
        return "Goblin Inventor"
    
    def get_description(self) -> str:
        return ("Goblins especializados em criar engenhocas e "
                "dispositivos mecânicos improvisados.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui engenhosidade caótica por habilidades de inventor
        traits[5] = RacialTrait(
            name="Inventor Nato",
            description="Pode criar dispositivos temporários com sucata",
            trait_type=TraitType.OTHER,
            value="tinker_ability"
        )
        
        # Adiciona conhecimento de engenharia
        traits.append(
            RacialTrait(
                name="Conhecimento Mecânico",
                description="Recebe +4 em testes relacionados a dispositivos mecânicos",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Engenharia", 4)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Goblin", "Técnico"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste das Raças Goblin ===")
    
    goblin = Goblin()
    print(goblin.get_summary())
    
    print(f"\n" + "="*50)
    
    hobgoblin = HobGoblin()
    print(hobgoblin.get_summary())
    
    print(f"\n" + "="*50)
    
    bugbear = BugBear()
    print(bugbear.get_summary())
    
    print(f"\n" + "="*50)
    
    tinker = GoblinTinker()
    print(tinker.get_summary())
