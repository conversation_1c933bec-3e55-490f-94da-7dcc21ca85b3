# 🔧 Storm RPG - API Reference

> Complete technical documentation for developers and creators

## 📋 Table of Contents

1. [Core Systems](#core-systems)
2. [Player Management](#player-management)
3. [Combat System](#combat-system)
4. [Spell System](#spell-system)
5. [Item System](#item-system)
6. [UI System](#ui-system)
7. [Event System](#event-system)
8. [Utilities](#utilities)

---

## 🎯 Core Systems

### GameManager

Main controller that coordinates all game systems.

```typescript
class GameManager {
    constructor()
    getGameState(): GameState
    getRemainingTime(): number
    forceStartGame(): void
    stopGame(): void
}
```

**Usage Example:**
```typescript
const gameManager = new GameManager();
if (gameManager.getGameState() === GameState.IN_PROGRESS) {
    console.log(`Time remaining: ${gameManager.getRemainingTime()}ms`);
}
```

### Constants

Global configuration constants for easy customization.

```typescript
export const GAME_CONFIG = {
    MAX_PLAYERS: 4,
    RESPAWN_TIME: 5000,
    GAME_DURATION: 1800000,
    XP_PER_ENEMY_KILL: 100,
    XP_PER_GEM_COLLECTED: 10
}

export const CLASS_CONFIGS = {
    WARRIOR: { hitDie: 10, baseAttributes: {...} },
    MAGE: { hitDie: 6, baseAttributes: {...} },
    ROGUE: { hitDie: 8, baseAttributes: {...} }
}
```

---

## 👥 Player Management

### PlayerManager

Handles player lifecycle, spawning, and statistics.

```typescript
class PlayerManager {
    constructor()
    
    // Player lifecycle
    getPlayerData(playerId: string): PlayerData | undefined
    getPlayerStats(playerId: string): PlayerStats | undefined
    getAllPlayers(): PlayerData[]
    getAlivePlayers(): PlayerData[]
    
    // Game actions
    selectPlayerClass(playerId: string, playerClass: PlayerClass): void
    spawnPlayer(playerId: string): void
    onPlayerDied(playerId: string, killerId?: string): void
    
    // Progression
    addExperience(playerId: string, xp: number): boolean
    addScore(playerId: string, points: number): void
    onGemCollected(playerId: string, gemValue: number): void
    
    // Combat
    damagePlayer(playerId: string, damage: number, attackerId?: string): boolean
    healPlayer(playerId: string, healing: number): number
}
```

### PlayerStats

Character statistics and progression.

```typescript
class PlayerStats {
    attributes: Attributes
    derivedStats: DerivedStats
    level: number
    experience: number
    playerClass: PlayerClass
    
    // Attribute methods
    getAttributeModifier(attribute: keyof Attributes): number
    getPrimaryAttributeModifier(): number
    
    // Progression
    addExperience(xp: number): boolean
    updateDerivedStats(): void
    
    // Health/Mana
    takeDamage(damage: number): number
    heal(healing: number): number
    spendMana(cost: number): boolean
    restoreMana(amount: number): number
    
    // Utility
    isAlive(): boolean
    canCastSpells(): boolean
    getSummary(): string
}
```

---

## ⚔️ Combat System

### CombatSystem

Handles all combat mechanics with d20 simulation.

```typescript
class CombatSystem {
    constructor(playerManager: PlayerManager)
    
    // Combat actions
    performMeleeAttack(attackerId: string, targetId: string, weaponDamage?: string): CombatResult | null
    performRangedAttack(attackerId: string, targetId: string, weaponDamage?: string, range?: number): CombatResult | null
    
    // Entity management
    registerCombatEntity(entity: CombatEntity): void
    unregisterCombatEntity(entityId: string): void
    
    // Combat state
    startCombat(entityId1: string, entityId2: string): void
    endCombat(entityId1: string, entityId2: string): void
    areInCombat(entityId1: string, entityId2: string): boolean
    
    // Utility
    findNearbyEnemies(entityId: string, radius?: number): CombatEntity[]
    applyHealing(targetId: string, healing: number, healerId?: string): number
}
```

### CombatResult

Result of a combat action.

```typescript
interface CombatResult {
    hit: boolean
    damage: number
    isCritical: boolean
    diceResult: DiceResult
    target: string
    attacker: string
}
```

---

## ✨ Spell System

### BaseSpell

Abstract base class for all spells.

```typescript
abstract class BaseSpell {
    name: string
    manaCost: number
    castTime: number
    cooldown: number
    range: number
    level: number
    
    canCast(caster: PlayerStats, target?: SpellTarget): boolean
    cast(caster: PlayerStats, target?: SpellTarget): Promise<SpellEffect | null>
    
    protected abstract executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null
    protected createVisualEffect(position: Vector3, particleAssetId: string, duration?: number): any
    protected playSoundEffect(soundAssetId: string, position?: Vector3): void
}
```

### SpellManager

Global spell management system.

```typescript
class SpellManager {
    registerSpell(spellId: string, spell: BaseSpell): void
    getSpell(spellId: string): BaseSpell | undefined
    
    // Cooldown management
    isOnCooldown(casterId: string, spellId: string): boolean
    startCooldown(casterId: string, spellId: string, duration: number): void
    getRemainingCooldown(casterId: string, spellId: string): number
    
    // Casting
    castSpell(casterId: string, spellId: string, caster: PlayerStats, target?: SpellTarget): Promise<SpellEffect | null>
}
```

### Included Spells

#### Fireball
```typescript
class Fireball extends BaseSpell {
    constructor() // 6 mana, 30m range, area damage
    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null
}
```

#### Heal
```typescript
class Heal extends BaseSpell {
    constructor() // 4 mana, 9m range, healing
    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null
}
```

#### StormBolt
```typescript
class StormBolt extends BaseSpell {
    constructor() // 3 mana, 24m range, lightning damage
    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null
}
```

---

## 💎 Item System

### ItemPickup

Manages item creation, collection, and effects.

```typescript
class ItemPickup {
    constructor(playerManager: PlayerManager)
    
    // Item management
    createItem(itemType: ItemType, position: Vector3, customData?: Partial<ItemData>): string
    collectItem(playerId: string, itemId: string): void
    removeItem(itemId: string): void
    
    // Loot system
    createLootDrop(position: Vector3, lootTable: Array<{item: ItemType; chance: number}>): void
    
    // Utility
    getActiveItems(): Map<string, ItemData>
    clearAllItems(): void
}
```

### ItemData

Item configuration and properties.

```typescript
interface ItemData {
    type: ItemType
    name: string
    value: number
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary'
    effect: string
    description?: string
}
```

---

## 🖥️ UI System

### HUDManager

Dynamic user interface management.

```typescript
class HUDManager {
    constructor(playerManager: PlayerManager)
    
    // HUD lifecycle
    createPlayerHUD(playerId: string): void
    removePlayerHUD(playerId: string): void
    
    // HUD updates
    updateHealthBar(playerId: string): void
    updateManaBar(playerId: string): void
    updateLevelDisplay(playerId: string): void
    updateScoreDisplay(playerId: string): void
    updateAllHUDElements(playerId: string): void
    
    // Notifications
    showNotification(playerId: string, message: string, duration?: number): void
    showDamageNumber(playerId: string, damage: number): void
    showHealingNumber(playerId: string, healing: number): void
    showLevelUpEffect(playerId: string): void
}
```

---

## 📡 Event System

### EventSystem

Global event communication system.

```typescript
class EventSystem {
    static getInstance(): EventSystem
    
    // Event management
    on(eventType: string, callback: EventCallback): void
    off(eventType: string, callback: EventCallback): void
    once(eventType: string, callback: EventCallback): void
    emit(eventType: string, data?: any, source?: string): void
    
    // Utility
    hasListeners(eventType: string): boolean
    getEventTypes(): string[]
    getHistory(eventType?: string): EventData[]
    clearHistory(): void
}
```

### Storm Events

Pre-defined event constants and typed emitters.

```typescript
export const STORM_EVENTS = {
    // Player events
    PLAYER_JOINED: 'player_joined',
    PLAYER_DIED: 'player_died',
    PLAYER_LEVEL_UP: 'player_level_up',
    
    // Combat events
    DAMAGE_DEALT: 'damage_dealt',
    CRITICAL_HIT: 'critical_hit',
    HEALING_APPLIED: 'healing_applied',
    
    // Spell events
    SPELL_CAST: 'spell_cast',
    MANA_SPENT: 'mana_spent',
    
    // Item events
    ITEM_PICKED_UP: 'item_picked_up',
    GEM_COLLECTED: 'gem_collected'
}

class StormEventEmitter {
    onPlayerJoined(callback: (playerId: string, playerName: string) => void): void
    emitPlayerJoined(playerId: string, playerName: string): void
    
    onDamageDealt(callback: (attacker: string, target: string, damage: number) => void): void
    emitDamageDealt(attacker: string, target: string, damage: number): void
    
    // ... more typed event methods
}
```

---

## 🎲 Utilities

### DiceRoller

d20 system simulation with advantage/disadvantage.

```typescript
class DiceRoller {
    // Basic rolls
    static rollD20(): number
    static rollDie(sides: number): number
    static rollMultiple(numDice: number, sides: number): number[]
    static rollWithModifier(numDice: number, sides: number, modifier?: number): number
    
    // d20 system
    static rollD20Check(modifier?: number, advantage?: boolean, disadvantage?: boolean): DiceResult
    static checkSuccess(modifier: number, difficultyClass: number, advantage?: boolean, disadvantage?: boolean): {success: boolean; result: DiceResult}
    
    // Combat rolls
    static rollAttack(attackBonus: number, targetAC: number, advantage?: boolean, disadvantage?: boolean): {hit: boolean; result: DiceResult}
    static rollDamage(damageString: string): number
    static rollSavingThrow(saveBonus: number, difficultyClass: number, advantage?: boolean, disadvantage?: boolean): {success: boolean; result: DiceResult}
    
    // Utility
    static rollPercentile(): number
    static checkPercentage(chance: number): boolean
    static rollAttribute(): number
    static generateAttributeSet(): Attributes
}
```

### DiceResult

Result of a d20 roll with metadata.

```typescript
interface DiceResult {
    roll: number
    modifier: number
    total: number
    isCriticalHit: boolean
    isCriticalFail: boolean
    advantage: boolean
    disadvantage: boolean
}
```

---

## 🔧 Extension Examples

### Adding a New Spell

```typescript
import { BaseSpell, SpellTarget, SpellEffect } from './BaseSpell';

class IceShard extends BaseSpell {
    constructor() {
        super("Ice Shard", 4, 1000, 4000, 25, 2);
    }
    
    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target) return null;
        
        const damage = this.calculateSpellDamage(caster, "2d6");
        
        // Create ice effect
        this.createVisualEffect(target.position, "ice_shard_particle", 2000);
        this.playSoundEffect("ice_crack_sound", target.position);
        
        // Apply damage and slow effect
        return { damage, statusEffect: 'slowed' };
    }
}

// Register the spell
spellManager.registerSpell('ice_shard', new IceShard());
```

### Adding a New Class

```typescript
import { BaseCharacterClass, ClassAbility } from './BaseClass';

class Paladin extends BaseCharacterClass {
    constructor() {
        super("Paladin", "Holy warrior with healing and protection abilities");
    }
    
    protected initializeAbilities(): void {
        this.addAbility(1, {
            name: "Lay on Hands",
            description: "Heal an ally with divine power",
            level: 1,
            cooldown: 10000,
            manaCost: 3,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                // Healing implementation
            }
        });
    }
}
```

---

## 📝 Best Practices

### Performance
- Use object pooling for frequently created/destroyed objects
- Limit particle effects duration
- Cache frequently accessed components

### Code Organization
- Keep scripts modular and focused
- Use TypeScript interfaces for type safety
- Document public methods and complex logic

### Remixability
- Use descriptive entity names
- Expose configuration through constants
- Provide clear extension points
- Include usage examples in comments

---

*This API reference is part of the Storm RPG open source project for Meta Horizon Worlds.*
