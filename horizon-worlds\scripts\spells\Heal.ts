/**
 * Storm RPG - Heal Spell
 * Magia de Cura com efeitos visuais restaurativos
 */

import { BaseSpell, SpellTarget, SpellEffect } from './BaseSpell';
import { PlayerStats } from '../core/AttributeSystem';
import { SPELL_CONFIGS, ASSET_IDS } from '../utils/Constants';
import { stormEvents } from '../utils/EventSystem';

export class Heal extends BaseSpell {
    constructor() {
        const config = SPELL_CONFIGS.HEAL;
        super(
            config.name,
            "Restaura pontos de vida do alvo com energia divina",
            config.manaCost,
            config.castTime,
            config.cooldown,
            config.range,
            2 // Nível da magia
        );
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target) {
            // Se não há alvo específico, cura o próprio conjurador
            return this.healSelf(caster);
        }

        return this.healTarget(caster, target);
    }

    /**
     * Cura o próprio conjurador
     */
    private healSelf(caster: PlayerStats): SpellEffect {
        const healing = this.calculateSpellHealing(caster, SPELL_CONFIGS.HEAL.healing);
        const actualHealing = caster.heal(healing);

        // Criar efeito visual no conjurador
        // TODO: Obter posição real do conjurador
        const casterPosition = { x: 0, y: 1, z: 0 };
        this.createHealingEffect(casterPosition);

        console.log(`${caster.classConfig.name} se curou em ${actualHealing} HP`);

        // Emitir evento de cura
        stormEvents.eventSystem.emit('healing_applied', {
            targetId: 'self',
            healing: actualHealing,
            healerId: caster.playerClass
        });

        return {
            healing: actualHealing
        };
    }

    /**
     * Cura um alvo específico
     */
    private healTarget(caster: PlayerStats, target: SpellTarget): SpellEffect | null {
        if (!target.position) {
            console.error("Heal requer posição do alvo");
            return null;
        }

        const healing = this.calculateSpellHealing(caster, SPELL_CONFIGS.HEAL.healing);

        // Criar efeito visual no alvo
        this.createHealingEffect(target.position);

        // Tentar aplicar cura ao alvo
        let actualHealing = 0;
        
        if (target.entity) {
            actualHealing = this.applyHealingToEntity(target.entity, healing);
        }

        console.log(`Heal curou ${actualHealing} HP no alvo`);

        // Emitir evento de cura
        stormEvents.eventSystem.emit('healing_applied', {
            targetId: target.entityId || 'unknown',
            healing: actualHealing,
            healerId: caster.playerClass
        });

        return {
            healing: actualHealing
        };
    }

    /**
     * Cria efeito visual de cura
     */
    private createHealingEffect(position: { x: number; y: number; z: number }): void {
        // Efeito de partículas de cura
        this.createVisualEffect(
            position,
            ASSET_IDS.PARTICLES.HEALING_LIGHT,
            2500
        );

        // Som de cura
        this.playSoundEffect(ASSET_IDS.SOUNDS.HEAL_CAST, position);

        // Efeito adicional: luz suave
        this.createHealingLight(position);
    }

    /**
     * Cria luz suave de cura
     */
    private createHealingLight(position: { x: number; y: number; z: number }): void {
        try {
            const lightEntity = Scene.createRootEntity();
            lightEntity.setPosition(position.x, position.y + 1, position.z);

            // Criar componente de luz
            const lightComponent = lightEntity.createComponent("engine:light", {
                type: "point",
                color: { r: 0.8, g: 1.0, b: 0.8 }, // Verde suave
                intensity: 2.0,
                range: 5.0
            });

            // Animar intensidade da luz
            this.animateHealingLight(lightComponent, lightEntity);

        } catch (error) {
            console.error("Erro ao criar luz de cura:", error);
        }
    }

    /**
     * Anima a luz de cura
     */
    private animateHealingLight(lightComponent: any, lightEntity: any): void {
        const duration = 2000;
        const startTime = Date.now();
        const maxIntensity = 2.0;

        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = elapsed / duration;

            if (progress <= 1) {
                // Fade in e fade out
                let intensity;
                if (progress <= 0.5) {
                    intensity = maxIntensity * (progress * 2);
                } else {
                    intensity = maxIntensity * (2 - progress * 2);
                }

                lightComponent.intensity = intensity;
                requestAnimationFrame(animate);
            } else {
                // Remover luz
                lightEntity.destroy();
            }
        };

        animate();
    }

    /**
     * Aplica cura a uma entidade
     */
    private applyHealingToEntity(entity: any, healing: number): number {
        try {
            const healthComponent = entity.getComponent("game:health");
            if (healthComponent) {
                const currentHealth = healthComponent.currentHealth;
                const maxHealth = healthComponent.maxHealth || 100;
                
                const actualHealing = Math.min(healing, maxHealth - currentHealth);
                healthComponent.currentHealth += actualHealing;

                return actualHealing;
            }
        } catch (error) {
            console.error("Erro ao aplicar cura:", error);
        }

        return 0;
    }

    /**
     * Sobrescreve a animação de conjuração
     */
    protected startCastingAnimation(targetPosition: { x: number; y: number; z: number }): void {
        console.log(`Conjurando Cura...`);
        
        // TODO: Adicionar efeitos visuais de conjuração
        // Por exemplo: brilho dourado ao redor das mãos
    }
}

// Cura em Massa
export class MassHeal extends Heal {
    private healRadius: number = 6;

    constructor() {
        super();
        this.name = "Cura em Massa";
        this.description = "Cura todos os aliados em uma área";
        this.manaCost = 12;
        this.level = 5;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        const centerPosition = target?.position || { x: 0, y: 0, z: 0 };
        
        // Encontrar todas as entidades na área
        const entitiesInArea = this.findEntitiesInArea(centerPosition, this.healRadius);
        
        const healing = this.calculateSpellHealing(caster, "3d8+3");
        let totalHealing = 0;

        // Criar efeito visual central
        this.createMassHealEffect(centerPosition);

        // Curar cada entidade
        entitiesInArea.forEach(entity => {
            const actualHealing = this.applyHealingToEntity(entity, healing);
            totalHealing += actualHealing;
        });

        // Curar o próprio conjurador se estiver na área
        const distanceToCaster = 0; // TODO: Calcular distância real
        if (distanceToCaster <= this.healRadius) {
            totalHealing += caster.heal(healing);
        }

        console.log(`Mass Heal curou ${entitiesInArea.length + 1} entidades por um total de ${totalHealing} HP`);

        return {
            healing: totalHealing,
            areaOfEffect: this.healRadius
        };
    }

    /**
     * Cria efeito visual de cura em massa
     */
    private createMassHealEffect(position: { x: number; y: number; z: number }): void {
        // Efeito central mais intenso
        this.createVisualEffect(
            position,
            ASSET_IDS.PARTICLES.HEALING_LIGHT,
            3000
        );

        // Ondas de cura expandindo
        this.createHealingWaves(position);
    }

    /**
     * Cria ondas de cura expandindo
     */
    private createHealingWaves(center: { x: number; y: number; z: number }): void {
        const waveCount = 3;
        const waveDelay = 500;

        for (let i = 0; i < waveCount; i++) {
            setTimeout(() => {
                this.createHealingWave(center, (i + 1) * 2);
            }, i * waveDelay);
        }
    }

    /**
     * Cria uma onda de cura individual
     */
    private createHealingWave(center: { x: number; y: number; z: number }, radius: number): void {
        try {
            const waveEntity = Scene.createRootEntity();
            waveEntity.setPosition(center.x, center.y, center.z);

            // TODO: Criar efeito de onda expandindo
            // Por enquanto, apenas log
            console.log(`Onda de cura com raio ${radius}m`);

            // Remover após animação
            setTimeout(() => {
                waveEntity.destroy();
            }, 1000);

        } catch (error) {
            console.error("Erro ao criar onda de cura:", error);
        }
    }
}

// Cura Menor (versão básica)
export class MinorHeal extends Heal {
    constructor() {
        super();
        this.name = "Cura Menor";
        this.description = "Versão básica da cura com menor custo de mana";
        this.manaCost = 2;
        this.level = 1;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        // Usar fórmula de cura menor
        const healing = this.calculateSpellHealing(caster, "1d8+1");
        
        if (!target) {
            const actualHealing = caster.heal(healing);
            return { healing: actualHealing };
        }

        return this.healTarget(caster, target);
    }
}

// Regeneração (cura ao longo do tempo)
export class Regeneration extends Heal {
    constructor() {
        super();
        this.name = "Regeneração";
        this.description = "Cura o alvo gradualmente ao longo do tempo";
        this.manaCost = 6;
        this.level = 3;
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        const position = target?.position || { x: 0, y: 0, z: 0 };
        
        // Criar efeito visual inicial
        this.createHealingEffect(position);

        // Iniciar regeneração ao longo do tempo
        this.startRegeneration(caster, target);

        return {
            healing: 0, // Cura inicial zero
            duration: 30000 // 30 segundos
        };
    }

    /**
     * Inicia processo de regeneração
     */
    private startRegeneration(caster: PlayerStats, target?: SpellTarget): void {
        const healingPerTick = Math.floor(this.calculateSpellHealing(caster, "1d4+1"));
        const tickInterval = 3000; // 3 segundos
        const duration = 30000; // 30 segundos
        const totalTicks = duration / tickInterval;

        let tickCount = 0;

        const regenInterval = setInterval(() => {
            tickCount++;

            // Aplicar cura
            let actualHealing = 0;
            if (target && target.entity) {
                actualHealing = this.applyHealingToEntity(target.entity, healingPerTick);
            } else {
                actualHealing = caster.heal(healingPerTick);
            }

            // Efeito visual menor a cada tick
            const position = target?.position || { x: 0, y: 0, z: 0 };
            this.createVisualEffect(position, ASSET_IDS.PARTICLES.HEALING_LIGHT, 1000);

            console.log(`Regeneração: +${actualHealing} HP (tick ${tickCount}/${totalTicks})`);

            // Parar após duração completa
            if (tickCount >= totalTicks) {
                clearInterval(regenInterval);
                console.log("Regeneração concluída");
            }
        }, tickInterval);
    }
}
