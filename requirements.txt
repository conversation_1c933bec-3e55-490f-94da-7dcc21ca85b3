# Storm RPG - Dependências Python

# Core dependencies
dataclasses-json>=0.6.0
pydantic>=2.0.0
typing-extensions>=4.0.0

# Web interface
flask>=2.3.0
flask-cors>=4.0.0
jinja2>=3.1.0

# Data handling
pyyaml>=6.0
json5>=0.9.0

# Math and random
numpy>=1.24.0
random2>=1.0.0

# Development tools
pytest>=7.0.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.0.0

# Documentation
markdown>=3.4.0
mkdocs>=1.5.0
mkdocs-material>=9.0.0

# Optional: For advanced features
# pillow>=10.0.0  # For character portraits
# reportlab>=4.0.0  # For PDF character sheets
