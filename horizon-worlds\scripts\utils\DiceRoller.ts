/**
 * Storm RPG - Dice Roller System
 * Sistema de rolagem de dados d20 para Meta Horizon Worlds
 */

import { GAME_CONFIG } from './Constants';

export interface DiceResult {
    roll: number;
    modifier: number;
    total: number;
    isCriticalHit: boolean;
    isCriticalFail: boolean;
    advantage: boolean;
    disadvantage: boolean;
}

export class DiceRoller {
    /**
     * Rola um d20 básico
     */
    static rollD20(): number {
        return Math.floor(Math.random() * 20) + 1;
    }

    /**
     * Rola qualquer tipo de dado
     */
    static rollDie(sides: number): number {
        return Math.floor(Math.random() * sides) + 1;
    }

    /**
     * Rola múltiplos dados
     */
    static rollMultiple(numDice: number, sides: number): number[] {
        const results: number[] = [];
        for (let i = 0; i < numDice; i++) {
            results.push(this.rollDie(sides));
        }
        return results;
    }

    /**
     * Rola dados com modificador (ex: 2d6+3)
     */
    static rollWithModifier(numDice: number, sides: number, modifier: number = 0): number {
        const rolls = this.rollMultiple(numDice, sides);
        const total = rolls.reduce((sum, roll) => sum + roll, 0);
        return total + modifier;
    }

    /**
     * Rola d20 com modificador e vantagem/desvantagem
     */
    static rollD20Check(
        modifier: number = 0, 
        advantage: boolean = false, 
        disadvantage: boolean = false
    ): DiceResult {
        let roll: number;
        
        if (advantage && !disadvantage) {
            // Vantagem: rola 2d20, pega o maior
            const roll1 = this.rollD20();
            const roll2 = this.rollD20();
            roll = Math.max(roll1, roll2);
        } else if (disadvantage && !advantage) {
            // Desvantagem: rola 2d20, pega o menor
            const roll1 = this.rollD20();
            const roll2 = this.rollD20();
            roll = Math.min(roll1, roll2);
        } else {
            // Normal: rola 1d20
            roll = this.rollD20();
        }

        const total = roll + modifier;
        const isCriticalHit = roll === GAME_CONFIG.CRITICAL_HIT_THRESHOLD;
        const isCriticalFail = roll === GAME_CONFIG.CRITICAL_FAIL_THRESHOLD;

        return {
            roll,
            modifier,
            total,
            isCriticalHit,
            isCriticalFail,
            advantage,
            disadvantage
        };
    }

    /**
     * Verifica se um teste foi bem-sucedido
     */
    static checkSuccess(
        modifier: number, 
        difficultyClass: number, 
        advantage: boolean = false, 
        disadvantage: boolean = false
    ): { success: boolean; result: DiceResult } {
        const result = this.rollD20Check(modifier, advantage, disadvantage);
        
        // Crítico sempre sucede (exceto se for crítico falha também)
        let success = result.isCriticalHit && !result.isCriticalFail;
        
        // Crítico falha sempre falha
        if (result.isCriticalFail) {
            success = false;
        } else if (!result.isCriticalHit) {
            // Teste normal
            success = result.total >= difficultyClass;
        }

        return { success, result };
    }

    /**
     * Rola ataque vs. Classe de Armadura
     */
    static rollAttack(
        attackBonus: number, 
        targetAC: number, 
        advantage: boolean = false, 
        disadvantage: boolean = false
    ): { hit: boolean; result: DiceResult } {
        const { success: hit, result } = this.checkSuccess(attackBonus, targetAC, advantage, disadvantage);
        return { hit, result };
    }

    /**
     * Rola dano com base em uma string (ex: "2d6+3")
     */
    static rollDamage(damageString: string): number {
        // Parse da string de dano (formato: "XdY+Z" ou "XdY-Z" ou "XdY")
        const regex = /(\d+)d(\d+)([+-]\d+)?/;
        const match = damageString.match(regex);
        
        if (!match) {
            console.error(`Formato de dano inválido: ${damageString}`);
            return 0;
        }

        const numDice = parseInt(match[1]);
        const sides = parseInt(match[2]);
        const modifier = match[3] ? parseInt(match[3]) : 0;

        return this.rollWithModifier(numDice, sides, modifier);
    }

    /**
     * Rola teste de resistência
     */
    static rollSavingThrow(
        saveBonus: number, 
        difficultyClass: number, 
        advantage: boolean = false, 
        disadvantage: boolean = false
    ): { success: boolean; result: DiceResult } {
        return this.checkSuccess(saveBonus, difficultyClass, advantage, disadvantage);
    }

    /**
     * Rola iniciativa
     */
    static rollInitiative(dexterityModifier: number, level: number = 1): DiceResult {
        const modifier = dexterityModifier + Math.floor(level / 2);
        return this.rollD20Check(modifier);
    }

    /**
     * Rola percentual (d100)
     */
    static rollPercentile(): number {
        return Math.floor(Math.random() * 100) + 1;
    }

    /**
     * Verifica chance percentual
     */
    static checkPercentage(chance: number): boolean {
        return this.rollPercentile() <= chance;
    }

    /**
     * Rola tabela de loot baseada em probabilidades
     */
    static rollLootTable(lootTable: { item: string; chance: number }[]): string | null {
        const roll = this.rollPercentile();
        let cumulativeChance = 0;

        for (const entry of lootTable) {
            cumulativeChance += entry.chance;
            if (roll <= cumulativeChance) {
                return entry.item;
            }
        }

        return null; // Nenhum item
    }

    /**
     * Simula rolagem de atributos (4d6, descarta menor)
     */
    static rollAttribute(): number {
        const rolls = this.rollMultiple(4, 6);
        rolls.sort((a, b) => b - a); // Ordena decrescente
        return rolls.slice(0, 3).reduce((sum, roll) => sum + roll, 0); // Soma os 3 maiores
    }

    /**
     * Gera conjunto completo de atributos
     */
    static generateAttributeSet(): {
        strength: number;
        dexterity: number;
        constitution: number;
        intelligence: number;
        wisdom: number;
        charisma: number;
    } {
        return {
            strength: this.rollAttribute(),
            dexterity: this.rollAttribute(),
            constitution: this.rollAttribute(),
            intelligence: this.rollAttribute(),
            wisdom: this.rollAttribute(),
            charisma: this.rollAttribute()
        };
    }

    /**
     * Utilitário para logging de resultados
     */
    static logResult(result: DiceResult, testName: string = "Teste"): void {
        let message = `${testName}: ${result.roll}`;
        
        if (result.modifier !== 0) {
            const sign = result.modifier >= 0 ? '+' : '';
            message += `${sign}${result.modifier}`;
        }
        
        message += ` = ${result.total}`;
        
        if (result.isCriticalHit) {
            message += " (CRÍTICO!)";
        } else if (result.isCriticalFail) {
            message += " (FALHA CRÍTICA!)";
        }
        
        if (result.advantage) {
            message += " [Vantagem]";
        } else if (result.disadvantage) {
            message += " [Desvantagem]";
        }

        console.log(message);
    }
}

// Classe auxiliar para testes específicos do Storm RPG
export class StormDiceRoller extends DiceRoller {
    /**
     * Rola teste de conjuração
     */
    static rollSpellcast(
        casterLevel: number, 
        spellLevel: number, 
        attribute: number
    ): { success: boolean; result: DiceResult } {
        const modifier = Math.floor((attribute - 10) / 2) + casterLevel;
        const dc = 10 + spellLevel;
        return this.checkSuccess(modifier, dc);
    }

    /**
     * Rola resistência mágica
     */
    static rollSpellResistance(
        casterLevel: number, 
        targetSR: number
    ): { success: boolean; result: DiceResult } {
        const result = this.rollD20Check(casterLevel);
        const success = result.total >= targetSR;
        return { success, result };
    }

    /**
     * Rola dano da Storm (com chance de efeito caótico)
     */
    static rollStormDamage(baseDamage: string): {
        damage: number;
        chaosEffect: boolean;
        effectType?: string;
    } {
        const damage = this.rollDamage(baseDamage);
        const chaosRoll = this.rollPercentile();
        const chaosEffect = chaosRoll <= 10; // 10% de chance

        let effectType: string | undefined;
        if (chaosEffect) {
            const effectRoll = this.rollDie(6);
            switch (effectRoll) {
                case 1: effectType = "stun"; break;
                case 2: effectType = "blind"; break;
                case 3: effectType = "teleport"; break;
                case 4: effectType = "heal"; break;
                case 5: effectType = "double_damage"; break;
                case 6: effectType = "chain_lightning"; break;
            }
        }

        return { damage, chaosEffect, effectType };
    }
}
