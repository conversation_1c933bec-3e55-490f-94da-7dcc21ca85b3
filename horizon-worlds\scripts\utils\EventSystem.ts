/**
 * Storm RPG - Event System
 * Sistema de eventos para comunicação entre componentes
 */

export type EventCallback = (...args: any[]) => void;

export interface EventData {
    type: string;
    data?: any;
    timestamp: number;
    source?: string;
}

export class EventSystem {
    private static instance: EventSystem;
    private listeners: Map<string, EventCallback[]> = new Map();
    private eventHistory: EventData[] = [];
    private maxHistorySize: number = 100;

    private constructor() {}

    /**
     * Singleton pattern
     */
    static getInstance(): EventSystem {
        if (!EventSystem.instance) {
            EventSystem.instance = new EventSystem();
        }
        return EventSystem.instance;
    }

    /**
     * Registra um listener para um evento
     */
    on(eventType: string, callback: EventCallback): void {
        if (!this.listeners.has(eventType)) {
            this.listeners.set(eventType, []);
        }
        this.listeners.get(eventType)!.push(callback);
    }

    /**
     * Remove um listener específico
     */
    off(eventType: string, callback: EventCallback): void {
        const callbacks = this.listeners.get(eventType);
        if (callbacks) {
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    /**
     * Remove todos os listeners de um evento
     */
    removeAllListeners(eventType: string): void {
        this.listeners.delete(eventType);
    }

    /**
     * Emite um evento
     */
    emit(eventType: string, data?: any, source?: string): void {
        const eventData: EventData = {
            type: eventType,
            data,
            timestamp: Date.now(),
            source
        };

        // Adicionar ao histórico
        this.addToHistory(eventData);

        // Chamar todos os listeners
        const callbacks = this.listeners.get(eventType);
        if (callbacks) {
            callbacks.forEach(callback => {
                try {
                    callback(data, eventData);
                } catch (error) {
                    console.error(`Erro ao executar callback para evento ${eventType}:`, error);
                }
            });
        }
    }

    /**
     * Registra um listener que só executa uma vez
     */
    once(eventType: string, callback: EventCallback): void {
        const onceCallback = (...args: any[]) => {
            callback(...args);
            this.off(eventType, onceCallback);
        };
        this.on(eventType, onceCallback);
    }

    /**
     * Adiciona evento ao histórico
     */
    private addToHistory(eventData: EventData): void {
        this.eventHistory.push(eventData);
        
        // Manter apenas os últimos eventos
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }

    /**
     * Obtém histórico de eventos
     */
    getHistory(eventType?: string): EventData[] {
        if (eventType) {
            return this.eventHistory.filter(event => event.type === eventType);
        }
        return [...this.eventHistory];
    }

    /**
     * Limpa o histórico
     */
    clearHistory(): void {
        this.eventHistory = [];
    }

    /**
     * Verifica se há listeners para um evento
     */
    hasListeners(eventType: string): boolean {
        const callbacks = this.listeners.get(eventType);
        return callbacks ? callbacks.length > 0 : false;
    }

    /**
     * Lista todos os tipos de eventos com listeners
     */
    getEventTypes(): string[] {
        return Array.from(this.listeners.keys());
    }
}

// Eventos específicos do Storm RPG
export const STORM_EVENTS = {
    // Eventos de jogador
    PLAYER_JOINED: 'player_joined',
    PLAYER_LEFT: 'player_left',
    PLAYER_SPAWNED: 'player_spawned',
    PLAYER_DIED: 'player_died',
    PLAYER_RESPAWNED: 'player_respawned',
    PLAYER_LEVEL_UP: 'player_level_up',
    PLAYER_CLASS_SELECTED: 'player_class_selected',

    // Eventos de combate
    COMBAT_STARTED: 'combat_started',
    COMBAT_ENDED: 'combat_ended',
    ATTACK_MADE: 'attack_made',
    DAMAGE_DEALT: 'damage_dealt',
    HEALING_APPLIED: 'healing_applied',
    CRITICAL_HIT: 'critical_hit',
    CRITICAL_MISS: 'critical_miss',

    // Eventos de magia
    SPELL_CAST: 'spell_cast',
    SPELL_HIT: 'spell_hit',
    SPELL_MISSED: 'spell_missed',
    MANA_SPENT: 'mana_spent',
    MANA_RESTORED: 'mana_restored',

    // Eventos de itens
    ITEM_PICKED_UP: 'item_picked_up',
    ITEM_USED: 'item_used',
    EQUIPMENT_CHANGED: 'equipment_changed',
    GEM_COLLECTED: 'gem_collected',

    // Eventos de inimigos
    ENEMY_SPAWNED: 'enemy_spawned',
    ENEMY_DIED: 'enemy_died',
    ENEMY_ATTACKED: 'enemy_attacked',
    BOSS_SPAWNED: 'boss_spawned',
    BOSS_DEFEATED: 'boss_defeated',

    // Eventos de jogo
    GAME_STARTED: 'game_started',
    GAME_ENDED: 'game_ended',
    ROUND_STARTED: 'round_started',
    ROUND_ENDED: 'round_ended',
    OBJECTIVE_COMPLETED: 'objective_completed',

    // Eventos de UI
    UI_UPDATED: 'ui_updated',
    NOTIFICATION_SHOWN: 'notification_shown',
    SCOREBOARD_UPDATED: 'scoreboard_updated',
    HUD_REFRESHED: 'hud_refreshed',

    // Eventos especiais da Storm
    STORM_SURGE: 'storm_surge',
    CHAOS_EFFECT: 'chaos_effect',
    TEMPESTADO_AWAKENED: 'tempestado_awakened'
};

// Classe auxiliar para eventos tipados
export class StormEventEmitter {
    private eventSystem: EventSystem;

    constructor() {
        this.eventSystem = EventSystem.getInstance();
    }

    // Eventos de jogador
    onPlayerJoined(callback: (playerId: string, playerName: string) => void): void {
        this.eventSystem.on(STORM_EVENTS.PLAYER_JOINED, callback);
    }

    emitPlayerJoined(playerId: string, playerName: string): void {
        this.eventSystem.emit(STORM_EVENTS.PLAYER_JOINED, { playerId, playerName });
    }

    onPlayerDied(callback: (playerId: string, killer?: string) => void): void {
        this.eventSystem.on(STORM_EVENTS.PLAYER_DIED, callback);
    }

    emitPlayerDied(playerId: string, killer?: string): void {
        this.eventSystem.emit(STORM_EVENTS.PLAYER_DIED, { playerId, killer });
    }

    // Eventos de combate
    onDamageDealt(callback: (attacker: string, target: string, damage: number) => void): void {
        this.eventSystem.on(STORM_EVENTS.DAMAGE_DEALT, callback);
    }

    emitDamageDealt(attacker: string, target: string, damage: number): void {
        this.eventSystem.emit(STORM_EVENTS.DAMAGE_DEALT, { attacker, target, damage });
    }

    onCriticalHit(callback: (attacker: string, target: string, damage: number) => void): void {
        this.eventSystem.on(STORM_EVENTS.CRITICAL_HIT, callback);
    }

    emitCriticalHit(attacker: string, target: string, damage: number): void {
        this.eventSystem.emit(STORM_EVENTS.CRITICAL_HIT, { attacker, target, damage });
    }

    // Eventos de magia
    onSpellCast(callback: (caster: string, spellName: string, target?: string) => void): void {
        this.eventSystem.on(STORM_EVENTS.SPELL_CAST, callback);
    }

    emitSpellCast(caster: string, spellName: string, target?: string): void {
        this.eventSystem.emit(STORM_EVENTS.SPELL_CAST, { caster, spellName, target });
    }

    // Eventos de itens
    onItemPickedUp(callback: (playerId: string, itemType: string, value: number) => void): void {
        this.eventSystem.on(STORM_EVENTS.ITEM_PICKED_UP, callback);
    }

    emitItemPickedUp(playerId: string, itemType: string, value: number): void {
        this.eventSystem.emit(STORM_EVENTS.ITEM_PICKED_UP, { playerId, itemType, value });
    }

    // Eventos especiais
    onStormSurge(callback: (intensity: number, duration: number) => void): void {
        this.eventSystem.on(STORM_EVENTS.STORM_SURGE, callback);
    }

    emitStormSurge(intensity: number, duration: number): void {
        this.eventSystem.emit(STORM_EVENTS.STORM_SURGE, { intensity, duration });
    }
}

// Instância global para facilitar o uso
export const stormEvents = new StormEventEmitter();
