// Storm RPG - Physical 3D Dice Actor
// Realistic physics-based dice for cinematic moments

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h"
#include "Components/ArrowComponent.h"
#include "Components/AudioComponent.h"
#include "Engine/StaticMesh.h"
#include "Sound/SoundBase.h"
#include "Particles/ParticleSystemComponent.h"
#include "Dice/StormDiceLibrary.h"
#include "StormPhysicalDice.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnPhysicalDiceResult, class AStormPhysicalDice*, Dice, int32, Result);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnPhysicalDiceSettled, class AStormPhysicalDice*, Dice);

UENUM(BlueprintType)
enum class EDiceType : uint8
{
    D4      UMETA(DisplayName = "d4"),
    D6      UMETA(DisplayName = "d6"),
    D8      UMETA(DisplayName = "d8"),
    D10     UMETA(DisplayName = "d10"),
    D12     UMETA(DisplayName = "d12"),
    D20     UMETA(DisplayName = "d20"),
    D100    UMETA(DisplayName = "d100")
};

USTRUCT(BlueprintType)
struct STORMRPG_API FDicePhysicsSettings
{
    GENERATED_BODY()

    /** Mass of the dice in kg */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Mass = 0.1f;

    /** Linear damping (air resistance) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float LinearDamping = 0.3f;

    /** Angular damping (rotational resistance) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float AngularDamping = 0.5f;

    /** Restitution (bounciness) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Restitution = 0.4f;

    /** Friction */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float Friction = 0.7f;

    /** Minimum impulse force */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float MinImpulseForce = 300.0f;

    /** Maximum impulse force */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float MaxImpulseForce = 800.0f;

    /** Minimum torque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float MinTorque = 100.0f;

    /** Maximum torque */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physics")
    float MaxTorque = 500.0f;

    FDicePhysicsSettings()
    {
        Mass = 0.1f;
        LinearDamping = 0.3f;
        AngularDamping = 0.5f;
        Restitution = 0.4f;
        Friction = 0.7f;
        MinImpulseForce = 300.0f;
        MaxImpulseForce = 800.0f;
        MinTorque = 100.0f;
        MaxTorque = 500.0f;
    }
};

/**
 * Physical 3D Dice Actor for Storm RPG
 * Provides realistic physics-based dice rolling for cinematic moments
 */
UCLASS(BlueprintType, Blueprintable)
class STORMRPG_API AStormPhysicalDice : public AActor
{
    GENERATED_BODY()

public:
    AStormPhysicalDice();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

public:
    // ========================================
    // COMPONENTS
    // ========================================

    /** Main dice mesh */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UStaticMeshComponent* DiceMesh;

    /** Face normal arrows (used to determine which face is up) */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    TArray<class UArrowComponent*> FaceArrows;

    /** Audio component for dice sounds */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UAudioComponent* AudioComponent;

    /** Particle system for magical effects */
    UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
    class UParticleSystemComponent* MagicalEffect;

    // ========================================
    // CONFIGURATION
    // ========================================

    /** Type of dice (d4, d6, d8, d10, d12, d20, d100) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Setup")
    EDiceType DiceType = EDiceType::D20;

    /** Physics settings for this dice */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Setup")
    FDicePhysicsSettings PhysicsSettings;

    /** Static meshes for different dice types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Setup")
    TMap<EDiceType, class UStaticMesh*> DiceMeshes;

    /** Materials for different dice types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Setup")
    TMap<EDiceType, class UMaterialInterface*> DiceMaterials;

    /** Sound effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* RollSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* BounceSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* SettleSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* CriticalSound;

    /** Particle effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* RollEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* CriticalEffect;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Effects")
    class UParticleSystem* FumbleEffect;

    // ========================================
    // EVENTS
    // ========================================

    /** Called when dice shows a result */
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPhysicalDiceResult OnDiceResult;

    /** Called when dice stops moving */
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnPhysicalDiceSettled OnDiceSettled;

    // ========================================
    // PUBLIC FUNCTIONS
    // ========================================

    /** Roll the dice with random force and torque */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void RollDice();

    /** Roll the dice with specific force and torque */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void RollDiceWithForce(const FVector& ImpulseForce, const FVector& Torque);

    /** Set dice type and update mesh/materials */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void SetDiceType(EDiceType NewDiceType);

    /** Get current dice result (which face is up) */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    int32 GetCurrentResult() const;

    /** Check if dice is currently moving */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    bool IsMoving() const;

    /** Reset dice to starting position and rotation */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void ResetDice();

    /** Apply magical effect (for Storm-themed rolls) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void ApplyMagicalEffect(bool bCritical = false);

protected:
    // ========================================
    // INTERNAL FUNCTIONS
    // ========================================

    /** Initialize dice mesh and physics */
    void InitializeDice();

    /** Setup face arrows for result detection */
    void SetupFaceArrows();

    /** Update physics settings */
    void UpdatePhysicsSettings();

    /** Calculate which face is pointing up */
    int32 CalculateUpwardFace() const;

    /** Handle dice collision */
    UFUNCTION()
    void OnDiceHit(UPrimitiveComponent* HitComponent, AActor* OtherActor, UPrimitiveComponent* OtherComponent, FVector NormalImpulse, const FHitResult& Hit);

    /** Handle dice sleep (stopped moving) */
    UFUNCTION()
    void OnDiceSleep(UPrimitiveComponent* SleepingComponent, FName BoneName);

    /** Handle dice wake (started moving) */
    UFUNCTION()
    void OnDiceWake(UPrimitiveComponent* WakingComponent, FName BoneName);

    /** Play sound effect */
    void PlaySoundEffect(class USoundBase* Sound, float VolumeMultiplier = 1.0f);

    /** Spawn particle effect */
    void SpawnParticleEffect(class UParticleSystem* Effect, const FVector& Location);

private:
    // ========================================
    // STATE TRACKING
    // ========================================

    /** Current result (cached) */
    int32 CurrentResult = 0;

    /** Whether dice is currently rolling */
    bool bIsRolling = false;

    /** Time since last movement */
    float TimeSinceLastMovement = 0.0f;

    /** Threshold for considering dice "settled" */
    float SettleThreshold = 0.5f;

    /** Last known velocity (for detecting when dice stops) */
    FVector LastVelocity = FVector::ZeroVector;

    /** Number of faces for current dice type */
    int32 NumFaces = 20;

public:
    // ========================================
    // BLUEPRINT EVENTS
    // ========================================

    /** Blueprint event called when roll starts */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnRollStarted();

    /** Blueprint event called when dice bounces */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnDiceBounced(const FVector& ImpactLocation, float ImpactForce);

    /** Blueprint event called when result is determined */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnResultDetermined(int32 Result, bool bIsCritical);

    // ========================================
    // UTILITY FUNCTIONS
    // ========================================

    /** Get number of faces for dice type */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    static int32 GetNumFacesForDiceType(EDiceType DiceType);

    /** Get dice type from number of sides */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    static EDiceType GetDiceTypeFromSides(int32 NumSides);

    /** Check if result is critical for this dice type */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    bool IsCriticalResult(int32 Result) const;

    /** Check if result is fumble for this dice type */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    bool IsFumbleResult(int32 Result) const;

    // ========================================
    // MULTIPLAYER SUPPORT
    // ========================================

    /** Replicated result for multiplayer */
    UPROPERTY(ReplicatedUsing = OnRep_DiceResult)
    int32 ReplicatedResult = 0;

    /** Replicated rolling state */
    UPROPERTY(Replicated)
    bool bReplicatedIsRolling = false;

    /** Handle result replication */
    UFUNCTION()
    void OnRep_DiceResult();

    /** Server RPC to roll dice */
    UFUNCTION(Server, Reliable)
    void ServerRollDice(const FVector& ImpulseForce, const FVector& Torque);

    /** Multicast RPC to show roll effects */
    UFUNCTION(NetMulticast, Reliable)
    void MulticastShowRollEffects(const FVector& ImpulseForce, const FVector& Torque);

    /** Multicast RPC to show result */
    UFUNCTION(NetMulticast, Reliable)
    void MulticastShowResult(int32 Result, bool bIsCritical);

    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};
