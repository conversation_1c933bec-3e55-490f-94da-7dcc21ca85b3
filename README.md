# Storm RPG - O Jogo de Fantasia Épica

> *"Entre a luz e a sombra, o destino de um mundo em tormenta é escrito pelos heróis que ousam desafiar o caos."*

## 🌍 Sobre o Jogo

Storm RPG é um sistema de RPG de fantasia épica ambientado no mundo de **Aethra**, onde a **Storm** - uma força mágica primordial - permeia toda a realidade. Os jogadores interpretam heróis marcados pelo destino, capazes de moldar o mundo através de suas ações.

## 🎲 Sistema de Jogo

- **Sistema:** d20 + modificadores vs. CD (Classe de Dificuldade)
- **Atributos:** Força, Destreza, Constituição, Inteligência, Sabedoria, Carisma
- **Níveis:** 1 a 20
- **Magia:** Sistema baseado em Pontos de Mana (PM)

## 📁 Estrutura do Projeto

```
storm-rpg/
├── core/                   # Sistema básico
│   ├── dice.py            # Sistema de dados
│   ├── attributes.py      # Atributos e modificadores
│   ├── character.py       # Classe base de personagem
│   └── mechanics.py       # Mecânicas fundamentais
├── races/                 # Raças jogáveis
├── classes/               # Classes de personagem
├── magic/                 # Sistema de magia
├── combat/                # Sistema de combate
├── monsters/              # Bestiário
├── world/                 # Mundo de Aethra
├── web/                   # Interface web
├── docs/                  # Documentação
└── examples/              # Exemplos e aventuras
```

## 🚀 Como Usar

1. **Instalação:**
   ```bash
   git clone [repositório]
   cd storm-rpg
   pip install -r requirements.txt
   ```

2. **Criar Personagem:**
   ```python
   from core.character import Character
   from races.human import Human
   from classes.warrior import Warrior
   
   hero = Character(race=Human(), character_class=Warrior())
   ```

3. **Interface Web:**
   ```bash
   python web/app.py
   ```

## 🌟 Características Principais

- **10 Raças Únicas:** Humanos, Elfos, Anões, Goblins, Minotauros e mais
- **10 Classes Heroicas:** Guerreiro, Mago, Clérigo, Ladino e outras
- **Sistema de Magia Storm:** Três tipos de magia (Arcana, Divina, Selvagem)
- **Tempestados:** Heróis marcados pela Storm com poderes únicos
- **Mundo Rico:** Aethra com nações, deuses silenciosos e mistérios

## 📚 Documentação

- [Manual do Jogador](docs/player_manual.md)
- [Guia do Mestre](docs/gm_guide.md)
- [Bestiário](docs/bestiary.md)
- [Atlas de Aethra](docs/world_atlas.md)

## 🎮 Exemplos

- [Criação de Personagem](examples/character_creation.py)
- [Sistema de Combate](examples/combat_example.py)
- [Conjuração de Magias](examples/magic_example.py)

## 📄 Licença

Este projeto é uma reimaginação inspirada em sistemas de RPG clássicos, com conteúdo original e livre para uso educacional e criativo.

---

*Que a Storm guie seus dados!* ⚡
