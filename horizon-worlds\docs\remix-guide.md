# 🔄 Storm RPG - Remix Guide

> Complete guide for creators who want to remix and extend Storm RPG

## 🎯 Quick Start

### 1. Clone the World
1. Open **Horizon Worlds Desktop Editor**
2. Search for "Storm RPG" in the public worlds
3. Click **"Remix"** to create your copy
4. Rename your world (e.g., "My Fantasy Adventure")

### 2. Understand the Structure
```
Your Remixed World/
├── 📁 Entities/
│   ├── Player_Spawn_Points (4 spawn locations)
│   ├── Item_Spawn_Areas (gem collection points)
│   ├── UI_Canvas (HUD elements)
│   └── Game_Manager (main controller)
├── 📁 Scripts/
│   ├── 🎮 Core Systems (player, combat, items)
│   ├── ✨ Spells (fireball, heal, storm bolt)
│   ├── 🖥️ UI (HUD, notifications)
│   └── 🔧 Utils (dice, events, constants)
└── 📁 Assets/
    ├── 🎨 Particles (spell effects)
    ├── 🔊 Sounds (magic, combat)
    └── 🎭 Models (gems, items)
```

### 3. Test Before Modifying
- Enter **Play Mode**
- Test with multiple players (invite friends)
- Verify all systems work correctly
- Take notes on what you want to change

---

## 🛠️ Common Modifications

### 🎨 Easy Changes (Beginner)

#### Change Game Settings
Edit `scripts/utils/Constants.ts`:

```typescript
export const GAME_CONFIG = {
    MAX_PLAYERS: 6,        // ← Change from 4 to 6 players
    GAME_DURATION: 3600000, // ← Change from 30 to 60 minutes
    RESPAWN_TIME: 3000,     // ← Faster respawn (3 seconds)
    XP_PER_ENEMY_KILL: 200  // ← Double XP rewards
};
```

#### Modify Class Stats
Edit `scripts/utils/Constants.ts`:

```typescript
export const CLASS_CONFIGS = {
    WARRIOR: {
        hitDie: 12,  // ← Increase from 10 to 12
        baseAttributes: {
            strength: 18,     // ← Increase from 16
            constitution: 16  // ← Increase from 14
        }
    }
};
```

#### Change Spell Costs
Edit `scripts/utils/Constants.ts`:

```typescript
export const SPELL_CONFIGS = {
    FIREBALL: {
        manaCost: 4,  // ← Reduce from 6
        damage: "8d6" // ← Increase damage
    }
};
```

### 🎭 Visual Changes (Beginner)

#### Replace Particle Effects
1. Import your custom particle effects to **Asset Library**
2. Edit `scripts/utils/Constants.ts`:

```typescript
export const ASSET_IDS = {
    PARTICLES: {
        FIREBALL_EXPLOSION: "your_custom_fireball_id",
        HEALING_LIGHT: "your_custom_heal_id"
    }
};
```

#### Change UI Colors
Edit `scripts/utils/Constants.ts`:

```typescript
export const UI_CONSTANTS = {
    COLORS: {
        PRIMARY: "#FF6B35",    // ← Orange theme
        SECONDARY: "#F7931E",  // ← Complementary orange
        SUCCESS: "#4CAF50",    // ← Keep green for health
        DANGER: "#F44336"      // ← Keep red for damage
    }
};
```

---

## 🚀 Intermediate Modifications

### ⚔️ Add New Classes

#### 1. Create the Class File
Create `scripts/classes/Paladin.ts`:

```typescript
import { BaseCharacterClass, ClassAbility } from './BaseClass';
import { PlayerStats } from '../core/AttributeSystem';

export class PaladinClass extends BaseCharacterClass {
    constructor() {
        super("Paladin", "Holy warrior with healing and protection");
    }

    protected initializeAbilities(): void {
        // Level 1: Lay on Hands
        this.addAbility(1, {
            name: "Lay on Hands",
            description: "Heal yourself or an ally",
            level: 1,
            cooldown: 8000,
            manaCost: 4,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                const healing = 15 + caster.getAttributeModifier('charisma');
                // Apply healing logic here
                console.log(`Paladin healed for ${healing} HP`);
            }
        });

        // Level 3: Divine Protection
        this.addAbility(3, {
            name: "Divine Protection",
            description: "Increase AC for all nearby allies",
            level: 3,
            cooldown: 30000,
            manaCost: 6,
            type: 'active',
            execute: (caster: PlayerStats) => {
                console.log("Divine Protection activated!");
                // Buff nearby allies
            }
        });
    }
}
```

#### 2. Register the Class
Edit `scripts/utils/Constants.ts`:

```typescript
export const CLASS_CONFIGS = {
    // ... existing classes
    PALADIN: {
        name: "Paladin",
        hitDie: 10,
        baseAttributes: {
            strength: 14,
            dexterity: 10,
            constitution: 14,
            intelligence: 10,
            wisdom: 14,
            charisma: 16  // Primary attribute
        },
        primaryAttribute: "charisma",
        spellcasting: true
    }
};

export enum PlayerClass {
    // ... existing classes
    PALADIN = "PALADIN"
}
```

#### 3. Update Class Factory
Edit `scripts/classes/BaseClass.ts`:

```typescript
import { PaladinClass } from './Paladin';

export class ClassFactory {
    private static classes: Map<PlayerClass, () => BaseCharacterClass> = new Map([
        // ... existing classes
        [PlayerClass.PALADIN, () => new PaladinClass()]
    ]);
}
```

### ✨ Add New Spells

#### 1. Create the Spell
Create `scripts/spells/IceSpike.ts`:

```typescript
import { BaseSpell, SpellTarget, SpellEffect } from './BaseSpell';
import { PlayerStats } from '../core/AttributeSystem';

export class IceSpike extends BaseSpell {
    constructor() {
        super(
            "Ice Spike",
            "Sharp ice projectile that slows enemies",
            5,    // mana cost
            1500, // cast time
            6000, // cooldown
            30,   // range
            3     // spell level
        );
    }

    protected executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null {
        if (!target || !target.position) return null;

        // Create ice projectile
        this.createIceProjectile(target.position);

        // Calculate damage
        const damage = this.calculateSpellDamage(caster, "3d6");

        return {
            damage,
            statusEffect: 'slowed'
        };
    }

    private createIceProjectile(targetPosition: Vector3): void {
        // Visual effect
        this.createVisualEffect(targetPosition, "ice_spike_particle", 2000);
        
        // Sound effect
        this.playSoundEffect("ice_crack_sound", targetPosition);
        
        // TODO: Add projectile animation
    }
}
```

#### 2. Register the Spell
Edit `scripts/core/GameManager.ts`:

```typescript
import { IceSpike } from '../spells/IceSpike';

private registerSpells(): void {
    // ... existing spells
    spellManager.registerSpell('ice_spike', new IceSpike());
}
```

### 🏰 Add New Areas

#### 1. Create Area Script
Create `scripts/areas/ForestArea.ts`:

```typescript
export class ForestArea {
    private areaCenter = { x: 50, y: 0, z: 50 };
    private areaRadius = 20;

    constructor() {
        this.setupArea();
        this.spawnTrees();
        this.addAreaEffects();
    }

    private setupArea(): void {
        // Create forest floor
        const floor = Scene.createRootEntity();
        floor.setPosition(this.areaCenter.x, this.areaCenter.y, this.areaCenter.z);
        floor.name = "Forest_Floor";

        // Add grass texture/material
        const mesh = floor.createComponent("engine:mesh", {
            assetId: "grass_plane_model"
        });
    }

    private spawnTrees(): void {
        for (let i = 0; i < 15; i++) {
            const angle = (Math.PI * 2 * i) / 15;
            const radius = 5 + Math.random() * 10;
            
            const treePos = {
                x: this.areaCenter.x + Math.cos(angle) * radius,
                y: this.areaCenter.y,
                z: this.areaCenter.z + Math.sin(angle) * radius
            };

            this.createTree(treePos);
        }
    }

    private createTree(position: Vector3): void {
        const tree = Scene.createRootEntity();
        tree.setPosition(position.x, position.y, position.z);
        tree.name = `Forest_Tree_${Date.now()}`;

        const mesh = tree.createComponent("engine:mesh", {
            assetId: "tree_model"
        });
    }

    private addAreaEffects(): void {
        // Ambient forest sounds
        const ambientSound = Scene.createRootEntity();
        ambientSound.setPosition(this.areaCenter.x, this.areaCenter.y + 5, this.areaCenter.z);

        const audio = ambientSound.createComponent("engine:audioSource", {
            assetId: "forest_ambient_sound",
            loop: true,
            volume: 0.3,
            spatialAudio: true
        });
        audio.play();
    }
}
```

#### 2. Initialize Area
Edit `scripts/core/GameManager.ts`:

```typescript
import { ForestArea } from '../areas/ForestArea';

private forestArea: ForestArea;

private initializeSystems(): void {
    // ... existing systems
    this.forestArea = new ForestArea();
}
```

---

## 🎮 Advanced Modifications

### 🤖 Add Enemy AI

#### 1. Create Enemy Base Class
Create `scripts/enemies/BaseEnemy.ts`:

```typescript
export abstract class BaseEnemy {
    protected entity: any;
    protected health: number;
    protected maxHealth: number;
    protected attackDamage: string;
    protected moveSpeed: number;
    protected target: string | null = null;

    constructor(position: Vector3, health: number, attackDamage: string) {
        this.maxHealth = health;
        this.health = health;
        this.attackDamage = attackDamage;
        this.createEntity(position);
        this.startAI();
    }

    protected abstract createEntity(position: Vector3): void;
    protected abstract getAttackRange(): number;
    protected abstract onDeath(): void;

    protected startAI(): void {
        setInterval(() => {
            this.updateAI();
        }, 1000); // Update every second
    }

    protected updateAI(): void {
        if (this.health <= 0) return;

        // Find nearest player
        this.target = this.findNearestPlayer();
        
        if (this.target) {
            this.moveTowardsTarget();
            
            if (this.isInAttackRange()) {
                this.attack();
            }
        }
    }

    protected findNearestPlayer(): string | null {
        // Implementation to find nearest player
        return null; // Placeholder
    }

    protected moveTowardsTarget(): void {
        // Implementation to move towards target
    }

    protected isInAttackRange(): boolean {
        // Check if target is in attack range
        return false; // Placeholder
    }

    protected attack(): void {
        // Perform attack on target
    }

    takeDamage(damage: number): void {
        this.health -= damage;
        if (this.health <= 0) {
            this.onDeath();
        }
    }
}
```

#### 2. Create Specific Enemy
Create `scripts/enemies/Goblin.ts`:

```typescript
import { BaseEnemy } from './BaseEnemy';

export class Goblin extends BaseEnemy {
    constructor(position: Vector3) {
        super(position, 25, "1d6+2"); // 25 HP, 1d6+2 damage
        this.moveSpeed = 4;
    }

    protected createEntity(position: Vector3): void {
        this.entity = Scene.createRootEntity();
        this.entity.setPosition(position.x, position.y, position.z);
        this.entity.name = `Goblin_${Date.now()}`;

        // Add goblin model
        const mesh = this.entity.createComponent("engine:mesh", {
            assetId: "goblin_model"
        });

        // Add health component
        const health = this.entity.createComponent("game:health", {
            maxHealth: this.maxHealth,
            currentHealth: this.health
        });
    }

    protected getAttackRange(): number {
        return 2; // 2 meters
    }

    protected onDeath(): void {
        // Drop loot
        this.dropLoot();
        
        // Remove entity
        this.entity.destroy();
        
        // Award XP to killer
        // Implementation here
    }

    private dropLoot(): void {
        const position = this.entity.getPosition();
        
        // 50% chance to drop a small gem
        if (Math.random() < 0.5) {
            // Create gem at goblin's position
            // Implementation here
        }
    }
}
```

### 🎯 Quest System

#### 1. Create Quest Manager
Create `scripts/quests/QuestManager.ts`:

```typescript
interface Quest {
    id: string;
    name: string;
    description: string;
    objectives: QuestObjective[];
    rewards: QuestReward[];
    isCompleted: boolean;
}

interface QuestObjective {
    id: string;
    description: string;
    type: 'kill' | 'collect' | 'reach';
    target: string;
    current: number;
    required: number;
    isCompleted: boolean;
}

export class QuestManager {
    private activeQuests: Map<string, Quest[]> = new Map(); // playerId -> quests

    giveQuest(playerId: string, quest: Quest): void {
        if (!this.activeQuests.has(playerId)) {
            this.activeQuests.set(playerId, []);
        }
        
        this.activeQuests.get(playerId)!.push(quest);
        this.notifyQuestAdded(playerId, quest);
    }

    updateObjective(playerId: string, objectiveType: string, target: string, amount: number = 1): void {
        const playerQuests = this.activeQuests.get(playerId);
        if (!playerQuests) return;

        playerQuests.forEach(quest => {
            quest.objectives.forEach(objective => {
                if (objective.type === objectiveType && 
                    objective.target === target && 
                    !objective.isCompleted) {
                    
                    objective.current += amount;
                    
                    if (objective.current >= objective.required) {
                        objective.isCompleted = true;
                        this.checkQuestCompletion(playerId, quest);
                    }
                }
            });
        });
    }

    private checkQuestCompletion(playerId: string, quest: Quest): void {
        const allCompleted = quest.objectives.every(obj => obj.isCompleted);
        
        if (allCompleted && !quest.isCompleted) {
            quest.isCompleted = true;
            this.giveRewards(playerId, quest.rewards);
            this.notifyQuestCompleted(playerId, quest);
        }
    }
}
```

---

## 🎨 Asset Creation Tips

### Particle Effects
1. Use **Horizon's Particle Editor**
2. Keep effects under 3 seconds duration
3. Use appropriate colors for spell types:
   - 🔥 Fire: Red/Orange/Yellow
   - ❄️ Ice: Blue/White/Cyan
   - ⚡ Lightning: Blue/White/Purple
   - 🌿 Nature: Green/Brown

### Sound Effects
1. Keep audio files under 5 seconds
2. Use appropriate volume levels (0.3-0.8)
3. Consider spatial audio for immersion
4. Provide both 2D (UI) and 3D (world) versions

### 3D Models
1. Optimize polygon count for VR performance
2. Use appropriate scale (1 unit = 1 meter)
3. Include LOD (Level of Detail) versions
4. Test on both mobile and VR devices

---

## 🐛 Common Issues & Solutions

### Performance Problems
```typescript
// ❌ Bad: Creating too many entities
for (let i = 0; i < 100; i++) {
    const particle = Scene.createRootEntity();
}

// ✅ Good: Use object pooling
class ParticlePool {
    private pool: any[] = [];
    
    getParticle(): any {
        return this.pool.pop() || Scene.createRootEntity();
    }
    
    returnParticle(particle: any): void {
        particle.setActive(false);
        this.pool.push(particle);
    }
}
```

### Script Errors
```typescript
// ❌ Bad: No error handling
player.getComponent("health").currentHealth -= damage;

// ✅ Good: Safe access
try {
    const healthComp = player.getComponent("health");
    if (healthComp) {
        healthComp.currentHealth -= damage;
    }
} catch (error) {
    console.error("Error applying damage:", error);
}
```

### Multiplayer Sync Issues
```typescript
// ❌ Bad: Local state only
let playerScore = 0;

// ✅ Good: Use event system
stormEvents.emit('score_updated', { playerId, newScore });
```

---

## 📚 Resources & Community

### Documentation
- [Storm RPG API Reference](api-reference.md)
- [Complete Tutorial](tutorial.md)
- [Horizon Worlds TypeScript Docs](https://developers.meta.com/horizon-worlds/)

### Community
- **Discord:** Storm RPG Creators
- **GitHub:** [Storm RPG Repository](https://github.com/storm-rpg/horizon-worlds)
- **Forum:** Horizon Worlds Community

### Getting Help
1. Check the **API Reference** for method signatures
2. Look at existing code examples in the project
3. Ask in the **Discord community**
4. Create an issue on **GitHub** for bugs

---

## 🏆 Showcase Your Remix

When you've created something amazing:

1. **Publish your world** with a clear name
2. **Tag it** with "Storm RPG Remix"
3. **Share screenshots** in the community
4. **Write a brief description** of your changes
5. **Credit the original** Storm RPG project

### Example Description:
> "**Forest Quest RPG** - A remix of Storm RPG featuring a mystical forest area with tree enemies, nature magic, and a Druid class. Added quest system and ambient forest sounds for immersion."

---

*Happy remixing! May your creativity flow like the Storm itself!* ⚡
