// Blueprint Class: BP_DiceManager
// This would be created in the Unreal Editor as a Blueprint Class
// Here's the equivalent C++ header for reference

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Dice/StormDiceLibrary.h"
#include "UI/StormDiceWidget.h"
#include "Actors/StormPhysicalDice.h"
#include "Engine/DataTable.h"
#include "StormDiceManager.generated.h"

USTRUCT(BlueprintType)
struct STORMRPG_API FDiceRollRequest
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Roll")
    FString Expression = "1d20";

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Roll")
    bool bUsePhysicalDice = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Roll")
    bool bShowUI = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Roll")
    FVector SpawnLocation = FVector::ZeroVector;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Roll")
    FString Context = "";

    FDiceRollRequest()
    {
        Expression = "1d20";
        bUsePhysicalDice = false;
        bShowUI = true;
        SpawnLocation = FVector::ZeroVector;
        Context = "";
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FOnDiceManagerResult, FStormDiceResult, Result, FString, Context);

/**
 * Dice Manager - Coordinates all dice rolling methods
 * Handles logic, UI, and physical dice based on context
 */
UCLASS(BlueprintType, Blueprintable)
class STORMRPG_API AStormDiceManager : public AActor
{
    GENERATED_BODY()

public:
    AStormDiceManager();

protected:
    virtual void BeginPlay() override;

public:
    // ========================================
    // COMPONENTS & REFERENCES
    // ========================================

    /** UI Widget for 2D dice display */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "UI")
    TSubclassOf<class UStormDiceWidget> DiceWidgetClass;

    /** Physical dice actor class */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Physical Dice")
    TSubclassOf<class AStormPhysicalDice> PhysicalDiceClass;

    /** Current dice widget instance */
    UPROPERTY(BlueprintReadOnly, Category = "UI")
    class UStormDiceWidget* CurrentDiceWidget;

    /** Active physical dice */
    UPROPERTY(BlueprintReadOnly, Category = "Physical Dice")
    TArray<class AStormPhysicalDice*> ActivePhysicalDice;

    // ========================================
    // CONFIGURATION
    // ========================================

    /** Default dice rolling method */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bUsePhysicalDiceByDefault = false;

    /** Show UI by default */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    bool bShowUIByDefault = true;

    /** Auto-clear dice after time */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    float AutoClearTime = 5.0f;

    /** Maximum number of physical dice */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Configuration")
    int32 MaxPhysicalDice = 10;

    // ========================================
    // EVENTS
    // ========================================

    /** Called when any dice roll completes */
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDiceManagerResult OnDiceResult;

    // ========================================
    // PUBLIC FUNCTIONS
    // ========================================

    /** Main dice rolling function */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager")
    void RollDice(const FDiceRollRequest& Request);

    /** Quick roll with expression */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager")
    void QuickRoll(const FString& Expression, bool bUsePhysical = false);

    /** Roll for specific game context */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager")
    void RollForContext(const FString& Context, const FString& Expression, bool bUsePhysical = false);

    // ========================================
    // PRESET ROLLS
    // ========================================

    /** Roll attack */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Presets")
    void RollAttack(int32 AttackBonus, int32 TargetAC, bool bAdvantage = false, bool bUsePhysical = false);

    /** Roll damage */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Presets")
    void RollDamage(const FString& DamageExpression, bool bUsePhysical = false);

    /** Roll saving throw */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Presets")
    void RollSavingThrow(int32 SaveBonus, int32 DC, bool bAdvantage = false, bool bUsePhysical = false);

    /** Roll skill check */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Presets")
    void RollSkillCheck(int32 SkillBonus, int32 DC, bool bAdvantage = false, bool bUsePhysical = false);

    /** Roll initiative */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Presets")
    void RollInitiative(int32 DexModifier, int32 LevelBonus = 0, bool bUsePhysical = false);

    // ========================================
    // STORM-SPECIFIC ROLLS
    // ========================================

    /** Roll Storm surge */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Storm")
    void RollStormSurge(int32 CasterLevel, bool bUsePhysical = true);

    /** Roll Tempestado awakening */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Storm")
    void RollTempestadoAwakening(int32 CharacterLevel, int32 StormExposure, bool bUsePhysical = true);

    /** Roll chaos effect */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Storm")
    void RollChaosEffect(bool bUsePhysical = true);

    // ========================================
    // UI MANAGEMENT
    // ========================================

    /** Show/hide dice UI */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|UI")
    void ShowDiceUI(bool bShow = true);

    /** Create dice widget if needed */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|UI")
    void EnsureDiceWidget();

    /** Clear all dice displays */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|UI")
    void ClearAllDice();

    // ========================================
    // PHYSICAL DICE MANAGEMENT
    // ========================================

    /** Spawn physical dice */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Physical")
    class AStormPhysicalDice* SpawnPhysicalDice(EDiceType DiceType, const FVector& Location);

    /** Clear all physical dice */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Physical")
    void ClearPhysicalDice();

    /** Get spawn location for physical dice */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice Manager|Physical")
    FVector GetPhysicalDiceSpawnLocation() const;

protected:
    // ========================================
    // INTERNAL FUNCTIONS
    // ========================================

    /** Handle logic-only roll */
    void HandleLogicRoll(const FDiceRollRequest& Request);

    /** Handle UI roll */
    void HandleUIRoll(const FDiceRollRequest& Request);

    /** Handle physical roll */
    void HandlePhysicalRoll(const FDiceRollRequest& Request);

    /** Parse expression to determine dice types needed */
    TArray<EDiceType> ParseDiceTypesFromExpression(const FString& Expression);

    /** Handle physical dice result */
    UFUNCTION()
    void OnPhysicalDiceResult(class AStormPhysicalDice* Dice, int32 Result);

    /** Handle UI dice result */
    UFUNCTION()
    void OnUIDiceResult(FStormDiceResult Result);

    /** Auto-clear timer callback */
    UFUNCTION()
    void AutoClearDice();

private:
    /** Timer handle for auto-clear */
    FTimerHandle AutoClearTimerHandle;

    /** Pending roll requests (for physical dice) */
    TArray<FDiceRollRequest> PendingRequests;

public:
    // ========================================
    // BLUEPRINT EVENTS
    // ========================================

    /** Blueprint event for roll started */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnRollStarted(const FString& Expression, const FString& Context);

    /** Blueprint event for roll completed */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnRollCompleted(const FStormDiceResult& Result, const FString& Context);

    /** Blueprint event for critical hit */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnCriticalHit(const FStormDiceResult& Result, const FString& Context);

    /** Blueprint event for critical failure */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnCriticalFailure(const FStormDiceResult& Result, const FString& Context);

    // ========================================
    // CONSOLE COMMANDS
    // ========================================

    /** Console command for quick rolling */
    UFUNCTION(Exec, Category = "Storm|Dice Manager")
    void StormRollCommand(const FString& Expression);

    /** Console command for physical dice */
    UFUNCTION(Exec, Category = "Storm|Dice Manager")
    void StormRollPhysical(const FString& Expression);

    /** Console command to clear dice */
    UFUNCTION(Exec, Category = "Storm|Dice Manager")
    void StormClearDice();
};
