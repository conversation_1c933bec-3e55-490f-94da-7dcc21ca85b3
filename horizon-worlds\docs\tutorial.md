# 🌩️ Tutorial: Como Criar um RPG Cooperativo em Horizon Worlds

> **Guia completo para criar seu próprio RPG usando o Storm RPG como base**

## 📋 Índice

1. [Introdução](#introdução)
2. [Configuração Inicial](#configuração-inicial)
3. [Sistema de Atributos e Classes](#sistema-de-atributos-e-classes)
4. [Combate com d20 Simulado](#combate-com-d20-simulado)
5. [Magias Interativas](#magias-interativas)
6. [<PERSON><PERSON><PERSON> de Itens](#sistema-de-itens)
7. [Interface de Usuário](#interface-de-usuário)
8. [<PERSON><PERSON><PERSON>](#tornando-remixável)
9. [Dicas Avançadas](#dicas-avançadas)

---

## 🎯 Introdução

Este tutorial ensina como criar um RPG cooperativo completo no Meta Horizon Worlds usando TypeScript. Você aprenderá a implementar:

- Sistema de classes (Guerreiro, Mago, Ladino)
- Combate em tempo real com mecânicas d20
- Magias com efeitos visuais e sonoros
- Coleta de itens e progressão
- Interface de usuário dinâmica
- Sistema multiplayer cooperativo

**Tempo estimado:** 4-6 horas  
**Nível:** Intermediário  
**Pré-requisitos:** Conhecimento básico de TypeScript e Horizon Worlds

---

## 🛠️ Configuração Inicial

### Passo 1: Criar o Mundo Base

1. Abra o **Horizon Worlds Desktop Editor**
2. Crie um novo mundo chamado "Meu RPG"
3. Adicione uma plataforma base (20x20 metros)
4. Configure 4 pontos de spawn para jogadores

```typescript
// Exemplo de configuração de spawn points
const SPAWN_POINTS = [
    { x: 0, y: 1, z: 0 },
    { x: 5, y: 1, z: 0 },
    { x: -5, y: 1, z: 0 },
    { x: 0, y: 1, z: 5 }
];
```

### Passo 2: Estrutura de Pastas

Organize seus scripts seguindo esta estrutura:

```
/scripts
├── core/
│   ├── GameManager.ts
│   ├── PlayerManager.ts
│   └── AttributeSystem.ts
├── spells/
│   ├── BaseSpell.ts
│   └── Fireball.ts
├── ui/
│   └── HUDManager.ts
└── utils/
    ├── DiceRoller.ts
    └── Constants.ts
```

### Passo 3: Script Principal

Crie o script principal que inicializa o jogo:

```typescript
// scripts/Main.ts
import { GameManager } from './core/GameManager';

// Inicializar quando o mundo carregar
const gameManager = new GameManager();
console.log("RPG inicializado!");
```

---

## ⚔️ Sistema de Atributos e Classes

### Implementando Atributos Básicos

```typescript
// scripts/core/AttributeSystem.ts
export interface Attributes {
    strength: number;     // Força
    dexterity: number;    // Destreza
    constitution: number; // Constituição
    intelligence: number; // Inteligência
    wisdom: number;       // Sabedoria
    charisma: number;     // Carisma
}

export class PlayerStats {
    attributes: Attributes;
    level: number = 1;
    hitPoints: number;
    manaPoints: number;

    constructor(playerClass: string) {
        this.attributes = this.getClassAttributes(playerClass);
        this.calculateDerivedStats();
    }

    getClassAttributes(playerClass: string): Attributes {
        switch (playerClass) {
            case "Warrior":
                return { strength: 16, dexterity: 12, constitution: 14, 
                        intelligence: 8, wisdom: 10, charisma: 10 };
            case "Mage":
                return { strength: 8, dexterity: 14, constitution: 10, 
                        intelligence: 18, wisdom: 12, charisma: 10 };
            case "Rogue":
                return { strength: 10, dexterity: 18, constitution: 12, 
                        intelligence: 12, wisdom: 14, charisma: 14 };
            default:
                return { strength: 10, dexterity: 10, constitution: 10, 
                        intelligence: 10, wisdom: 10, charisma: 10 };
        }
    }

    getModifier(attribute: keyof Attributes): number {
        return Math.floor((this.attributes[attribute] - 10) / 2);
    }
}
```

### Criando Classes de Personagem

```typescript
// scripts/classes/Warrior.ts
export class Warrior {
    name = "Guerreiro";
    hitDie = 10;
    
    getAbilities(level: number): string[] {
        const abilities = ["Golpe Poderoso"];
        if (level >= 3) abilities.push("Ataque Extra");
        if (level >= 5) abilities.push("Fúria");
        return abilities;
    }
}
```

---

## 🎲 Combate com d20 Simulado

### Sistema de Dados

```typescript
// scripts/utils/DiceRoller.ts
export class DiceRoller {
    static rollD20(): number {
        return Math.floor(Math.random() * 20) + 1;
    }

    static rollAttack(attackBonus: number, targetAC: number): {
        hit: boolean;
        roll: number;
        total: number;
        critical: boolean;
    } {
        const roll = this.rollD20();
        const total = roll + attackBonus;
        const hit = total >= targetAC;
        const critical = roll === 20;

        return { hit, roll, total, critical };
    }

    static rollDamage(diceString: string): number {
        // Parse "2d6+3" format
        const match = diceString.match(/(\d+)d(\d+)([+-]\d+)?/);
        if (!match) return 0;

        const numDice = parseInt(match[1]);
        const sides = parseInt(match[2]);
        const modifier = match[3] ? parseInt(match[3]) : 0;

        let total = 0;
        for (let i = 0; i < numDice; i++) {
            total += Math.floor(Math.random() * sides) + 1;
        }

        return total + modifier;
    }
}
```

### Sistema de Combate

```typescript
// scripts/core/CombatSystem.ts
export class CombatSystem {
    performAttack(attacker: PlayerStats, target: any): void {
        const attackBonus = attacker.getModifier('strength') + attacker.level;
        const targetAC = 15; // Exemplo

        const attackResult = DiceRoller.rollAttack(attackBonus, targetAC);
        
        if (attackResult.hit) {
            let damage = DiceRoller.rollDamage("1d8");
            damage += attacker.getModifier('strength');
            
            if (attackResult.critical) {
                damage *= 2;
                console.log("CRÍTICO!");
            }
            
            this.applyDamage(target, damage);
        }
    }
}
```

---

## ✨ Magias Interativas

### Classe Base de Magia

```typescript
// scripts/spells/BaseSpell.ts
export abstract class BaseSpell {
    name: string;
    manaCost: number;
    range: number;

    constructor(name: string, manaCost: number, range: number) {
        this.name = name;
        this.manaCost = manaCost;
        this.range = range;
    }

    abstract cast(caster: PlayerStats, target?: any): void;

    protected createVisualEffect(position: Vector3, effectId: string): void {
        const effect = Scene.createRootEntity();
        effect.setPosition(position.x, position.y, position.z);
        
        const particle = effect.createComponent("engine:persistentParticleEffect", {
            assetId: effectId
        });

        // Remover após 3 segundos
        setTimeout(() => effect.destroy(), 3000);
    }
}
```

### Implementando Bola de Fogo

```typescript
// scripts/spells/Fireball.ts
export class Fireball extends BaseSpell {
    constructor() {
        super("Bola de Fogo", 6, 30);
    }

    cast(caster: PlayerStats, targetPosition: Vector3): void {
        // Verificar mana
        if (caster.manaPoints < this.manaCost) {
            console.log("Mana insuficiente!");
            return;
        }

        caster.manaPoints -= this.manaCost;

        // Criar projétil
        this.createFireballProjectile(targetPosition);

        // Aplicar dano após 1 segundo
        setTimeout(() => {
            this.explode(targetPosition, caster);
        }, 1000);
    }

    private createFireballProjectile(target: Vector3): void {
        const fireball = Scene.createRootEntity();
        fireball.setPosition(0, 1.5, 0); // Posição do conjurador
        
        // Efeito visual
        this.createVisualEffect(target, "fireball_particle");
        
        // Som
        const audio = fireball.createComponent("engine:audioSource", {
            assetId: "fireball_sound",
            volume: 0.8
        });
        audio.play();
    }

    private explode(position: Vector3, caster: PlayerStats): void {
        // Efeito de explosão
        this.createVisualEffect(position, "explosion_particle");
        
        // Calcular dano
        const damage = DiceRoller.rollDamage("6d6");
        
        // Encontrar inimigos na área (raio de 6m)
        const enemies = this.findEnemiesInRadius(position, 6);
        enemies.forEach(enemy => {
            this.applyFireDamage(enemy, damage);
        });
    }
}
```

---

## 💎 Sistema de Itens

### Criando Itens Coletáveis

```typescript
// scripts/items/ItemPickup.ts
export class ItemPickup {
    createGem(position: Vector3, value: number): void {
        const gem = Scene.createRootEntity();
        gem.setPosition(position.x, position.y, position.z);
        gem.name = `Gem_${Date.now()}`;

        // Modelo 3D
        const mesh = gem.createComponent("engine:mesh", {
            assetId: "gem_model"
        });

        // Animação de rotação
        this.addRotationAnimation(gem);

        // Trigger de colisão
        const trigger = gem.createComponent("engine:trigger", {
            shape: "sphere",
            radius: 1.0
        });

        trigger.onTriggerEnter.add((player) => {
            this.collectGem(player, gem, value);
        });
    }

    private collectGem(player: any, gem: any, value: number): void {
        // Efeito visual
        this.createPickupEffect(gem.getPosition());
        
        // Som
        this.playPickupSound();
        
        // Adicionar pontos ao jogador
        this.addScore(player.id, value);
        
        // Remover gema
        gem.destroy();
    }
}
```

---

## 🖥️ Interface de Usuário

### Criando HUD Dinâmica

```typescript
// scripts/ui/HUDManager.ts
export class HUDManager {
    createHealthBar(player: any): void {
        const canvas = Scene.createRootEntity();
        canvas.name = `HUD_${player.id}`;

        const canvasComponent = canvas.createComponent("engine:canvas", {
            renderMode: "screenSpace"
        });

        // Barra de vida
        const healthBar = Scene.createRootEntity();
        healthBar.setParent(canvas);

        const rectTransform = healthBar.createComponent("engine:rectTransform", {
            anchorMin: { x: 0.05, y: 0.9 },
            anchorMax: { x: 0.35, y: 0.95 }
        });

        const image = healthBar.createComponent("engine:image", {
            color: { r: 0.8, g: 0.2, b: 0.2, a: 1 }
        });
    }

    updateHealthBar(playerId: string, currentHP: number, maxHP: number): void {
        const healthBar = Scene.root.findEntityByName(`HealthBar_${playerId}`);
        if (healthBar) {
            const fillComponent = healthBar.getComponent("engine:image");
            fillComponent.fillAmount = currentHP / maxHP;
        }
    }

    showNotification(playerId: string, message: string): void {
        const notification = Scene.root.findEntityByName(`Notification_${playerId}`);
        if (notification) {
            const text = notification.getComponent("engine:text");
            text.text = message;
            notification.setActive(true);

            setTimeout(() => {
                notification.setActive(false);
            }, 3000);
        }
    }
}
```

---

## 🔄 Tornando Remixável

### Boas Práticas para Remix

1. **Use nomes descritivos:**
```typescript
// ❌ Ruim
const e1 = Scene.createRootEntity();

// ✅ Bom
const playerSpawnPoint = Scene.createRootEntity();
playerSpawnPoint.name = "Player_Spawn_Point_1";
```

2. **Documente seu código:**
```typescript
/**
 * Cria um ponto de spawn para jogadores
 * @param position - Posição do spawn point
 * @param spawnId - ID único do spawn
 */
function createSpawnPoint(position: Vector3, spawnId: string): void {
    // Implementação...
}
```

3. **Use constantes configuráveis:**
```typescript
// scripts/config/GameConfig.ts
export const GAME_CONFIG = {
    MAX_PLAYERS: 4,
    GAME_DURATION: 1800000, // 30 minutos
    RESPAWN_TIME: 5000,     // 5 segundos
    
    // Fácil de modificar para outros criadores
    WARRIOR_BASE_HP: 100,
    MAGE_BASE_HP: 60,
    ROGUE_BASE_HP: 80
};
```

4. **Publique assets reutilizáveis:**
   - Efeitos de partículas
   - Sons de magia
   - Modelos 3D de itens
   - Texturas de interface

---

## 💡 Dicas Avançadas

### Performance

```typescript
// Use object pooling para projéteis
class ProjectilePool {
    private pool: any[] = [];
    
    getProjectile(): any {
        return this.pool.pop() || this.createNewProjectile();
    }
    
    returnProjectile(projectile: any): void {
        projectile.setActive(false);
        this.pool.push(projectile);
    }
}
```

### Eventos Customizados

```typescript
// Sistema de eventos para comunicação entre sistemas
class EventSystem {
    private listeners = new Map<string, Function[]>();
    
    on(event: string, callback: Function): void {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event)!.push(callback);
    }
    
    emit(event: string, data?: any): void {
        const callbacks = this.listeners.get(event);
        if (callbacks) {
            callbacks.forEach(cb => cb(data));
        }
    }
}
```

### Salvamento de Progresso

```typescript
// Salvar progresso do jogador
class SaveSystem {
    savePlayerData(playerId: string, data: any): void {
        const saveData = JSON.stringify(data);
        // Use Horizon Worlds persistent storage
        // (implementação específica da plataforma)
    }
    
    loadPlayerData(playerId: string): any {
        // Carregar dados salvos
        // (implementação específica da plataforma)
    }
}
```

---

## 🎯 Próximos Passos

Agora que você tem a base, pode expandir com:

1. **Mais Classes:** Paladino, Druida, Bardo
2. **Sistema de Guilds:** Cooperação entre jogadores
3. **Dungeons:** Áreas instanciadas para grupos
4. **PvP:** Combate entre jogadores
5. **Economia:** Sistema de comércio
6. **Quests:** Missões e objetivos

---

## 📚 Recursos Adicionais

- [Documentação do Horizon Worlds](https://developers.meta.com/horizon-worlds/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Storm RPG GitHub Repository](#)
- [Discord da Comunidade](#)

---

**Criado com ❤️ para a competição Open Source Champions**

*Que a Storm guie seu código!* ⚡
