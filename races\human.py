"""
Storm RPG - Raça Humana
Os humanos são adaptáveis e ambiciosos, dominando muitas terras de Aethra
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus
from core.attributes import AttributeType


class Human(Race):
    """
    Humanos - A raça mais adaptável e ambiciosa
    
    Os humanos são a raça mais numerosa e diversa de Aethra. Sua adaptabilidade
    e ambição os levaram a construir grandes reinos e explorar todos os cantos
    do mundo. Embora não possuam as habilidades especializadas de outras raças,
    sua versatilidade os torna capazes de se destacar em qualquer área.
    """
    
    def get_name(self) -> str:
        return "Humano"
    
    def get_description(self) -> str:
        return ("Adaptáveis e ambiciosos, os humanos são a raça dominante em "
                "muitas regiões de Aethra. Sua versatilidade compensa a falta "
                "de habilidades raciais especializadas.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9  # 9 metros por turno
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            RacialTrait(
                name="Versatilidade Humana",
                description="Escolha um atributo para receber +2 de bônus racial",
                trait_type=TraitType.ATTRIBUTE_BONUS,
                value=(AttributeType.STRENGTH, 2)  # Padrão, pode ser alterado
            ),
            
            RacialTrait(
                name="Talento Adicional",
                description="Humanos recebem um talento adicional no 1º nível",
                trait_type=TraitType.OTHER,
                value="extra_feat"
            ),
            
            RacialTrait(
                name="Perícia Adicional",
                description="Humanos recebem 2 pontos de perícia adicionais por nível",
                trait_type=TraitType.SKILL_BONUS,
                value=("all", 2)
            ),
            
            RacialTrait(
                name="Ambição",
                description="Humanos ganham 10% mais experiência em aventuras",
                trait_type=TraitType.OTHER,
                value="bonus_xp"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Idioma Regional"]
    
    def apply_to_character(self, character):
        """Aplica características humanas ao personagem"""
        super().apply_to_character(character)
        
        # Humanos podem escolher o atributo para o bônus +2
        # Por padrão, aplicamos em Força, mas isso pode ser customizado
        
        # Adicionar pontos de perícia extras
        character.skill_points_per_level = getattr(character, 'skill_points_per_level', 0) + 2
    
    def set_attribute_bonus(self, character, attribute_type: AttributeType):
        """Permite escolher qual atributo recebe o bônus +2"""
        # Remove o bônus anterior se existir
        for attr_type in AttributeType:
            character.attributes.attributes[attr_type].racial_bonus = 0
        
        # Aplica o novo bônus
        character.attributes.apply_racial_bonus(attribute_type, 2)


class VariantHuman(Human):
    """
    Variante Humana - Foco em talentos
    
    Uma versão alternativa dos humanos que troca alguns benefícios
    por talentos adicionais, representando humanos especializados.
    """
    
    def get_name(self) -> str:
        return "Humano Variante"
    
    def get_description(self) -> str:
        return ("Humanos especializados que desenvolveram talentos únicos "
                "em troca de versatilidade geral.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            RacialTrait(
                name="Especialização",
                description="Escolha dois atributos diferentes para receber +1 cada",
                trait_type=TraitType.ATTRIBUTE_BONUS,
                value=[(AttributeType.STRENGTH, 1), (AttributeType.DEXTERITY, 1)]
            ),
            
            RacialTrait(
                name="Talento Especializado",
                description="Recebe um talento adicional no 1º nível",
                trait_type=TraitType.OTHER,
                value="extra_feat"
            ),
            
            RacialTrait(
                name="Perícia Especializada",
                description="Escolha uma perícia para receber +2 de bônus racial",
                trait_type=TraitType.SKILL_BONUS,
                value=("chosen_skill", 2)
            )
        ]


# Subtipos regionais de humanos
class AlenathHuman(Human):
    """Humanos do Reino de Alenath - Tecnológicos e mágicos"""
    
    def get_name(self) -> str:
        return "Humano de Alenath"
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui a versatilidade por especialização tecnomágica
        traits[0] = create_attribute_bonus(AttributeType.INTELLIGENCE, 2, "Intelecto Alenathiano")
        
        # Adiciona conhecimento tecnomágico
        traits.append(
            RacialTrait(
                name="Conhecimento Tecnomágico",
                description="Recebe +2 em testes relacionados a magia e tecnologia",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Arcano", 2)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Alenathiano", "Arcano"]


class TribalHuman(Human):
    """Humanos tribais - Conectados com a natureza"""
    
    def get_name(self) -> str:
        return "Humano Tribal"
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui a versatilidade por conexão natural
        traits[0] = create_attribute_bonus(AttributeType.WISDOM, 2, "Sabedoria Ancestral")
        
        # Adiciona sobrevivência natural
        traits.append(
            RacialTrait(
                name="Sobrevivência Natural",
                description="Recebe +2 em testes de Sobrevivência e Natureza",
                trait_type=TraitType.SKILL_BONUS,
                value=("Sobrevivência", 2)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Tribal", "Silvestre"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Raça Humana ===")
    
    human = Human()
    print(human.get_summary())
    
    print(f"\n=== Humano de Alenath ===")
    alenath_human = AlenathHuman()
    print(alenath_human.get_summary())
    
    print(f"\n=== Humano Tribal ===")
    tribal_human = TribalHuman()
    print(tribal_human.get_summary())
