// Storm RPG - Main Entry Point
// Initializes all game systems and manages the world

import { GameManager } from './core/GameManager';
import { PlayerManager } from './core/PlayerManager';
import { stormEvents } from './utils/EventSystem';
import { demoManager } from './demo/DemoManager';
import { demoHUD } from './demo/DemoHUD';
import { performanceOptimizer } from './demo/PerformanceOptimizer';
import { demoAssets } from './demo/DemoAssets';

// Demo mode flag - set to true for demo experience
const DEMO_MODE = true;

// Initialize core systems
const gameManager = new GameManager();
const playerManager = new PlayerManager();

// Initialize demo systems if in demo mode
if (DEMO_MODE) {
    console.log("🎮 Initializing Storm RPG in DEMO MODE");
    
    // Demo systems are automatically initialized via their constructors
    // demoManager, demoHUD, performanceOptimizer, demoAssets
    
    // Set up demo-specific event handlers
    stormEvents.onPlayerJoined((playerId, playerName) => {
        console.log(`🌟 ${playerName} joined the Arton Demo!`);
        
        // Demo manager will handle the demo flow
        // demoManager.startDemo() is called automatically via events
    });
    
    stormEvents.onPlayerLeft((playerId, playerName) => {
        console.log(`👋 ${playerName} left the Arton Demo.`);
        
        // Cleanup demo resources
        demoHUD.removeHUD(playerId);
    });
    
    // Demo-specific console commands
    (globalThis as any).DemoCommands = {
        completeDemo: (playerId: string) => demoManager.forceCompleteDemo(playerId),
        restartDemo: (playerId: string) => demoManager.restartDemo(playerId),
        showStats: () => {
            console.log("Performance Stats:", performanceOptimizer.getPerformanceStats());
            console.log("Memory Stats:", demoAssets.getMemoryStats());
        },
        optimizeAssets: () => demoAssets.optimizeForCurrentProfile()
    };
    
} else {
    // Standard RPG mode
    console.log("⚔️ Initializing Storm RPG in FULL MODE");
    
    stormEvents.onPlayerJoined((playerId, playerName) => {
        console.log(`🎮 ${playerName} joined the Storm RPG world!`);
    });

    stormEvents.onPlayerLeft((playerId, playerName) => {
        console.log(`👋 ${playerName} left the Storm RPG world.`);
    });
}

// Export for global access
(globalThis as any).StormRPG = {
    gameManager,
    playerManager,
    events: stormEvents,
    demoMode: DEMO_MODE,
    demo: DEMO_MODE ? {
        manager: demoManager,
        hud: demoHUD,
        optimizer: performanceOptimizer,
        assets: demoAssets
    } : null
};

console.log(`⚡ Storm RPG initialized successfully in ${DEMO_MODE ? 'DEMO' : 'FULL'} mode!`);
