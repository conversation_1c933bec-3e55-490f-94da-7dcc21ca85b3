// Blueprint Class: GA_StormAttack (Gameplay Ability)
// This would be created in the Unreal Editor as a Blueprint Class
// Here's the equivalent C++ implementation for reference

#pragma once

#include "CoreMinimal.h"
#include "Abilities/GameplayAbility.h"
#include "GameplayTagContainer.h"
#include "Dice/StormDiceLibrary.h"
#include "StormAttackAbility.generated.h"

/**
 * Storm Attack Gameplay Ability
 * Integrates dice rolling with Gameplay Ability System
 */
UCLASS(BlueprintType, Blueprintable)
class STORMRPG_API UStormAttackAbility : public UGameplayAbility
{
    GENERATED_BODY()

public:
    UStormAttackAbility();

protected:
    virtual void ActivateAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, const FGameplayEventData* TriggerEventData) override;
    virtual void EndAbility(const FGameplayAbilitySpecHandle Handle, const FGameplayAbilityActorInfo* ActorInfo, const FGameplayAbilityActivationInfo ActivationInfo, bool bReplicateEndAbility, bool bWasCancelled) override;

public:
    // ========================================
    // ABILITY CONFIGURATION
    // ========================================

    /** Base attack bonus */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    int32 BaseAttackBonus = 5;

    /** Weapon damage expression */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    FString WeaponDamage = "1d8+3";

    /** Whether this attack can critical hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    bool bCanCriticalHit = true;

    /** Critical hit multiplier */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    float CriticalMultiplier = 2.0f;

    /** Use physical dice for this attack */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Attack")
    bool bUsePhysicalDice = false;

    // ========================================
    // GAMEPLAY TAGS
    // ========================================

    /** Tag applied on critical hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag CriticalHitTag;

    /** Tag applied on critical miss */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag CriticalMissTag;

    /** Tag applied on normal hit */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag HitTag;

    /** Tag applied on miss */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Gameplay Tags")
    FGameplayTag MissTag;

protected:
    // ========================================
    // ABILITY IMPLEMENTATION
    // ========================================

    /** Perform attack roll */
    UFUNCTION(BlueprintCallable, Category = "Storm|Attack")
    void PerformAttackRoll();

    /** Handle attack roll result */
    UFUNCTION(BlueprintCallable, Category = "Storm|Attack")
    void HandleAttackResult(const FStormDiceResult& AttackRoll, int32 TargetAC);

    /** Perform damage roll */
    UFUNCTION(BlueprintCallable, Category = "Storm|Attack")
    void PerformDamageRoll(bool bIsCritical = false);

    /** Apply damage to target */
    UFUNCTION(BlueprintCallable, Category = "Storm|Attack")
    void ApplyDamageToTarget(int32 Damage, bool bIsCritical);

    /** Get target actor */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Attack")
    AActor* GetTargetActor() const;

    /** Get target's armor class */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Attack")
    int32 GetTargetArmorClass() const;

    /** Check if attacker has advantage */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Attack")
    bool HasAdvantage() const;

    /** Check if attacker has disadvantage */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Attack")
    bool HasDisadvantage() const;

private:
    /** Current target for this attack */
    UPROPERTY()
    AActor* CurrentTarget = nullptr;

    /** Attack roll result */
    FStormDiceResult AttackRollResult;

    /** Damage roll result */
    FStormDiceResult DamageRollResult;

public:
    // ========================================
    // BLUEPRINT EVENTS
    // ========================================

    /** Blueprint event called when attack starts */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnAttackStarted(AActor* Target);

    /** Blueprint event called when attack roll is made */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnAttackRolled(const FStormDiceResult& Result, int32 TargetAC, bool bHit);

    /** Blueprint event called when damage is rolled */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnDamageRolled(const FStormDiceResult& Result, bool bIsCritical);

    /** Blueprint event called when attack completes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnAttackCompleted(bool bHit, int32 Damage, bool bIsCritical);
};

// ========================================
// BLUEPRINT GRAPH EQUIVALENT (Pseudo-code)
// ========================================

/*
Event Graph for GA_StormAttack:

1. ActivateAbility
   ├── Get Target Actor
   ├── Check if Valid Target
   ├── OnAttackStarted (Blueprint Event)
   └── PerformAttackRoll

2. PerformAttackRoll
   ├── Get Attack Bonus (Base + Modifiers)
   ├── Get Target AC
   ├── Check Advantage/Disadvantage
   ├── Call StormDiceLibrary::RollAttack
   └── HandleAttackResult

3. HandleAttackResult
   ├── OnAttackRolled (Blueprint Event)
   ├── Check if Hit
   ├── Apply Gameplay Tags (Hit/Miss/Critical)
   ├── If Hit: PerformDamageRoll
   └── If Miss: EndAbility

4. PerformDamageRoll
   ├── Get Weapon Damage Expression
   ├── Check if Critical (double dice)
   ├── Call StormDiceLibrary::RollExpression
   ├── OnDamageRolled (Blueprint Event)
   └── ApplyDamageToTarget

5. ApplyDamageToTarget
   ├── Create Gameplay Effect Spec
   ├── Set Damage Amount
   ├── Apply to Target
   ├── OnAttackCompleted (Blueprint Event)
   └── EndAbility

Blueprint Nodes Used:
- Custom Events
- Branch (if/else)
- Get Gameplay Ability System Component
- Apply Gameplay Effect to Target
- Add Gameplay Tag
- Remove Gameplay Tag
- Print String (for debugging)
- Delay (for animation timing)

Variables:
- CurrentTarget (Actor Reference)
- AttackRollResult (Storm Dice Result)
- DamageRollResult (Storm Dice Result)
- BaseAttackBonus (Integer)
- WeaponDamage (String)
- bCanCriticalHit (Boolean)
- CriticalMultiplier (Float)

Functions:
- GetTargetActor (Pure)
- GetTargetArmorClass (Pure)
- HasAdvantage (Pure)
- HasDisadvantage (Pure)
- PerformAttackRoll
- HandleAttackResult
- PerformDamageRoll
- ApplyDamageToTarget

Events:
- OnAttackStarted
- OnAttackRolled
- OnDamageRolled
- OnAttackCompleted
*/
