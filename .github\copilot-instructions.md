# Copilot Instructions for Storm RPG Codebase

## Big Picture Architecture
- **Two main implementations:**
  - **Python (Storm-Meta):** Core RPG logic, character, race, class, and mechanics modules. Follows a modular structure: `core/`, `races/`, `classes/`.
  - **TypeScript (horizon-worlds):** Multiplayer VR adaptation for Meta Horizon Worlds. Organized in `scripts/` by domain (core, classes, spells, items, ui, utils).
- **Data Flow:**
  - Python: Character creation and game logic flow through `core/character.py`, with races and classes injected as dependencies.
  - TypeScript: Game state managed by `GameManager.ts` and `PlayerManager.ts`, with event-driven updates via `EventSystem.ts`.

## Developer Workflows
- **Python:**
  - Install dependencies: `pip install -r requirements.txt`
  - Run web interface (if present): `python web/app.py`
  - Create characters: see example in root `README.md`.
- **TypeScript:**
  - No build system detected; scripts are organized for direct use in Horizon Worlds.
  - Key entry points: `GameManager.ts`, `HUDManager.ts`.

## Project-Specific Conventions
- **Python:**
  - Classes and races are injected into `Character` objects.
  - Dice rolls and mechanics are abstracted in `core/dice.py` and `core/mechanics.py`.
  - Naming: Use English for code, Portuguese for documentation/comments.
- **TypeScript:**
  - Each domain (core, classes, spells, items, ui, utils) has its own folder and base class.
  - Event-driven logic via `EventSystem.ts`.
  - Constants centralized in `Constants.ts`.

## Integration Points & Cross-Component Patterns
- **Python:**
  - Races and classes are plug-and-play via inheritance and composition.
  - Mechanics and attributes are decoupled for easy extension.
- **TypeScript:**
  - Game state and player management are separated (`GameManager.ts`, `PlayerManager.ts`).
  - UI and gameplay logic are modular and communicate via events.

## Key Files & Examples
- Python: `core/character.py`, `races/human.py`, `classes/warrior.py`, `core/dice.py`
- TypeScript: `scripts/core/GameManager.ts`, `scripts/core/PlayerManager.ts`, `scripts/utils/EventSystem.ts`, `scripts/ui/HUDManager.ts`

## Patterns to Follow
- Prefer composition and inheritance for extensibility (see race/class structure).
- Use event-driven updates for game state changes (see `EventSystem.ts`).
- Keep domain logic modular and separated by concern.

---

_If any section is unclear or missing important conventions, please provide feedback to improve these instructions._
