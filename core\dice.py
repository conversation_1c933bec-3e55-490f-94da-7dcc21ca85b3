"""
Storm RPG - Sistema de Dados
Implementa o sistema de dados d20 e outras mecânicas de rolagem
"""

import random
from typing import List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class DiceType(Enum):
    """Tipos de dados disponíveis"""
    D4 = 4
    D6 = 6
    D8 = 8
    D10 = 10
    D12 = 12
    D20 = 20
    D100 = 100


@dataclass
class DiceResult:
    """Resultado de uma rolagem de dados"""
    rolls: List[int]
    modifier: int
    total: int
    dice_type: DiceType
    num_dice: int
    
    @property
    def natural_total(self) -> int:
        """Total sem modificadores"""
        return sum(self.rolls)
    
    @property
    def is_natural_20(self) -> bool:
        """Verifica se houve 20 natural (apenas para d20)"""
        return self.dice_type == DiceType.D20 and 20 in self.rolls
    
    @property
    def is_natural_1(self) -> bool:
        """Verifica se houve 1 natural (apenas para d20)"""
        return self.dice_type == DiceType.D20 and 1 in self.rolls
    
    def __str__(self) -> str:
        rolls_str = "+".join(map(str, self.rolls))
        if self.modifier != 0:
            sign = "+" if self.modifier > 0 else ""
            return f"[{rolls_str}]{sign}{self.modifier} = {self.total}"
        return f"[{rolls_str}] = {self.total}"


class Dice:
    """Classe principal para rolagem de dados"""
    
    @staticmethod
    def roll(num_dice: int = 1, dice_type: DiceType = DiceType.D20, 
             modifier: int = 0, advantage: bool = False, 
             disadvantage: bool = False) -> DiceResult:
        """
        Rola dados com modificadores e vantagem/desvantagem
        
        Args:
            num_dice: Número de dados a rolar
            dice_type: Tipo do dado (d4, d6, d8, d10, d12, d20, d100)
            modifier: Modificador a adicionar
            advantage: Rola 2 dados e pega o maior (apenas d20)
            disadvantage: Rola 2 dados e pega o menor (apenas d20)
        """
        if advantage and disadvantage:
            advantage = disadvantage = False
        
        # Para vantagem/desvantagem, sempre rola 2 d20
        if (advantage or disadvantage) and dice_type == DiceType.D20:
            rolls = [random.randint(1, dice_type.value) for _ in range(2)]
            if advantage:
                final_roll = max(rolls)
            else:  # disadvantage
                final_roll = min(rolls)
            rolls = [final_roll]  # Mantém apenas o resultado final
        else:
            rolls = [random.randint(1, dice_type.value) for _ in range(num_dice)]
        
        total = sum(rolls) + modifier
        
        return DiceResult(
            rolls=rolls,
            modifier=modifier,
            total=total,
            dice_type=dice_type,
            num_dice=num_dice
        )
    
    @staticmethod
    def roll_multiple(dice_expressions: List[str]) -> List[DiceResult]:
        """
        Rola múltiplas expressões de dados
        Exemplo: ["1d20+5", "2d6+3", "1d4"]
        """
        results = []
        for expression in dice_expressions:
            result = Dice.parse_and_roll(expression)
            results.append(result)
        return results
    
    @staticmethod
    def parse_and_roll(expression: str) -> DiceResult:
        """
        Analisa uma expressão de dados e rola
        Formato: "NdX+M" onde N=número de dados, X=tipo, M=modificador
        Exemplos: "1d20+5", "2d6", "1d4-1"
        """
        expression = expression.replace(" ", "").lower()
        
        # Separar modificador
        modifier = 0
        if "+" in expression:
            parts = expression.split("+")
            expression = parts[0]
            modifier = int(parts[1])
        elif "-" in expression and expression.count("-") == 1:
            parts = expression.split("-")
            expression = parts[0]
            modifier = -int(parts[1])
        
        # Separar número de dados e tipo
        if "d" in expression:
            num_str, dice_str = expression.split("d")
            num_dice = int(num_str) if num_str else 1
            dice_value = int(dice_str)
            
            # Encontrar o tipo de dado correspondente
            dice_type = None
            for dt in DiceType:
                if dt.value == dice_value:
                    dice_type = dt
                    break
            
            if dice_type is None:
                raise ValueError(f"Tipo de dado inválido: d{dice_value}")
            
            return Dice.roll(num_dice, dice_type, modifier)
        else:
            raise ValueError(f"Expressão de dado inválida: {expression}")


# Funções de conveniência
def roll_d20(modifier: int = 0, advantage: bool = False, 
             disadvantage: bool = False) -> DiceResult:
    """Rola 1d20 com modificador e vantagem/desvantagem"""
    return Dice.roll(1, DiceType.D20, modifier, advantage, disadvantage)


def roll_dice(expression: str) -> DiceResult:
    """Rola dados usando expressão string (ex: "2d6+3")"""
    return Dice.parse_and_roll(expression)


def roll_initiative(dexterity_modifier: int, level: int = 1) -> DiceResult:
    """Rola iniciativa (1d20 + Des + nível)"""
    return roll_d20(dexterity_modifier + level)


def roll_damage(weapon_damage: str, strength_modifier: int = 0) -> DiceResult:
    """Rola dano de arma com modificador de Força"""
    base_damage = roll_dice(weapon_damage)
    base_damage.modifier += strength_modifier
    base_damage.total += strength_modifier
    return base_damage


# Testes de exemplo
if __name__ == "__main__":
    print("=== Testes do Sistema de Dados ===")
    
    # Teste básico d20
    result = roll_d20(5)
    print(f"1d20+5: {result}")
    
    # Teste com vantagem
    result = roll_d20(3, advantage=True)
    print(f"1d20+3 (vantagem): {result}")
    
    # Teste de dano
    result = roll_damage("1d8", 3)
    print(f"Dano 1d8+3: {result}")
    
    # Teste de expressão
    result = roll_dice("2d6+4")
    print(f"2d6+4: {result}")
    
    # Teste de iniciativa
    result = roll_initiative(2, 5)
    print(f"Iniciativa (Des+2, Nível 5): {result}")
