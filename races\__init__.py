"""
Storm RPG - Sistema de <PERSON> as raças jog<PERSON> A<PERSON>ra
"""

from .base_race import Race, RacialTrait
from .human import Human
from .elf import ForestElf, <PERSON>Elf
from .dwarf import MountainDwarf, HillDwarf
from .goblin import Goblin
from .minotaur import Minotaur
from .amphibian import <PERSON><PERSON>
from .sereian import <PERSON><PERSON><PERSON>
from .tiefling import StormTiefling

__all__ = [
    "Race",
    "RacialTrait", 
    "Human",
    "ForestElf",
    "ShadeElf",
    "MountainDwarf", 
    "HillDwarf",
    "Goblin",
    "Minotaur",
    "Luran",
    "Sereian",
    "StormTiefling"
]

# Dicionário para fácil acesso às raças
AVAILABLE_RACES = {
    "human": Human,
    "forest_elf": ForestElf,
    "shade_elf": <PERSON><PERSON><PERSON>,
    "mountain_dwarf": MountainDwarf,
    "hill_dwarf": HillDwarf,
    "goblin": Goblin,
    "minotaur": Minotaur,
    "luran": <PERSON><PERSON>,
    "sereian": <PERSON><PERSON><PERSON>,
    "storm_tiefling": <PERSON>T<PERSON>ling
}

def get_race_by_name(name: str) -> Race:
    """Retorna uma instância da raça pelo nome"""
    race_class = AVAILABLE_RACES.get(name.lower())
    if race_class:
        return race_class()
    raise ValueError(f"Raça '{name}' não encontrada")

def list_available_races() -> list:
    """Lista todas as raças disponíveis"""
    return list(AVAILABLE_RACES.keys())
