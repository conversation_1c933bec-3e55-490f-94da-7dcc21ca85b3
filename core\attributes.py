"""
Storm RPG - Sistema de Atributos
Implementa os seis atributos básicos e seus modificadores
"""

from dataclasses import dataclass
from typing import Dict, Optional
from enum import Enum


class AttributeType(Enum):
    """Tipos de atributos do jogo"""
    STRENGTH = "strength"      # Força
    DEXTERITY = "dexterity"    # Destreza  
    CONSTITUTION = "constitution"  # Constituição
    INTELLIGENCE = "intelligence"  # Inteligência
    WISDOM = "wisdom"          # Sabedoria
    CHARISMA = "charisma"      # Carisma


@dataclass
class Attribute:
    """Representa um atributo individual"""
    base_value: int
    racial_bonus: int = 0
    enhancement_bonus: int = 0
    temporary_bonus: int = 0
    
    @property
    def total_value(self) -> int:
        """Valor total do atributo"""
        return self.base_value + self.racial_bonus + self.enhancement_bonus + self.temporary_bonus
    
    @property
    def modifier(self) -> int:
        """Modificador do atributo (valor - 10) / 2"""
        return (self.total_value - 10) // 2
    
    def __str__(self) -> str:
        mod_str = f"+{self.modifier}" if self.modifier >= 0 else str(self.modifier)
        return f"{self.total_value} ({mod_str})"


class AttributeSet:
    """Conjunto completo de atributos de um personagem"""
    
    def __init__(self, strength: int = 10, dexterity: int = 10, 
                 constitution: int = 10, intelligence: int = 10,
                 wisdom: int = 10, charisma: int = 10):
        self.attributes = {
            AttributeType.STRENGTH: Attribute(strength),
            AttributeType.DEXTERITY: Attribute(dexterity),
            AttributeType.CONSTITUTION: Attribute(constitution),
            AttributeType.INTELLIGENCE: Attribute(intelligence),
            AttributeType.WISDOM: Attribute(wisdom),
            AttributeType.CHARISMA: Attribute(charisma)
        }
    
    def get_attribute(self, attr_type: AttributeType) -> Attribute:
        """Retorna um atributo específico"""
        return self.attributes[attr_type]
    
    def get_modifier(self, attr_type: AttributeType) -> int:
        """Retorna o modificador de um atributo"""
        return self.attributes[attr_type].modifier
    
    def get_value(self, attr_type: AttributeType) -> int:
        """Retorna o valor total de um atributo"""
        return self.attributes[attr_type].total_value
    
    def apply_racial_bonus(self, attr_type: AttributeType, bonus: int):
        """Aplica bônus racial a um atributo"""
        self.attributes[attr_type].racial_bonus += bonus
    
    def apply_enhancement_bonus(self, attr_type: AttributeType, bonus: int):
        """Aplica bônus de aprimoramento (itens mágicos)"""
        self.attributes[attr_type].enhancement_bonus = bonus
    
    def apply_temporary_bonus(self, attr_type: AttributeType, bonus: int):
        """Aplica bônus temporário (magias, efeitos)"""
        self.attributes[attr_type].temporary_bonus += bonus
    
    def remove_temporary_bonuses(self):
        """Remove todos os bônus temporários"""
        for attr in self.attributes.values():
            attr.temporary_bonus = 0
    
    @property
    def point_buy_cost(self) -> int:
        """Calcula o custo em pontos para compra de atributos"""
        cost_table = {
            8: 0, 9: 1, 10: 2, 11: 3, 12: 4, 13: 5,
            14: 7, 15: 9, 16: 12, 17: 15, 18: 19
        }
        
        total_cost = 0
        for attr in self.attributes.values():
            base = attr.base_value
            if base in cost_table:
                total_cost += cost_table[base]
            else:
                # Valores fora da tabela custam muito caro
                total_cost += 25
        
        return total_cost
    
    def __str__(self) -> str:
        lines = []
        for attr_type, attr in self.attributes.items():
            name = attr_type.value.capitalize()
            lines.append(f"{name}: {attr}")
        return "\n".join(lines)


class Modifier:
    """Classe para calcular modificadores diversos"""
    
    @staticmethod
    def attribute_modifier(value: int) -> int:
        """Calcula modificador de atributo"""
        return (value - 10) // 2
    
    @staticmethod
    def size_modifier(size: str) -> Dict[str, int]:
        """Retorna modificadores por tamanho"""
        size_mods = {
            "minúsculo": {"ac": 2, "attack": 2, "stealth": 8},
            "miúdo": {"ac": 1, "attack": 1, "stealth": 4},
            "pequeno": {"ac": 1, "attack": 1, "stealth": 4},
            "médio": {"ac": 0, "attack": 0, "stealth": 0},
            "grande": {"ac": -1, "attack": -1, "stealth": -4},
            "enorme": {"ac": -2, "attack": -2, "stealth": -8},
            "colossal": {"ac": -4, "attack": -4, "stealth": -12}
        }
        return size_mods.get(size.lower(), size_mods["médio"])
    
    @staticmethod
    def level_bonus(level: int) -> int:
        """Bônus de nível para testes"""
        return level // 2


# Funções de conveniência para geração de atributos
def roll_attributes() -> AttributeSet:
    """Gera atributos aleatoriamente (4d6, descarta menor)"""
    from .dice import Dice, DiceType
    
    def roll_single_attribute() -> int:
        # Rola 4d6, descarta o menor
        rolls = [Dice.roll(1, DiceType.D6).total for _ in range(4)]
        rolls.sort(reverse=True)
        return sum(rolls[:3])  # Soma os 3 maiores
    
    return AttributeSet(
        strength=roll_single_attribute(),
        dexterity=roll_single_attribute(),
        constitution=roll_single_attribute(),
        intelligence=roll_single_attribute(),
        wisdom=roll_single_attribute(),
        charisma=roll_single_attribute()
    )


def standard_array() -> AttributeSet:
    """Retorna o array padrão de atributos"""
    return AttributeSet(15, 14, 13, 12, 10, 8)


def point_buy_attributes(strength: int = 8, dexterity: int = 8,
                        constitution: int = 8, intelligence: int = 8,
                        wisdom: int = 8, charisma: int = 8) -> AttributeSet:
    """Cria atributos usando sistema de compra por pontos"""
    attrs = AttributeSet(strength, dexterity, constitution, 
                        intelligence, wisdom, charisma)
    
    # Verifica se está dentro do limite de pontos (geralmente 25)
    if attrs.point_buy_cost > 25:
        raise ValueError(f"Custo de {attrs.point_buy_cost} pontos excede o limite de 25")
    
    return attrs


# Teste de exemplo
if __name__ == "__main__":
    print("=== Testes do Sistema de Atributos ===")
    
    # Teste básico
    attrs = AttributeSet(16, 14, 13, 12, 10, 8)
    print("Atributos básicos:")
    print(attrs)
    print(f"Custo em pontos: {attrs.point_buy_cost}")
    
    # Teste com bônus racial
    attrs.apply_racial_bonus(AttributeType.STRENGTH, 2)
    attrs.apply_racial_bonus(AttributeType.CONSTITUTION, 1)
    print(f"\nCom bônus racial (+2 For, +1 Con):")
    print(f"Força: {attrs.get_attribute(AttributeType.STRENGTH)}")
    print(f"Constituição: {attrs.get_attribute(AttributeType.CONSTITUTION)}")
    
    # Teste de array padrão
    print(f"\nArray padrão:")
    standard = standard_array()
    print(standard)
