"""
Storm RPG - Classe Base de Raça
Define a estrutura básica para todas as raças
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from enum import Enum
from core.attributes import AttributeType


class TraitType(Enum):
    """Tipos de características raciais"""
    ATTRIBUTE_BONUS = "attribute_bonus"
    SKILL_BONUS = "skill_bonus"
    RESISTANCE = "resistance"
    IMMUNITY = "immunity"
    SPECIAL_SENSE = "special_sense"
    MOVEMENT = "movement"
    LANGUAGE = "language"
    SPELL_LIKE = "spell_like"
    NATURAL_WEAPON = "natural_weapon"
    SIZE_MODIFIER = "size_modifier"
    OTHER = "other"


@dataclass
class RacialTrait:
    """Representa uma característica racial"""
    name: str
    description: str
    trait_type: TraitType
    value: Any = None
    
    def apply_to_character(self, character):
        """Aplica a característica ao personagem"""
        if self.trait_type == TraitType.ATTRIBUTE_BONUS:
            attr_type, bonus = self.value
            character.attributes.apply_racial_bonus(attr_type, bonus)
        
        elif self.trait_type == TraitType.SKILL_BONUS:
            skill_name, bonus = self.value
            if skill_name in character.skills:
                character.skills[skill_name].ranks += bonus
        
        elif self.trait_type == TraitType.LANGUAGE:
            if self.value not in character.languages:
                character.languages.append(self.value)
        
        elif self.trait_type == TraitType.MOVEMENT:
            movement_type, speed = self.value
            if movement_type == "base":
                character.speed = speed
            # Outros tipos de movimento podem ser adicionados aqui
        
        elif self.trait_type == TraitType.SPECIAL_SENSE:
            # Adiciona sentidos especiais como características
            character.add_feature(f"{self.name}: {self.description}")
        
        elif self.trait_type == TraitType.RESISTANCE:
            character.add_feature(f"Resistência a {self.value}: {self.description}")
        
        elif self.trait_type == TraitType.IMMUNITY:
            character.add_feature(f"Imunidade a {self.value}: {self.description}")
        
        elif self.trait_type == TraitType.NATURAL_WEAPON:
            character.add_feature(f"Arma Natural - {self.name}: {self.description}")
        
        elif self.trait_type == TraitType.SPELL_LIKE:
            character.add_feature(f"Habilidade Mágica - {self.name}: {self.description}")
        
        else:
            # Outras características são adicionadas como features gerais
            character.add_feature(f"{self.name}: {self.description}")


class Race(ABC):
    """Classe base abstrata para todas as raças"""
    
    def __init__(self):
        self.name = self.get_name()
        self.description = self.get_description()
        self.size = self.get_size()
        self.base_speed = self.get_base_speed()
        self.traits = self.get_racial_traits()
        self.languages = self.get_languages()
    
    @abstractmethod
    def get_name(self) -> str:
        """Nome da raça"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Descrição da raça"""
        pass
    
    @abstractmethod
    def get_size(self) -> str:
        """Tamanho da raça"""
        pass
    
    @abstractmethod
    def get_base_speed(self) -> int:
        """Velocidade base em metros"""
        pass
    
    @abstractmethod
    def get_racial_traits(self) -> List[RacialTrait]:
        """Lista de características raciais"""
        pass
    
    @abstractmethod
    def get_languages(self) -> List[str]:
        """Idiomas conhecidos"""
        pass
    
    def apply_to_character(self, character):
        """Aplica todas as características raciais ao personagem"""
        # Aplicar velocidade base
        character.speed = self.base_speed
        
        # Aplicar idiomas
        for language in self.languages:
            if language not in character.languages:
                character.languages.append(language)
        
        # Aplicar características raciais
        for trait in self.traits:
            trait.apply_to_character(character)
        
        # Definir raça no personagem
        character.race = self
    
    def get_trait_by_name(self, name: str) -> Optional[RacialTrait]:
        """Busca uma característica pelo nome"""
        for trait in self.traits:
            if trait.name.lower() == name.lower():
                return trait
        return None
    
    def has_trait(self, name: str) -> bool:
        """Verifica se a raça possui uma característica"""
        return self.get_trait_by_name(name) is not None
    
    def get_attribute_bonuses(self) -> Dict[AttributeType, int]:
        """Retorna todos os bônus de atributo da raça"""
        bonuses = {}
        for trait in self.traits:
            if trait.trait_type == TraitType.ATTRIBUTE_BONUS:
                attr_type, bonus = trait.value
                bonuses[attr_type] = bonuses.get(attr_type, 0) + bonus
        return bonuses
    
    def __str__(self) -> str:
        return f"{self.name} - {self.description}"
    
    def get_summary(self) -> str:
        """Retorna um resumo da raça"""
        lines = [
            f"=== {self.name} ===",
            f"Tamanho: {self.size}",
            f"Velocidade: {self.base_speed}m",
            f"Idiomas: {', '.join(self.languages)}",
            "",
            "Características Raciais:"
        ]
        
        for trait in self.traits:
            lines.append(f"• {trait.name}: {trait.description}")
        
        return "\n".join(lines)


# Características raciais comuns que podem ser reutilizadas
def create_attribute_bonus(attr_type: AttributeType, bonus: int, name: str = None) -> RacialTrait:
    """Cria uma característica de bônus de atributo"""
    if name is None:
        attr_name = attr_type.value.capitalize()
        name = f"+{bonus} {attr_name}"
    
    return RacialTrait(
        name=name,
        description=f"Recebe +{bonus} de bônus racial em {attr_type.value.capitalize()}",
        trait_type=TraitType.ATTRIBUTE_BONUS,
        value=(attr_type, bonus)
    )

def create_darkvision(range_meters: int = 18) -> RacialTrait:
    """Cria característica de visão no escuro"""
    return RacialTrait(
        name="Visão no Escuro",
        description=f"Pode ver no escuro até {range_meters}m como se fosse penumbra",
        trait_type=TraitType.SPECIAL_SENSE,
        value=range_meters
    )

def create_resistance(damage_type: str) -> RacialTrait:
    """Cria característica de resistência"""
    return RacialTrait(
        name=f"Resistência a {damage_type}",
        description=f"Recebe metade do dano de {damage_type}",
        trait_type=TraitType.RESISTANCE,
        value=damage_type
    )

def create_skill_bonus(skill_name: str, bonus: int) -> RacialTrait:
    """Cria bônus racial em perícia"""
    return RacialTrait(
        name=f"Aptidão em {skill_name}",
        description=f"Recebe +{bonus} de bônus racial em testes de {skill_name}",
        trait_type=TraitType.SKILL_BONUS,
        value=(skill_name, bonus)
    )
