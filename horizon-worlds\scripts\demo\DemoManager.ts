/**
 * Storm RPG - Demo Manager
 * Gerencia a experiência de demonstração do mundo Arton
 */

import { PlayerManager } from '../core/PlayerManager';
import { stormEvents } from '../utils/EventSystem';
import { UI_CONSTANTS } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Players: any;
declare const UI: any;

export interface DemoProgress {
    playerId: string;
    startTime: number;
    currentStep: number;
    completedSteps: string[];
    npcInteractionSequence: string[];
    questCompleted: boolean;
}

export class DemoManager {
    private demoSessions: Map<string, DemoProgress> = new Map();
    private demoSteps: string[] = [
        'tutorial_start',
        'meet_lisandra',
        'explore_tollon',
        'find_crystal',
        'activate_crystal',
        'complete_quest'
    ];

    constructor() {
        this.setupDemoEnvironment();
        this.setupEventListeners();
        console.log("[DemoManager] Sistema de demo inicializado");
    }

    /**
     * Configura o ambiente específico para demo
     */
    private setupDemoEnvironment(): void {
        // Otimizar para performance mobile
        this.optimizeForMobile();
        
        // Configurar zonas limitadas
        this.setupDemoZones();
        
        // Criar elementos específicos da demo
        this.createDemoElements();
    }

    /**
     * Otimizações para dispositivos móveis
     */
    private optimizeForMobile(): void {
        // Reduzir qualidade de partículas
        const particleSettings = {
            maxParticles: 50,
            quality: 'medium',
            enableShadows: false
        };

        // Desabilitar zonas não essenciais
        const zonesToDisable = [
            'Montanhas_Uivantes',
            'Deserto_Zalyra',
            'Pantano_Juncos',
            'Vale_Drais'
        ];

        zonesToDisable.forEach(zoneName => {
            const zone = Scene.root.findEntityByName(zoneName);
            if (zone) {
                zone.setActive(false);
            }
        });

        console.log("[DemoManager] Otimizações mobile aplicadas");
    }

    /**
     * Configura zonas limitadas para demo
     */
    private setupDemoZones(): void {
        // Manter apenas Khalmyr e parte de Tollon
        const demoZones = [
            {
                name: 'Khalmyr_Demo',
                center: { x: 0, y: 0, z: 0 },
                radius: 15,
                description: 'Centro de Khalmyr - Ponto de partida'
            },
            {
                name: 'Tollon_Demo',
                center: { x: 25, y: 0, z: 0 },
                radius: 20,
                description: 'Entrada da Floresta de Tollon'
            },
            {
                name: 'Crystal_Area',
                center: { x: 35, y: 0, z: 10 },
                radius: 8,
                description: 'Local do Cristal da Storm'
            }
        ];

        demoZones.forEach(zone => {
            this.createZoneMarker(zone);
        });
    }

    /**
     * Cria marcadores visuais das zonas
     */
    private createZoneMarker(zone: any): void {
        const marker = Scene.createRootEntity();
        marker.setPosition(zone.center.x, zone.center.y + 0.1, zone.center.z);
        marker.name = `${zone.name}_Marker`;

        // Círculo no chão indicando a zona
        const circle = marker.createComponent("engine:mesh", {
            assetId: "zone_circle_model"
        });

        // Efeito de partículas sutil
        const particles = marker.createComponent("engine:persistentParticleEffect", {
            assetId: "zone_glow_particles"
        });
    }

    /**
     * Cria elementos específicos da demo
     */
    private createDemoElements(): void {
        // Cristal da Storm (objetivo principal)
        this.createStormCrystal();
        
        // Placas informativas
        this.createInfoSigns();
        
        // Portal de saída (aparece no final)
        this.createExitPortal();
    }

    /**
     * Cria o Cristal da Storm
     */
    private createStormCrystal(): void {
        const crystal = Scene.createRootEntity();
        crystal.setPosition(35, 1, 10);
        crystal.name = "Storm_Crystal";

        // Modelo do cristal
        const mesh = crystal.createComponent("engine:mesh", {
            assetId: "storm_crystal_model"
        });

        // Efeito de brilho
        const glow = crystal.createComponent("engine:persistentParticleEffect", {
            assetId: "crystal_glow_effect"
        });

        // Som ambiente
        const ambientSound = crystal.createComponent("engine:audioSource", {
            assetId: "crystal_hum_sound",
            loop: true,
            volume: 0.3,
            spatialAudio: true
        });
        ambientSound.play();

        // Trigger de interação
        const trigger = crystal.createComponent("engine:trigger", {
            shape: "sphere",
            radius: 3.0
        });

        trigger.onTriggerEnter.add((player: any) => {
            this.onCrystalInteraction(player);
        });

        // Animação de rotação
        this.animateCrystal(crystal);
    }

    /**
     * Anima o cristal da Storm
     */
    private animateCrystal(crystal: any): void {
        let rotation = 0;
        
        setInterval(() => {
            rotation += 1;
            crystal.setRotation(0, rotation, 0);
        }, 50);
    }

    /**
     * Cria placas informativas
     */
    private createInfoSigns(): void {
        const signs = [
            {
                position: { x: 5, y: 1, z: 0 },
                text: "Bem-vindo a Arton!\nSiga para a Floresta de Tollon"
            },
            {
                position: { x: 20, y: 1, z: 0 },
                text: "Floresta de Tollon\nProcure por Lisandra, a Druida"
            },
            {
                position: { x: 30, y: 1, z: 5 },
                text: "Cristal da Storm\nToque para ativar"
            }
        ];

        signs.forEach((signData, index) => {
            const sign = Scene.createRootEntity();
            sign.setPosition(signData.position.x, signData.position.y, signData.position.z);
            sign.name = `Info_Sign_${index}`;

            // Modelo da placa
            const signMesh = sign.createComponent("engine:mesh", {
                assetId: "wooden_sign_model"
            });

            // Texto da placa
            const text = sign.createComponent("engine:text", {
                text: signData.text,
                fontSize: 0.3,
                color: { r: 0.9, g: 0.9, b: 0.9, a: 1 },
                billboard: true
            });
        });
    }

    /**
     * Cria portal de saída (inicialmente invisível)
     */
    private createExitPortal(): void {
        const portal = Scene.createRootEntity();
        portal.setPosition(0, 1, -10);
        portal.name = "Exit_Portal";
        portal.setActive(false); // Inicialmente invisível

        // Efeito visual do portal
        const portalEffect = portal.createComponent("engine:persistentParticleEffect", {
            assetId: "portal_effect"
        });

        // Som do portal
        const portalSound = portal.createComponent("engine:audioSource", {
            assetId: "portal_sound",
            loop: true,
            volume: 0.5
        });

        // Trigger de saída
        const trigger = portal.createComponent("engine:trigger", {
            shape: "sphere",
            radius: 2.0
        });

        trigger.onTriggerEnter.add((player: any) => {
            this.onPortalExit(player);
        });
    }

    /**
     * Configura listeners de eventos
     */
    private setupEventListeners(): void {
        // Jogador entra no mundo
        stormEvents.onPlayerJoined((playerId, playerName) => {
            this.startDemo(playerId, playerName);
        });

        // Jogador sai do mundo
        stormEvents.eventSystem.on('player_left', (data) => {
            this.endDemo(data.playerId);
        });

        // Interação com NPCs
        stormEvents.eventSystem.on('npc_interaction', (data) => {
            this.onNPCInteraction(data.playerId, data.npcName);
        });
    }

    /**
     * Inicia a demo para um jogador
     */
    private startDemo(playerId: string, playerName: string): void {
        const demoProgress: DemoProgress = {
            playerId,
            startTime: Date.now(),
            currentStep: 0,
            completedSteps: [],
            npcInteractionSequence: [],
            questCompleted: false
        };

        this.demoSessions.set(playerId, demoProgress);

        // Mostrar tutorial inicial
        this.showTutorialGuide(playerId, playerName);

        // Criar HUD da demo
        this.createDemoHUD(playerId);

        console.log(`[DemoManager] Demo iniciada para ${playerName}`);
    }

    /**
     * Mostra o guia tutorial inicial
     */
    private showTutorialGuide(playerId: string, playerName: string): void {
        const messages = [
            `Bem-vindo a Arton, ${playerName}!`,
            "Você é um aventureiro marcado pela Storm.",
            "Explore o mundo tocando nos NPCs e objetos.",
            "Sua missão: ativar o Cristal da Storm na floresta.",
            "Boa sorte, herói!"
        ];

        let messageIndex = 0;
        const showNextMessage = () => {
            if (messageIndex < messages.length) {
                this.showNotification(playerId, messages[messageIndex], 4000);
                messageIndex++;
                setTimeout(showNextMessage, 4500);
            } else {
                this.advanceStep(playerId, 'tutorial_start');
            }
        };

        showNextMessage();
    }

    /**
     * Cria HUD específico da demo
     */
    private createDemoHUD(playerId: string): void {
        // HUD será criado via UI system
        const hudData = {
            playerId,
            elements: {
                objective: "Encontre Lisandra na Floresta de Tollon",
                timer: "00:00",
                progress: "1/6"
            }
        };

        // Emitir evento para criar HUD
        stormEvents.eventSystem.emit('create_demo_hud', hudData);
    }

    /**
     * Avança para o próximo passo da demo
     */
    private advanceStep(playerId: string, stepName: string): void {
        const progress = this.demoSessions.get(playerId);
        if (!progress) return;

        if (!progress.completedSteps.includes(stepName)) {
            progress.completedSteps.push(stepName);
            progress.currentStep++;

            // Atualizar HUD
            this.updateDemoHUD(playerId);

            // Verificar se completou todos os passos
            if (progress.currentStep >= this.demoSteps.length) {
                this.completeDemo(playerId);
            }
        }
    }

    /**
     * Atualiza o HUD da demo
     */
    private updateDemoHUD(playerId: string): void {
        const progress = this.demoSessions.get(playerId);
        if (!progress) return;

        const elapsed = Date.now() - progress.startTime;
        const minutes = Math.floor(elapsed / 60000);
        const seconds = Math.floor((elapsed % 60000) / 1000);
        const timer = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        const objectives = [
            "Tutorial concluído",
            "Encontre Lisandra",
            "Explore a Floresta de Tollon",
            "Localize o Cristal da Storm",
            "Ative o Cristal da Storm",
            "Missão completa!"
        ];

        const currentObjective = objectives[progress.currentStep] || "Missão completa!";

        // Emitir evento para atualizar HUD
        stormEvents.eventSystem.emit('update_demo_hud', {
            playerId,
            objective: currentObjective,
            timer,
            progress: `${progress.currentStep}/${this.demoSteps.length}`
        });
    }

    /**
     * Manipula interação com NPCs
     */
    private onNPCInteraction(playerId: string, npcName: string): void {
        const progress = this.demoSessions.get(playerId);
        if (!progress) return;

        progress.npcInteractionSequence.push(npcName);

        // Verificar sequência especial (Easter Egg)
        this.checkEasterEgg(playerId);

        // Avançar passos baseado no NPC
        switch (npcName) {
            case 'Lisandra':
                this.advanceStep(playerId, 'meet_lisandra');
                this.showNotification(playerId, "Lisandra: O Cristal da Storm precisa ser ativado!", 5000);
                break;
            case 'Rodleck':
                this.showNotification(playerId, "Rodleck: Cuidado na floresta, aventureiro.", 3000);
                break;
        }
    }

    /**
     * Verifica sequência do Easter Egg
     */
    private checkEasterEgg(playerId: string): void {
        const progress = this.demoSessions.get(playerId);
        if (!progress) return;

        const sequence = progress.npcInteractionSequence;
        const secretSequence = ['Lisandra', 'Rodleck', 'Beluhga'];

        // Verificar se a sequência corresponde
        if (sequence.length >= 3) {
            const lastThree = sequence.slice(-3);
            if (JSON.stringify(lastThree) === JSON.stringify(secretSequence)) {
                this.triggerEasterEgg(playerId);
            }
        }
    }

    /**
     * Dispara o Easter Egg
     */
    private triggerEasterEgg(playerId: string): void {
        // Ativar portal especial
        const secretPortal = Scene.createRootEntity();
        secretPortal.setPosition(10, 1, 10);
        secretPortal.name = "Secret_Portal";

        // Efeito dourado especial
        const effect = secretPortal.createComponent("engine:persistentParticleEffect", {
            assetId: "golden_portal_effect"
        });

        // Som místico
        const sound = secretPortal.createComponent("engine:audioSource", {
            assetId: "mystic_chime_sound",
            volume: 0.8
        });
        sound.play();

        this.showNotification(playerId, "🌟 Portal Secreto Desbloqueado! A verdadeira jornada começa agora...", 8000);

        // Remover portal após 30 segundos
        setTimeout(() => {
            secretPortal.destroy();
        }, 30000);
    }

    /**
     * Manipula interação com o cristal
     */
    private onCrystalInteraction(player: any): void {
        const playerId = player.id;
        const progress = this.demoSessions.get(playerId);
        if (!progress || progress.questCompleted) return;

        // Ativar cristal
        this.activateStormCrystal(playerId);
        
        // Marcar quest como completa
        progress.questCompleted = true;
        this.advanceStep(playerId, 'activate_crystal');
        this.advanceStep(playerId, 'complete_quest');
    }

    /**
     * Ativa o Cristal da Storm
     */
    private activateStormCrystal(playerId: string): void {
        const crystal = Scene.root.findEntityByName("Storm_Crystal");
        if (!crystal) return;

        // Efeito de ativação
        const activationEffect = Scene.createRootEntity();
        activationEffect.setPosition(35, 2, 10);

        const particles = activationEffect.createComponent("engine:persistentParticleEffect", {
            assetId: "crystal_activation_effect"
        });

        // Som de ativação
        const sound = activationEffect.createComponent("engine:audioSource", {
            assetId: "crystal_activate_sound",
            volume: 1.0
        });
        sound.play();

        // Luz intensa
        const light = activationEffect.createComponent("engine:light", {
            type: "point",
            color: { r: 0.8, g: 0.9, b: 1.0 },
            intensity: 5.0,
            range: 20.0
        });

        // Notificação
        this.showNotification(playerId, "✨ Cristal da Storm Ativado! A energia flui pela floresta!", 6000);

        // Remover efeito após 5 segundos
        setTimeout(() => {
            activationEffect.destroy();
        }, 5000);
    }

    /**
     * Completa a demo
     */
    private completeDemo(playerId: string): void {
        const progress = this.demoSessions.get(playerId);
        if (!progress) return;

        const totalTime = Date.now() - progress.startTime;
        const minutes = Math.floor(totalTime / 60000);
        const seconds = Math.floor((totalTime % 60000) / 1000);

        // Mostrar tela de conclusão
        this.showCompletionScreen(playerId, minutes, seconds);

        // Ativar portal de saída
        const exitPortal = Scene.root.findEntityByName("Exit_Portal");
        if (exitPortal) {
            exitPortal.setActive(true);
        }

        console.log(`[DemoManager] Demo completa para ${playerId} em ${minutes}:${seconds}`);
    }

    /**
     * Mostra tela de conclusão
     */
    private showCompletionScreen(playerId: string, minutes: number, seconds: number): void {
        const messages = [
            "🎉 DEMO CONCLUÍDA! 🎉",
            `Tempo total: ${minutes}:${seconds.toString().padStart(2, '0')}`,
            "Você ativou o Cristal da Storm e sentiu o poder da magia!",
            "Este mundo é totalmente remixável.",
            "Clone-o e crie sua própria aventura em Arton!",
            "Obrigado por jogar! ⚡"
        ];

        let messageIndex = 0;
        const showNextMessage = () => {
            if (messageIndex < messages.length) {
                this.showNotification(playerId, messages[messageIndex], 5000);
                messageIndex++;
                setTimeout(showNextMessage, 5500);
            }
        };

        showNextMessage();
    }

    /**
     * Manipula saída pelo portal
     */
    private onPortalExit(player: any): void {
        const playerId = player.id;
        
        this.showNotification(playerId, "Até a próxima aventura em Arton! 🌟", 3000);
        
        // Opcional: teleportar para spawn ou outro mundo
        setTimeout(() => {
            // player.teleport(spawnPosition);
        }, 3000);
    }

    /**
     * Termina a demo para um jogador
     */
    private endDemo(playerId: string): void {
        this.demoSessions.delete(playerId);
        console.log(`[DemoManager] Demo finalizada para ${playerId}`);
    }

    /**
     * Mostra notificação para um jogador
     */
    private showNotification(playerId: string, message: string, duration: number = 3000): void {
        stormEvents.eventSystem.emit('show_notification', {
            playerId,
            message,
            duration
        });
    }

    /**
     * Obtém progresso da demo de um jogador
     */
    public getDemoProgress(playerId: string): DemoProgress | undefined {
        return this.demoSessions.get(playerId);
    }

    /**
     * Força conclusão da demo (para testes)
     */
    public forceCompleteDemo(playerId: string): void {
        this.completeDemo(playerId);
    }

    /**
     * Reinicia demo para um jogador
     */
    public restartDemo(playerId: string): void {
        this.endDemo(playerId);
        // Jogador precisará sair e entrar novamente
    }
}

// Instância global do gerenciador de demo
export const demoManager = new DemoManager();
