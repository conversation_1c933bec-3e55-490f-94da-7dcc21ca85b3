/**
 * Storm RPG - Attribute System
 * Sistema de atributos e modificadores para personagens
 */

import { CLASS_CONFIGS, PlayerClass } from '../utils/Constants';
import { DiceRoller } from '../utils/DiceRoller';

export interface Attributes {
    strength: number;
    dexterity: number;
    constitution: number;
    intelligence: number;
    wisdom: number;
    charisma: number;
}

export interface DerivedStats {
    hitPoints: number;
    maxHitPoints: number;
    manaPoints: number;
    maxManaPoints: number;
    armorClass: number;
    initiative: number;
    speed: number;
}

export class AttributeSystem {
    /**
     * Calcula modificador de atributo
     */
    static getModifier(attributeValue: number): number {
        return Math.floor((attributeValue - 10) / 2);
    }

    /**
     * Calcula todos os modificadores de uma vez
     */
    static getAllModifiers(attributes: Attributes): Record<keyof Attributes, number> {
        return {
            strength: this.getModifier(attributes.strength),
            dexterity: this.getModifier(attributes.dexterity),
            constitution: this.getModifier(attributes.constitution),
            intelligence: this.getModifier(attributes.intelligence),
            wisdom: this.getModifier(attributes.wisdom),
            charisma: this.getModifier(attributes.charisma)
        };
    }

    /**
     * Gera atributos baseados na classe
     */
    static generateClassAttributes(playerClass: PlayerClass): Attributes {
        const classConfig = CLASS_CONFIGS[playerClass];
        return { ...classConfig.baseAttributes };
    }

    /**
     * Gera atributos aleatórios
     */
    static generateRandomAttributes(): Attributes {
        return DiceRoller.generateAttributeSet();
    }

    /**
     * Aplica bônus racial aos atributos
     */
    static applyRacialBonuses(
        attributes: Attributes, 
        racialBonuses: Partial<Attributes>
    ): Attributes {
        return {
            strength: attributes.strength + (racialBonuses.strength || 0),
            dexterity: attributes.dexterity + (racialBonuses.dexterity || 0),
            constitution: attributes.constitution + (racialBonuses.constitution || 0),
            intelligence: attributes.intelligence + (racialBonuses.intelligence || 0),
            wisdom: attributes.wisdom + (racialBonuses.wisdom || 0),
            charisma: attributes.charisma + (racialBonuses.charisma || 0)
        };
    }

    /**
     * Calcula estatísticas derivadas
     */
    static calculateDerivedStats(
        attributes: Attributes, 
        playerClass: PlayerClass, 
        level: number = 1
    ): DerivedStats {
        const modifiers = this.getAllModifiers(attributes);
        const classConfig = CLASS_CONFIGS[playerClass];

        // Pontos de vida
        const baseHP = classConfig.hitDie + modifiers.constitution;
        const maxHitPoints = baseHP + ((level - 1) * (Math.floor(classConfig.hitDie / 2) + 1 + modifiers.constitution));

        // Pontos de mana (apenas para conjuradores)
        let maxManaPoints = 0;
        if (classConfig.spellcasting) {
            const spellcastingMod = modifiers[classConfig.primaryAttribute as keyof typeof modifiers];
            maxManaPoints = (level * 2) + (spellcastingMod * level);
            maxManaPoints = Math.max(0, maxManaPoints);
        }

        // Classe de Armadura (10 + Des + armadura)
        const armorClass = 10 + modifiers.dexterity;

        // Iniciativa
        const initiative = modifiers.dexterity + Math.floor(level / 2);

        // Velocidade base (9 metros)
        const speed = 9;

        return {
            hitPoints: maxHitPoints,
            maxHitPoints,
            manaPoints: maxManaPoints,
            maxManaPoints,
            armorClass,
            initiative,
            speed
        };
    }
}

export class PlayerStats {
    public attributes: Attributes;
    public derivedStats: DerivedStats;
    public level: number;
    public experience: number;
    public playerClass: PlayerClass;
    public classConfig: any;

    constructor(playerClass: PlayerClass, customAttributes?: Attributes) {
        this.playerClass = playerClass;
        this.classConfig = CLASS_CONFIGS[playerClass];
        this.level = 1;
        this.experience = 0;

        // Usar atributos customizados ou gerar baseados na classe
        this.attributes = customAttributes || AttributeSystem.generateClassAttributes(playerClass);
        
        // Calcular estatísticas derivadas
        this.derivedStats = AttributeSystem.calculateDerivedStats(
            this.attributes, 
            this.playerClass, 
            this.level
        );
    }

    /**
     * Obtém modificador de um atributo específico
     */
    getAttributeModifier(attribute: keyof Attributes): number {
        return AttributeSystem.getModifier(this.attributes[attribute]);
    }

    /**
     * Obtém o modificador do atributo primário da classe
     */
    getPrimaryAttributeModifier(): number {
        const primaryAttr = this.classConfig.primaryAttribute as keyof Attributes;
        return this.getAttributeModifier(primaryAttr);
    }

    /**
     * Atualiza estatísticas derivadas
     */
    updateDerivedStats(): void {
        this.derivedStats = AttributeSystem.calculateDerivedStats(
            this.attributes, 
            this.playerClass, 
            this.level
        );
    }

    /**
     * Adiciona experiência e verifica level up
     */
    addExperience(xp: number): boolean {
        this.experience += xp;
        const newLevel = this.calculateLevelFromXP();
        
        if (newLevel > this.level) {
            this.levelUp(newLevel);
            return true;
        }
        
        return false;
    }

    /**
     * Calcula nível baseado na experiência
     */
    private calculateLevelFromXP(): number {
        // Progressão simples: 1000 XP por nível
        return Math.min(10, Math.floor(this.experience / 1000) + 1);
    }

    /**
     * Sobe de nível
     */
    private levelUp(newLevel: number): void {
        const oldLevel = this.level;
        this.level = newLevel;
        
        // Recalcular estatísticas
        this.updateDerivedStats();
        
        // Curar completamente no level up
        this.derivedStats.hitPoints = this.derivedStats.maxHitPoints;
        this.derivedStats.manaPoints = this.derivedStats.maxManaPoints;
        
        console.log(`Level Up! ${oldLevel} → ${newLevel}`);
    }

    /**
     * Aplica dano
     */
    takeDamage(damage: number): number {
        const actualDamage = Math.min(damage, this.derivedStats.hitPoints);
        this.derivedStats.hitPoints -= actualDamage;
        return actualDamage;
    }

    /**
     * Aplica cura
     */
    heal(healing: number): number {
        const actualHealing = Math.min(
            healing, 
            this.derivedStats.maxHitPoints - this.derivedStats.hitPoints
        );
        this.derivedStats.hitPoints += actualHealing;
        return actualHealing;
    }

    /**
     * Gasta mana
     */
    spendMana(cost: number): boolean {
        if (this.derivedStats.manaPoints >= cost) {
            this.derivedStats.manaPoints -= cost;
            return true;
        }
        return false;
    }

    /**
     * Restaura mana
     */
    restoreMana(amount: number): number {
        const actualRestore = Math.min(
            amount, 
            this.derivedStats.maxManaPoints - this.derivedStats.manaPoints
        );
        this.derivedStats.manaPoints += actualRestore;
        return actualRestore;
    }

    /**
     * Verifica se está vivo
     */
    isAlive(): boolean {
        return this.derivedStats.hitPoints > 0;
    }

    /**
     * Verifica se pode conjurar magias
     */
    canCastSpells(): boolean {
        return this.classConfig.spellcasting;
    }

    /**
     * Obtém bônus de ataque base
     */
    getBaseAttackBonus(): number {
        // Progressão baseada na classe
        switch (this.playerClass) {
            case PlayerClass.WARRIOR:
                return this.level; // Progressão completa
            case PlayerClass.ROGUE:
                return Math.floor(this.level * 0.75); // Progressão média
            case PlayerClass.MAGE:
                return Math.floor(this.level * 0.5); // Progressão baixa
            default:
                return Math.floor(this.level * 0.5);
        }
    }

    /**
     * Obtém bônus de resistência
     */
    getSaveBonus(saveType: 'fortitude' | 'reflex' | 'will'): number {
        const baseBonus = Math.floor(this.level / 3);
        
        switch (this.playerClass) {
            case PlayerClass.WARRIOR:
                if (saveType === 'fortitude') return baseBonus + 2;
                break;
            case PlayerClass.ROGUE:
                if (saveType === 'reflex') return baseBonus + 2;
                break;
            case PlayerClass.MAGE:
                if (saveType === 'will') return baseBonus + 2;
                break;
        }
        
        return baseBonus;
    }

    /**
     * Serializa para JSON (para salvar estado)
     */
    toJSON(): any {
        return {
            attributes: this.attributes,
            derivedStats: this.derivedStats,
            level: this.level,
            experience: this.experience,
            playerClass: this.playerClass
        };
    }

    /**
     * Carrega de JSON
     */
    static fromJSON(data: any): PlayerStats {
        const stats = new PlayerStats(data.playerClass, data.attributes);
        stats.level = data.level;
        stats.experience = data.experience;
        stats.derivedStats = data.derivedStats;
        return stats;
    }

    /**
     * Obtém resumo do personagem
     */
    getSummary(): string {
        const className = this.classConfig.name;
        const hp = `${this.derivedStats.hitPoints}/${this.derivedStats.maxHitPoints}`;
        const mp = this.canCastSpells() ? 
            ` | MP: ${this.derivedStats.manaPoints}/${this.derivedStats.maxManaPoints}` : '';
        
        return `${className} Nível ${this.level} | HP: ${hp}${mp} | CA: ${this.derivedStats.armorClass}`;
    }
}
