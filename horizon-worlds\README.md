# 🌩️ Storm RPG: The World of the Tempest

> An epic fantasy cooperative RPG for **Meta Horizon Worlds**, inspired by *Tormenta 20*, fully built with **TypeScript**.
> 🎮 Multiplayer | 📱 Mobile & VR | 🔁 Remixable | 🧠 Open Source

---

## 📖 About the World

**Storm RPG** is an immersive world where up to four players take on the roles of heroes marked by the **Storm**, a primordial magical force that flows through the world of **Aethra**. Inspired by *Tormenta 20*, this project has been reimagined with new names, adapted lore, and full compatibility with the **Meta Horizon Worlds** ecosystem.

Players explore forgotten ruins, battle shadowy creatures, and master arcane powers as they unravel the mystery of the gods' disappearance — and the awakening of the Void.

This world was designed as a **remixable platform**, enabling other creators to use, extend, and build new experiences upon this complete RPG system.

---

## 🚀 Key Features

- ✅ **Class & Attribute System** (Warrior, Mage, Rogue, etc.)
- ✅ **Real-Time Combat** with d20 simulation
- ✅ **Interactive Spells** (Fireball, Heal, Storm Bolt)
- ✅ **Item Collection & Progression** (Gems, experience points)
- ✅ **Dynamic HUD** (Score, notifications, status)
- ✅ **Cooperative Multiplayer** (1–4 players)
- ✅ **Fully in TypeScript** with modular architecture
- ✅ **Mobile & VR Ready**

## 🏗️ Estrutura do Projeto

```
horizon-worlds/
├── scripts/
│   ├── core/
│   │   ├── GameManager.ts          # Gerenciador principal
│   │   ├── PlayerManager.ts        # Controle de jogadores
│   │   ├── AttributeSystem.ts      # Sistema de atributos
│   │   └── CombatSystem.ts         # Sistema de combate
│   ├── classes/
│   │   ├── BaseClass.ts            # Classe base
│   │   ├── Warrior.ts              # Guerreiro
│   │   ├── Mage.ts                 # Mago
│   │   └── Rogue.ts                # Ladino
│   ├── spells/
│   │   ├── Fireball.ts             # Bola de Fogo
│   │   ├── Heal.ts                 # Cura
│   │   └── StormBolt.ts            # Raio da Tempestade
│   ├── enemies/
│   │   ├── BaseEnemy.ts            # Inimigo base
│   │   ├── Goblin.ts               # Goblin
│   │   └── StormElemental.ts       # Elemental da Tempestade
│   ├── items/
│   │   ├── ItemPickup.ts           # Coleta de itens
│   │   ├── GemCollector.ts         # Coletor de gemas
│   │   └── Equipment.ts            # Equipamentos
│   ├── ui/
│   │   ├── HUDManager.ts           # Interface principal
│   │   ├── ClassSelector.ts        # Seleção de classe
│   │   └── Scoreboard.ts           # Placar
│   └── utils/
│       ├── DiceRoller.ts           # Sistema de dados
│       ├── EventSystem.ts          # Sistema de eventos
│       └── Constants.ts            # Constantes do jogo
├── assets/
│   ├── particles/                  # Efeitos de partículas
│   ├── sounds/                     # Efeitos sonoros
│   └── models/                     # Modelos 3D
├── world-setup/
│   ├── spawn-points.md             # Configuração de spawn
│   ├── enemy-placement.md          # Posicionamento de inimigos
│   └── ui-setup.md                 # Configuração da UI
└── docs/
    ├── tutorial.md                 # Tutorial completo
    ├── api-reference.md            # Referência da API
    └── remix-guide.md              # Guia para remix
```

## 🚀 Como Usar

### Para Jogadores:
1. Entre no mundo "Storm RPG" no Horizon Worlds
2. Escolha sua classe (Guerreiro, Mago ou Ladino)
3. Coopere com outros jogadores para derrotar inimigos
4. Colete gemas e itens para evoluir seu personagem
5. Use magias e habilidades especiais em combate

### Para Criadores:
1. Faça remix do mundo "Storm RPG"
2. Customize os scripts TypeScript conforme necessário
3. Adicione novos inimigos, magias ou áreas
4. Publique sua versão personalizada

## 🎮 Mecânicas Principais

### Sistema de Atributos
- **Força:** Dano corpo a corpo e capacidade de carga
- **Destreza:** Precisão, esquiva e velocidade
- **Constituição:** Pontos de vida e resistência
- **Inteligência:** Pontos de mana e magias arcanas
- **Sabedoria:** Percepção e magias divinas
- **Carisma:** Liderança e magias espontâneas

### Classes Disponíveis
- **Guerreiro:** Tank com alta defesa e ataques corpo a corpo
- **Mago:** DPS mágico com magias de área e controle
- **Ladino:** DPS ágil com ataques críticos e furtividade

### Sistema de Combate
- Rolagens d20 + modificadores vs. Classe de Dificuldade
- Ataques críticos em 20 natural
- Falhas críticas em 1 natural
- Sistema de iniciativa baseado em Destreza

## 🏆 Competição Open Source Champions

Este projeto foi criado para a competição "Open Source Champions" do Meta Horizon Worlds:

- **Main Competition:** Mundo remixável completo
- **Interactive Asset Mini-Challenge:** Assets públicos reutilizáveis
- **Tutorial Mini-Challenge:** Guia completo para criadores

## 📚 Documentação

- [Tutorial Completo](docs/tutorial.md) - Como criar um RPG do zero
- [API Reference](docs/api-reference.md) - Documentação técnica
- [Remix Guide](docs/remix-guide.md) - Como personalizar o jogo

## 🤝 Contribuição

Este é um projeto open source! Contribuições são bem-vindas:

1. Faça fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

MIT License - Livre para uso, modificação e distribuição.

## 🌟 Créditos

- **Sistema Base:** Inspirado em Tormenta 20 RPG
- **Plataforma:** Meta Horizon Worlds
- **Linguagem:** TypeScript
- **Criado por:** [Seu Nome]

---

*Que a Storm guie seus dados!* ⚡
