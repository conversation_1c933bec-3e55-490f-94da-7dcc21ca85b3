# 🌩️ Storm RPG - Meta Horizon Worlds Edition

> **Um RPG cooperativo de fantasia épica rodando diretamente no Meta Horizon Worlds**

## 🎯 Visão Geral

Storm RPG é um jogo multiplayer imersivo baseado no universo de Aethra, onde jogadores assumem o papel de heróis Tempestados em aventuras cooperativas. Construído inteiramente em TypeScript para Meta Horizon Worlds, oferece:

- **Sistema de Classes:** Guerreiro, Mago, Ladino com progressão
- **Combate d20:** Mecânicas de RPG clássico adaptadas para VR/Mobile
- **Magias Interativas:** Efeitos visuais e sonoros imersivos
- **Cooperação:** 1-4 jogadores trabalhando juntos
- **Mundo Remixável:** Código aberto para outros criadores

## 🏗️ Estrutura do Projeto

```
horizon-worlds/
├── scripts/
│   ├── core/
│   │   ├── GameManager.ts          # Gerenciador principal
│   │   ├── PlayerManager.ts        # Controle de jogadores
│   │   ├── AttributeSystem.ts      # Sistema de atributos
│   │   └── CombatSystem.ts         # Sistema de combate
│   ├── classes/
│   │   ├── BaseClass.ts            # Classe base
│   │   ├── Warrior.ts              # Guerreiro
│   │   ├── Mage.ts                 # Mago
│   │   └── Rogue.ts                # Ladino
│   ├── spells/
│   │   ├── Fireball.ts             # Bola de Fogo
│   │   ├── Heal.ts                 # Cura
│   │   └── StormBolt.ts            # Raio da Tempestade
│   ├── enemies/
│   │   ├── BaseEnemy.ts            # Inimigo base
│   │   ├── Goblin.ts               # Goblin
│   │   └── StormElemental.ts       # Elemental da Tempestade
│   ├── items/
│   │   ├── ItemPickup.ts           # Coleta de itens
│   │   ├── GemCollector.ts         # Coletor de gemas
│   │   └── Equipment.ts            # Equipamentos
│   ├── ui/
│   │   ├── HUDManager.ts           # Interface principal
│   │   ├── ClassSelector.ts        # Seleção de classe
│   │   └── Scoreboard.ts           # Placar
│   └── utils/
│       ├── DiceRoller.ts           # Sistema de dados
│       ├── EventSystem.ts          # Sistema de eventos
│       └── Constants.ts            # Constantes do jogo
├── assets/
│   ├── particles/                  # Efeitos de partículas
│   ├── sounds/                     # Efeitos sonoros
│   └── models/                     # Modelos 3D
├── world-setup/
│   ├── spawn-points.md             # Configuração de spawn
│   ├── enemy-placement.md          # Posicionamento de inimigos
│   └── ui-setup.md                 # Configuração da UI
└── docs/
    ├── tutorial.md                 # Tutorial completo
    ├── api-reference.md            # Referência da API
    └── remix-guide.md              # Guia para remix
```

## 🚀 Como Usar

### Para Jogadores:
1. Entre no mundo "Storm RPG" no Horizon Worlds
2. Escolha sua classe (Guerreiro, Mago ou Ladino)
3. Coopere com outros jogadores para derrotar inimigos
4. Colete gemas e itens para evoluir seu personagem
5. Use magias e habilidades especiais em combate

### Para Criadores:
1. Faça remix do mundo "Storm RPG"
2. Customize os scripts TypeScript conforme necessário
3. Adicione novos inimigos, magias ou áreas
4. Publique sua versão personalizada

## 🎮 Mecânicas Principais

### Sistema de Atributos
- **Força:** Dano corpo a corpo e capacidade de carga
- **Destreza:** Precisão, esquiva e velocidade
- **Constituição:** Pontos de vida e resistência
- **Inteligência:** Pontos de mana e magias arcanas
- **Sabedoria:** Percepção e magias divinas
- **Carisma:** Liderança e magias espontâneas

### Classes Disponíveis
- **Guerreiro:** Tank com alta defesa e ataques corpo a corpo
- **Mago:** DPS mágico com magias de área e controle
- **Ladino:** DPS ágil com ataques críticos e furtividade

### Sistema de Combate
- Rolagens d20 + modificadores vs. Classe de Dificuldade
- Ataques críticos em 20 natural
- Falhas críticas em 1 natural
- Sistema de iniciativa baseado em Destreza

## 🏆 Competição Open Source Champions

Este projeto foi criado para a competição "Open Source Champions" do Meta Horizon Worlds:

- **Main Competition:** Mundo remixável completo
- **Interactive Asset Mini-Challenge:** Assets públicos reutilizáveis
- **Tutorial Mini-Challenge:** Guia completo para criadores

## 📚 Documentação

- [Tutorial Completo](docs/tutorial.md) - Como criar um RPG do zero
- [API Reference](docs/api-reference.md) - Documentação técnica
- [Remix Guide](docs/remix-guide.md) - Como personalizar o jogo

## 🤝 Contribuição

Este é um projeto open source! Contribuições são bem-vindas:

1. Faça fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Abra um Pull Request

## 📄 Licença

MIT License - Livre para uso, modificação e distribuição.

## 🌟 Créditos

- **Sistema Base:** Inspirado em Tormenta 20 RPG
- **Plataforma:** Meta Horizon Worlds
- **Linguagem:** TypeScript
- **Criado por:** [Seu Nome]

---

*Que a Storm guie seus dados!* ⚡
