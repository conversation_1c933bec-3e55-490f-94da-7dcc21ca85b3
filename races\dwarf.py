"""
Storm RPG - Raças Anãs
Anões da Montanha e Anões da Colina
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus, create_darkvision, create_resistance
from core.attributes import AttributeType


class MountainDwarf(Race):
    """
    Anões da Montanha - Mestres da forja e guerreiros resistentes
    
    Os anões da montanha vivem nas profundezas das montanhas de Kragh,
    onde forjam as melhores armas e armaduras de Aethra. São conhecidos
    por sua resistência, força e maestria na metalurgia.
    """
    
    def get_name(self) -> str:
        return "An<PERSON> da Montanha"
    
    def get_description(self) -> str:
        return ("Resistentes mestres da forja que vivem nas montanhas. "
                "Conhecidos por sua força e habilidade com metais.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 6  # An<PERSON><PERSON> são mais lentos, mas não são afetados por armadura
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CONSTITUTION, 2, "Resistência Anã"),
            create_attribute_bonus(AttributeType.STRENGTH, 1, "Força da Montanha"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Resistência Anã",
                description="Recebe +2 em testes de resistência contra veneno e magia",
                trait_type=TraitType.RESISTANCE,
                value="poison_magic"
            ),
            
            RacialTrait(
                name="Conhecimento de Pedra",
                description="Recebe +2 em testes relacionados a pedra, metal e arquitetura",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Engenharia", 2)
            ),
            
            RacialTrait(
                name="Maestria com Armas Anãs",
                description="Proficiência com machados de guerra, martelos de guerra e armas anãs",
                trait_type=TraitType.OTHER,
                value="dwarven_weapons"
            ),
            
            RacialTrait(
                name="Maestria com Armaduras",
                description="Proficiência com armaduras leves e médias",
                trait_type=TraitType.OTHER,
                value="armor_proficiency"
            ),
            
            RacialTrait(
                name="Forjador Nato",
                description="Recebe +2 em testes de Ofício relacionados a metalurgia",
                trait_type=TraitType.SKILL_BONUS,
                value=("Ofício", 2)
            ),
            
            RacialTrait(
                name="Estabilidade",
                description="Recebe +4 contra tentativas de derrubar ou empurrar",
                trait_type=TraitType.OTHER,
                value="stability"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Anão", "Terran"]
    
    def apply_to_character(self, character):
        """Aplica características dos anões da montanha"""
        super().apply_to_character(character)
        
        # Anões não são afetados por penalidades de velocidade de armadura
        character.add_feature("Movimento Estável: Velocidade não é reduzida por armadura")


class HillDwarf(Race):
    """
    Anões da Colina - Diplomáticos e sábios
    
    Os anões da colina são mais sociáveis que seus primos da montanha.
    Vivem em comunidades próximas à superfície e são conhecidos por
    sua sabedoria, diplomacia e habilidades de cura.
    """
    
    def get_name(self) -> str:
        return "Anão da Colina"
    
    def get_description(self) -> str:
        return ("Diplomáticos e sábios, vivem próximos à superfície e "
                "são conhecidos por sua sabedoria e habilidades sociais.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 6
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.WISDOM, 2, "Sabedoria Ancestral"),
            create_attribute_bonus(AttributeType.CONSTITUTION, 1, "Resistência Anã"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Resistência Anã",
                description="Recebe +2 em testes de resistência contra veneno e magia",
                trait_type=TraitType.RESISTANCE,
                value="poison_magic"
            ),
            
            RacialTrait(
                name="Conhecimento de Pedra",
                description="Recebe +2 em testes relacionados a pedra e arquitetura",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Engenharia", 2)
            ),
            
            RacialTrait(
                name="Diplomacia Anã",
                description="Recebe +2 em testes de Diplomacia e Intuição",
                trait_type=TraitType.SKILL_BONUS,
                value=("Diplomacia", 2)
            ),
            
            RacialTrait(
                name="Resistência Extra",
                description="Recebe +1 ponto de vida por nível",
                trait_type=TraitType.OTHER,
                value="extra_hp"
            ),
            
            RacialTrait(
                name="Conhecimento Herbal",
                description="Recebe +2 em testes de Medicina e Sobrevivência",
                trait_type=TraitType.SKILL_BONUS,
                value=("Medicina", 2)
            ),
            
            RacialTrait(
                name="Estabilidade",
                description="Recebe +4 contra tentativas de derrubar ou empurrar",
                trait_type=TraitType.OTHER,
                value="stability"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Anão", "Gnomo"]
    
    def apply_to_character(self, character):
        """Aplica características dos anões da colina"""
        super().apply_to_character(character)
        
        # Anões da colina ganham HP extra
        character.health_manager.max_hp += character.level
        character.health_manager.current_hp = character.health_manager.max_hp


class DeepDwarf(Race):
    """
    Anões das Profundezas - Adaptados às cavernas mais profundas
    
    Uma subraça anã que vive nas profundezas mais extremas,
    desenvolvendo adaptações únicas para sobreviver no subsolo.
    """
    
    def get_name(self) -> str:
        return "Anão das Profundezas"
    
    def get_description(self) -> str:
        return ("Adaptados às profundezas extremas, desenvolveram "
                "habilidades únicas para sobreviver no subsolo.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 6
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CONSTITUTION, 2, "Resistência das Profundezas"),
            create_attribute_bonus(AttributeType.WISDOM, 1, "Percepção Subterrânea"),
            
            RacialTrait(
                name="Visão no Escuro Superior",
                description="Pode ver no escuro até 36m como se fosse penumbra",
                trait_type=TraitType.SPECIAL_SENSE,
                value=36
            ),
            
            RacialTrait(
                name="Sensibilidade à Luz",
                description="Recebe -1 em ataques e testes de Percepção sob luz solar",
                trait_type=TraitType.OTHER,
                value="light_sensitivity"
            ),
            
            RacialTrait(
                name="Resistência Anã Superior",
                description="Recebe +4 em testes de resistência contra veneno e magia",
                trait_type=TraitType.RESISTANCE,
                value="poison_magic_superior"
            ),
            
            RacialTrait(
                name="Conhecimento das Profundezas",
                description="Recebe +4 em testes relacionados ao subsolo",
                trait_type=TraitType.SKILL_BONUS,
                value=("Sobrevivência", 4)
            ),
            
            RacialTrait(
                name="Tremorsense",
                description="Pode detectar movimento no solo num raio de 3m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="tremorsense"
            ),
            
            RacialTrait(
                name="Estabilidade",
                description="Recebe +4 contra tentativas de derrubar ou empurrar",
                trait_type=TraitType.OTHER,
                value="stability"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Anão", "Terran", "Subcomum"]


class DuergarDwarf(Race):
    """
    Anões Cinzentos (Duergar) - Corrompidos pela Storm sombria
    
    Anões que foram expostos à Storm sombria e desenvolveram
    habilidades psíquicas, mas perderam parte de sua humanidade.
    """
    
    def get_name(self) -> str:
        return "Anão Cinzento"
    
    def get_description(self) -> str:
        return ("Corrompidos pela Storm sombria, desenvolveram habilidades "
                "psíquicas mas perderam parte de sua humanidade.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 6
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CONSTITUTION, 2, "Resistência Sombria"),
            create_attribute_bonus(AttributeType.INTELLIGENCE, 1, "Mente Psíquica"),
            
            create_darkvision(36),
            
            RacialTrait(
                name="Sensibilidade à Luz",
                description="Recebe -1 em ataques e testes de Percepção sob luz solar",
                trait_type=TraitType.OTHER,
                value="light_sensitivity"
            ),
            
            RacialTrait(
                name="Resistência Psíquica",
                description="Recebe +2 em testes de resistência contra efeitos mentais",
                trait_type=TraitType.RESISTANCE,
                value="mental"
            ),
            
            RacialTrait(
                name="Invisibilidade Psíquica",
                description="Pode se tornar invisível por 1 minuto, 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="invisibility"
            ),
            
            RacialTrait(
                name="Crescimento Psíquico",
                description="Pode aumentar de tamanho por 1 minuto, 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="enlarge"
            ),
            
            RacialTrait(
                name="Imunidade a Paralisia",
                description="Imune a efeitos de paralisia e sono",
                trait_type=TraitType.IMMUNITY,
                value="paralysis_sleep"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Anão", "Subcomum", "Psíquico"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste das Raças Anãs ===")
    
    mountain_dwarf = MountainDwarf()
    print(mountain_dwarf.get_summary())
    
    print(f"\n" + "="*50)
    
    hill_dwarf = HillDwarf()
    print(hill_dwarf.get_summary())
    
    print(f"\n" + "="*50)
    
    deep_dwarf = DeepDwarf()
    print(deep_dwarf.get_summary())
