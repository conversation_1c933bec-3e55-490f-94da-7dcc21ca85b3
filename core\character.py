"""
Storm RPG - Sistema de Personagens
Classe base para personagens jogadores e NPCs
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from enum import Enum
from .attributes import AttributeSet, AttributeType
from .mechanics import HealthManager, Test, TestResult


class CharacterSize(Enum):
    """Tamanhos de personagem"""
    TINY = "minúsculo"
    SMALL = "pequeno" 
    MEDIUM = "médio"
    LARGE = "grande"
    HUGE = "enorme"
    GARGANTUAN = "colossal"


class CharacterLevel:
    """Gerenciador de nível e experiência"""
    
    def __init__(self, level: int = 1):
        self.level = max(1, min(20, level))
        self.experience = self.xp_for_level(self.level)
    
    @staticmethod
    def xp_for_level(level: int) -> int:
        """Retorna XP necessário para um nível"""
        xp_table = {
            1: 0, 2: 1000, 3: 3000, 4: 6000, 5: 10000,
            6: 15000, 7: 21000, 8: 28000, 9: 36000, 10: 45000,
            11: 55000, 12: 66000, 13: 78000, 14: 91000, 15: 105000,
            16: 120000, 17: 136000, 18: 153000, 19: 171000, 20: 190000
        }
        return xp_table.get(level, 0)
    
    def add_experience(self, xp: int) -> bool:
        """Adiciona XP e verifica se subiu de nível"""
        self.experience += xp
        new_level = self.level_for_xp(self.experience)
        
        if new_level > self.level:
            self.level = new_level
            return True
        return False
    
    @staticmethod
    def level_for_xp(xp: int) -> int:
        """Retorna o nível para uma quantidade de XP"""
        for level in range(20, 0, -1):
            if xp >= CharacterLevel.xp_for_level(level):
                return level
        return 1
    
    @property
    def proficiency_bonus(self) -> int:
        """Bônus de proficiência baseado no nível"""
        return (self.level - 1) // 4 + 2
    
    @property
    def xp_to_next_level(self) -> int:
        """XP necessário para o próximo nível"""
        if self.level >= 20:
            return 0
        return self.xp_for_level(self.level + 1) - self.experience


@dataclass
class Skill:
    """Representa uma perícia"""
    name: str
    attribute: AttributeType
    trained: bool = False
    ranks: int = 0
    
    def get_modifier(self, attributes: AttributeSet, level: int) -> int:
        """Calcula modificador total da perícia"""
        attr_mod = attributes.get_modifier(self.attribute)
        training_bonus = 2 if self.trained else 0
        level_bonus = level // 2
        return attr_mod + self.ranks + training_bonus + level_bonus


class Character:
    """Classe base para personagens"""
    
    def __init__(self, name: str = "Personagem", race=None, character_class=None):
        self.name = name
        self.race = race
        self.character_class = character_class
        
        # Atributos básicos
        self.attributes = AttributeSet()
        self.level_manager = CharacterLevel()
        
        # Características físicas
        self.size = CharacterSize.MEDIUM
        self.speed = 9  # metros por turno
        
        # Combate
        self.armor_class = 10
        self.health_manager = HealthManager(8)  # HP base
        self.mana_points = 0
        self.max_mana_points = 0
        
        # Perícias
        self.skills: Dict[str, Skill] = {}
        self._initialize_skills()
        
        # Equipamentos e inventário
        self.equipment: Dict[str, Any] = {}
        self.inventory: List[Any] = []
        
        # Características especiais
        self.features: List[str] = []
        self.spells_known: List[Any] = []
        self.languages: List[str] = ["Comum"]
        
        # Aplicar raça e classe se fornecidas
        if self.race:
            self.race.apply_to_character(self)
        if self.character_class:
            self.character_class.apply_to_character(self)
    
    def _initialize_skills(self):
        """Inicializa as perícias básicas"""
        basic_skills = {
            "Acrobacia": Skill("Acrobacia", AttributeType.DEXTERITY),
            "Atletismo": Skill("Atletismo", AttributeType.STRENGTH),
            "Enganação": Skill("Enganação", AttributeType.CHARISMA),
            "Furtividade": Skill("Furtividade", AttributeType.DEXTERITY),
            "História": Skill("História", AttributeType.INTELLIGENCE),
            "Intimidação": Skill("Intimidação", AttributeType.CHARISMA),
            "Intuição": Skill("Intuição", AttributeType.WISDOM),
            "Investigação": Skill("Investigação", AttributeType.INTELLIGENCE),
            "Medicina": Skill("Medicina", AttributeType.WISDOM),
            "Natureza": Skill("Natureza", AttributeType.INTELLIGENCE),
            "Percepção": Skill("Percepção", AttributeType.WISDOM),
            "Persuasão": Skill("Persuasão", AttributeType.CHARISMA),
            "Prestidigitação": Skill("Prestidigitação", AttributeType.DEXTERITY),
            "Religião": Skill("Religião", AttributeType.INTELLIGENCE),
            "Sobrevivência": Skill("Sobrevivência", AttributeType.WISDOM)
        }
        self.skills.update(basic_skills)
    
    @property
    def level(self) -> int:
        """Nível atual do personagem"""
        return self.level_manager.level
    
    @property
    def proficiency_bonus(self) -> int:
        """Bônus de proficiência"""
        return self.level_manager.proficiency_bonus
    
    @property
    def hit_points(self) -> int:
        """Pontos de vida atuais"""
        return self.health_manager.current_hp
    
    @property
    def max_hit_points(self) -> int:
        """Pontos de vida máximos"""
        return self.health_manager.max_hp
    
    def get_skill_modifier(self, skill_name: str) -> int:
        """Retorna o modificador de uma perícia"""
        if skill_name in self.skills:
            skill = self.skills[skill_name]
            return skill.get_modifier(self.attributes, self.level)
        return 0
    
    def make_skill_test(self, skill_name: str, difficulty: int, 
                       advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Realiza um teste de perícia"""
        modifier = self.get_skill_modifier(skill_name)
        return Test.skill_test(modifier, difficulty, advantage, disadvantage)
    
    def make_attribute_test(self, attr_type: AttributeType, difficulty: int,
                           advantage: bool = False, disadvantage: bool = False) -> TestResult:
        """Realiza um teste de atributo"""
        return Test.attribute_test(self.attributes, attr_type, difficulty, 
                                 self.level // 2, advantage, disadvantage)
    
    def take_damage(self, damage: int) -> int:
        """Recebe dano"""
        return self.health_manager.take_damage(damage)
    
    def heal(self, healing: int) -> int:
        """Recebe cura"""
        return self.health_manager.heal(healing)
    
    def spend_mana(self, cost: int) -> bool:
        """Gasta pontos de mana"""
        if self.mana_points >= cost:
            self.mana_points -= cost
            return True
        return False
    
    def restore_mana(self, amount: int):
        """Restaura pontos de mana"""
        self.mana_points = min(self.max_mana_points, self.mana_points + amount)
    
    def add_feature(self, feature: str):
        """Adiciona uma característica especial"""
        if feature not in self.features:
            self.features.append(feature)
    
    def level_up(self):
        """Sobe de nível (deve ser implementado pelas classes)"""
        if self.character_class:
            self.character_class.level_up(self)
    
    def get_armor_class(self) -> int:
        """Calcula CA total"""
        base_ac = 10
        dex_mod = self.attributes.get_modifier(AttributeType.DEXTERITY)
        
        # CA base + Des + equipamentos + outros bônus
        total_ac = base_ac + dex_mod + self.armor_class - 10
        return total_ac
    
    def get_initiative_modifier(self) -> int:
        """Modificador de iniciativa"""
        return self.attributes.get_modifier(AttributeType.DEXTERITY) + self.level
    
    def short_rest(self):
        """Descanso curto (1 hora)"""
        # Recupera algumas habilidades
        pass
    
    def long_rest(self):
        """Descanso longo (8 horas)"""
        # Recupera HP e mana
        self.health_manager.current_hp = self.health_manager.max_hp
        self.mana_points = self.max_mana_points
        # Remove efeitos temporários
        self.attributes.remove_temporary_bonuses()
    
    def __str__(self) -> str:
        race_name = self.race.name if self.race else "Humano"
        class_name = self.character_class.name if self.character_class else "Aventureiro"
        
        return (f"{self.name}\n"
                f"{race_name} {class_name} Nível {self.level}\n"
                f"HP: {self.hit_points}/{self.max_hit_points}\n"
                f"MP: {self.mana_points}/{self.max_mana_points}\n"
                f"CA: {self.get_armor_class()}")


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste do Sistema de Personagens ===")
    
    # Criar personagem básico
    hero = Character("Aelindra")
    hero.attributes.apply_racial_bonus(AttributeType.DEXTERITY, 2)
    hero.skills["Furtividade"].trained = True
    hero.skills["Furtividade"].ranks = 3
    
    print(hero)
    print(f"\nModificador de Furtividade: +{hero.get_skill_modifier('Furtividade')}")
    
    # Teste de perícia
    result = hero.make_skill_test("Furtividade", 15)
    print(f"Teste de Furtividade: {result}")
    
    # Teste de dano e cura
    print(f"\nHP antes do dano: {hero.hit_points}")
    damage = hero.take_damage(5)
    print(f"Recebeu {damage} de dano, HP atual: {hero.hit_points}")
    
    healing = hero.heal(3)
    print(f"Curou {healing} HP, HP atual: {hero.hit_points}")
