/**
 * Storm RPG - Base Spell System
 * Sistema base para magias com efeitos visuais e sonoros
 */

import { PlayerStats } from '../core/AttributeSystem';
import { DiceRoller } from '../utils/DiceRoller';
import { stormEvents } from '../utils/EventSystem';
import { SPELL_CONFIGS, ASSET_IDS } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Entity: any;

export interface SpellTarget {
    entityId?: string;
    position: { x: number; y: number; z: number };
    entity?: any; // Horizon Worlds Entity
}

export interface SpellEffect {
    damage?: number;
    healing?: number;
    duration?: number;
    statusEffect?: string;
    areaOfEffect?: number;
}

export abstract class BaseSpell {
    public name: string;
    public description: string;
    public manaCost: number;
    public castTime: number;
    public cooldown: number;
    public range: number;
    public level: number;

    constructor(
        name: string,
        description: string,
        manaCost: number,
        castTime: number = 1000,
        cooldown: number = 3000,
        range: number = 30,
        level: number = 1
    ) {
        this.name = name;
        this.description = description;
        this.manaCost = manaCost;
        this.castTime = castTime;
        this.cooldown = cooldown;
        this.range = range;
        this.level = level;
    }

    /**
     * Verifica se o conjurador pode lançar a magia
     */
    canCast(caster: PlayerStats, target?: SpellTarget): boolean {
        // Verificar mana
        if (caster.derivedStats.manaPoints < this.manaCost) {
            return false;
        }

        // Verificar se pode conjurar magias
        if (!caster.canCastSpells()) {
            return false;
        }

        // Verificar alcance se há alvo
        if (target && target.position) {
            // TODO: Implementar verificação de alcance baseada na posição do conjurador
        }

        return true;
    }

    /**
     * Inicia o processo de conjuração
     */
    cast(caster: PlayerStats, target?: SpellTarget): Promise<SpellEffect | null> {
        if (!this.canCast(caster, target)) {
            return Promise.resolve(null);
        }

        // Gastar mana
        caster.spendMana(this.manaCost);

        // Emitir evento de início de conjuração
        stormEvents.emitSpellCast(caster.playerClass, this.name, target?.entityId);

        // Iniciar animação de conjuração
        this.startCastingAnimation(target?.position || { x: 0, y: 0, z: 0 });

        // Retornar promessa que resolve após o tempo de conjuração
        return new Promise((resolve) => {
            setTimeout(() => {
                const effect = this.executeSpell(caster, target);
                resolve(effect);
            }, this.castTime);
        });
    }

    /**
     * Executa a magia (implementado pelas subclasses)
     */
    protected abstract executeSpell(caster: PlayerStats, target?: SpellTarget): SpellEffect | null;

    /**
     * Inicia animação de conjuração
     */
    protected startCastingAnimation(targetPosition: { x: number; y: number; z: number }): void {
        // Implementação base - pode ser sobrescrita
        console.log(`Conjurando ${this.name}...`);
    }

    /**
     * Cria efeito visual
     */
    protected createVisualEffect(
        position: { x: number; y: number; z: number },
        particleAssetId: string,
        duration: number = 3000
    ): any {
        try {
            const effectEntity = Scene.createRootEntity();
            effectEntity.setPosition(position.x, position.y, position.z);

            const particleComponent = effectEntity.createComponent("engine:persistentParticleEffect", {
                assetId: particleAssetId
            });

            // Remover efeito após duração
            setTimeout(() => {
                effectEntity.destroy();
            }, duration);

            return effectEntity;
        } catch (error) {
            console.error("Erro ao criar efeito visual:", error);
            return null;
        }
    }

    /**
     * Reproduz efeito sonoro
     */
    protected playSoundEffect(soundAssetId: string, position?: { x: number; y: number; z: number }): void {
        try {
            const soundEntity = Scene.createRootEntity();
            
            if (position) {
                soundEntity.setPosition(position.x, position.y, position.z);
            }

            const audioComponent = soundEntity.createComponent("engine:audioSource", {
                assetId: soundAssetId,
                loop: false,
                volume: 1.0,
                spatialAudio: !!position
            });

            audioComponent.play();

            // Remover entidade após o som
            setTimeout(() => {
                soundEntity.destroy();
            }, 5000);
        } catch (error) {
            console.error("Erro ao reproduzir som:", error);
        }
    }

    /**
     * Calcula dano da magia
     */
    protected calculateSpellDamage(caster: PlayerStats, baseDamage: string): number {
        const damage = DiceRoller.rollDamage(baseDamage);
        const spellcastingMod = caster.getPrimaryAttributeModifier();
        return damage + spellcastingMod;
    }

    /**
     * Calcula cura da magia
     */
    protected calculateSpellHealing(caster: PlayerStats, baseHealing: string): number {
        const healing = DiceRoller.rollDamage(baseHealing);
        const spellcastingMod = caster.getPrimaryAttributeModifier();
        return healing + spellcastingMod;
    }

    /**
     * Encontra entidades em área
     */
    protected findEntitiesInArea(
        center: { x: number; y: number; z: number },
        radius: number
    ): any[] {
        // TODO: Implementar busca de entidades em área
        // Por enquanto retorna array vazio
        return [];
    }

    /**
     * Calcula distância entre dois pontos
     */
    protected calculateDistance(
        pos1: { x: number; y: number; z: number },
        pos2: { x: number; y: number; z: number }
    ): number {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * Verifica teste de resistência
     */
    protected rollSavingThrow(
        target: any,
        saveType: 'fortitude' | 'reflex' | 'will',
        difficultyClass: number
    ): boolean {
        // TODO: Implementar baseado nas estatísticas do alvo
        const saveBonus = 5; // Placeholder
        const result = DiceRoller.rollSavingThrow(saveBonus, difficultyClass);
        return result.success;
    }
}

// Sistema de gerenciamento de magias
export class SpellManager {
    private activeSpells: Map<string, BaseSpell> = new Map();
    private spellCooldowns: Map<string, number> = new Map();

    /**
     * Registra uma magia
     */
    registerSpell(spellId: string, spell: BaseSpell): void {
        this.activeSpells.set(spellId, spell);
    }

    /**
     * Obtém uma magia
     */
    getSpell(spellId: string): BaseSpell | undefined {
        return this.activeSpells.get(spellId);
    }

    /**
     * Verifica se uma magia está em cooldown
     */
    isOnCooldown(casterId: string, spellId: string): boolean {
        const key = `${casterId}_${spellId}`;
        const cooldownEnd = this.spellCooldowns.get(key);
        
        if (!cooldownEnd) return false;
        
        const now = Date.now();
        if (now >= cooldownEnd) {
            this.spellCooldowns.delete(key);
            return false;
        }
        
        return true;
    }

    /**
     * Inicia cooldown de uma magia
     */
    startCooldown(casterId: string, spellId: string, duration: number): void {
        const key = `${casterId}_${spellId}`;
        const cooldownEnd = Date.now() + duration;
        this.spellCooldowns.set(key, cooldownEnd);
    }

    /**
     * Tenta conjurar uma magia
     */
    async castSpell(
        casterId: string,
        spellId: string,
        caster: PlayerStats,
        target?: SpellTarget
    ): Promise<SpellEffect | null> {
        const spell = this.getSpell(spellId);
        if (!spell) {
            console.error(`Magia ${spellId} não encontrada`);
            return null;
        }

        // Verificar cooldown
        if (this.isOnCooldown(casterId, spellId)) {
            console.log(`Magia ${spellId} ainda está em cooldown`);
            return null;
        }

        // Tentar conjurar
        const result = await spell.cast(caster, target);
        
        if (result) {
            // Iniciar cooldown
            this.startCooldown(casterId, spellId, spell.cooldown);
        }

        return result;
    }

    /**
     * Obtém tempo restante de cooldown
     */
    getRemainingCooldown(casterId: string, spellId: string): number {
        const key = `${casterId}_${spellId}`;
        const cooldownEnd = this.spellCooldowns.get(key);
        
        if (!cooldownEnd) return 0;
        
        const remaining = cooldownEnd - Date.now();
        return Math.max(0, remaining);
    }

    /**
     * Lista todas as magias disponíveis
     */
    getAvailableSpells(): string[] {
        return Array.from(this.activeSpells.keys());
    }

    /**
     * Remove todos os cooldowns de um conjurador
     */
    clearCasterCooldowns(casterId: string): void {
        const keysToDelete: string[] = [];
        
        for (const key of this.spellCooldowns.keys()) {
            if (key.startsWith(`${casterId}_`)) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => this.spellCooldowns.delete(key));
    }
}

// Instância global do gerenciador de magias
export const spellManager = new SpellManager();
