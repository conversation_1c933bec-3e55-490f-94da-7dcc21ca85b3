/**
 * Storm RPG - Demo Assets Manager
 * Gerencia assets específicos para o modo demonstração
 */

import { stormEvents } from '../utils/EventSystem';
import { performanceOptimizer } from './PerformanceOptimizer';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const AssetLibrary: any;

export interface AssetInfo {
    id: string;
    name: string;
    type: 'model' | 'texture' | 'audio' | 'particle' | 'material';
    category: 'demo' | 'npc' | 'environment' | 'effects' | 'ui';
    size: number; // em KB
    quality: 'low' | 'medium' | 'high';
    mobileOptimized: boolean;
    preload: boolean;
}

export class DemoAssets {
    private assetRegistry: Map<string, AssetInfo> = new Map();
    private loadedAssets: Map<string, any> = new Map();
    private preloadQueue: string[] = [];
    private loadingPromises: Map<string, Promise<any>> = new Map();

    constructor() {
        this.registerDemoAssets();
        this.preloadEssentialAssets();
        console.log("[DemoAssets] Sistema de assets da demo inicializado");
    }

    /**
     * Registra todos os assets da demo
     */
    private registerDemoAssets(): void {
        // Assets de NPCs
        this.registerAsset({
            id: "lisandra_model",
            name: "Lisandra - Druida",
            type: 'model',
            category: 'npc',
            size: 2500,
            quality: 'high',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "rodleck_model",
            name: "Rodleck - Guardião",
            type: 'model',
            category: 'npc',
            size: 2800,
            quality: 'high',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "beluhga_model",
            name: "Beluhga - Rainha dos Dragões",
            type: 'model',
            category: 'npc',
            size: 4500,
            quality: 'high',
            mobileOptimized: false,
            preload: false
        });

        // Assets de ambiente
        this.registerAsset({
            id: "khalmyr_buildings",
            name: "Edifícios de Khalmyr",
            type: 'model',
            category: 'environment',
            size: 3200,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "tollon_trees",
            name: "Árvores de Tollon",
            type: 'model',
            category: 'environment',
            size: 2100,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "storm_crystal_model",
            name: "Cristal da Storm",
            type: 'model',
            category: 'environment',
            size: 1800,
            quality: 'high',
            mobileOptimized: true,
            preload: true
        });

        // Assets de efeitos
        this.registerAsset({
            id: "crystal_glow_effect",
            name: "Brilho do Cristal",
            type: 'particle',
            category: 'effects',
            size: 500,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "crystal_activation_effect",
            name: "Ativação do Cristal",
            type: 'particle',
            category: 'effects',
            size: 800,
            quality: 'high',
            mobileOptimized: true,
            preload: false
        });

        this.registerAsset({
            id: "portal_effect",
            name: "Efeito de Portal",
            type: 'particle',
            category: 'effects',
            size: 1200,
            quality: 'high',
            mobileOptimized: false,
            preload: false
        });

        this.registerAsset({
            id: "golden_portal_effect",
            name: "Portal Dourado (Easter Egg)",
            type: 'particle',
            category: 'effects',
            size: 1500,
            quality: 'high',
            mobileOptimized: false,
            preload: false
        });

        // Assets de áudio
        this.registerAsset({
            id: "crystal_hum_sound",
            name: "Som Ambiente do Cristal",
            type: 'audio',
            category: 'environment',
            size: 800,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "crystal_activate_sound",
            name: "Som de Ativação",
            type: 'audio',
            category: 'effects',
            size: 600,
            quality: 'high',
            mobileOptimized: true,
            preload: false
        });

        this.registerAsset({
            id: "mystic_chime_sound",
            name: "Sino Místico",
            type: 'audio',
            category: 'effects',
            size: 400,
            quality: 'medium',
            mobileOptimized: true,
            preload: false
        });

        this.registerAsset({
            id: "forest_ambient",
            name: "Som Ambiente da Floresta",
            type: 'audio',
            category: 'environment',
            size: 1200,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        // Assets de UI
        this.registerAsset({
            id: "hud_panel_background",
            name: "Fundo do Painel HUD",
            type: 'texture',
            category: 'ui',
            size: 300,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "notification_background",
            name: "Fundo de Notificação",
            type: 'texture',
            category: 'ui',
            size: 200,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "dialogue_background",
            name: "Fundo de Diálogo",
            type: 'texture',
            category: 'ui',
            size: 250,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        // Assets de marcadores
        this.registerAsset({
            id: "zone_circle_model",
            name: "Marcador de Zona",
            type: 'model',
            category: 'ui',
            size: 150,
            quality: 'low',
            mobileOptimized: true,
            preload: true
        });

        this.registerAsset({
            id: "wooden_sign_model",
            name: "Placa de Madeira",
            type: 'model',
            category: 'environment',
            size: 400,
            quality: 'medium',
            mobileOptimized: true,
            preload: true
        });

        console.log(`[DemoAssets] ${this.assetRegistry.size} assets registrados`);
    }

    /**
     * Registra um asset individual
     */
    private registerAsset(assetInfo: AssetInfo): void {
        this.assetRegistry.set(assetInfo.id, assetInfo);
        
        if (assetInfo.preload) {
            this.preloadQueue.push(assetInfo.id);
        }
    }

    /**
     * Pré-carrega assets essenciais
     */
    private async preloadEssentialAssets(): Promise<void> {
        console.log(`[DemoAssets] Pré-carregando ${this.preloadQueue.length} assets essenciais...`);
        
        const profile = performanceOptimizer.getCurrentProfile();
        
        // Filtrar assets baseado no perfil de performance
        const filteredQueue = this.preloadQueue.filter(assetId => {
            const asset = this.assetRegistry.get(assetId);
            if (!asset) return false;
            
            // Pular assets não otimizados para mobile em dispositivos móveis
            if (profile.isMobile && !asset.mobileOptimized) {
                return false;
            }
            
            // Pular assets de alta qualidade em dispositivos de baixo desempenho
            if (profile.performanceLevel === 'low' && asset.quality === 'high') {
                return false;
            }
            
            return true;
        });

        // Carregar assets em lotes para não sobrecarregar
        const batchSize = profile.isMobile ? 3 : 5;
        for (let i = 0; i < filteredQueue.length; i += batchSize) {
            const batch = filteredQueue.slice(i, i + batchSize);
            await this.loadAssetBatch(batch);
            
            // Pequena pausa entre lotes
            await this.delay(100);
        }

        console.log(`[DemoAssets] Pré-carregamento concluído: ${filteredQueue.length} assets carregados`);
    }

    /**
     * Carrega um lote de assets
     */
    private async loadAssetBatch(assetIds: string[]): Promise<void> {
        const promises = assetIds.map(assetId => this.loadAsset(assetId));
        await Promise.all(promises);
    }

    /**
     * Carrega um asset específico
     */
    public async loadAsset(assetId: string): Promise<any> {
        // Verificar se já está carregado
        if (this.loadedAssets.has(assetId)) {
            return this.loadedAssets.get(assetId);
        }

        // Verificar se já está sendo carregado
        if (this.loadingPromises.has(assetId)) {
            return this.loadingPromises.get(assetId);
        }

        const assetInfo = this.assetRegistry.get(assetId);
        if (!assetInfo) {
            console.warn(`[DemoAssets] Asset não encontrado: ${assetId}`);
            return null;
        }

        // Criar promise de carregamento
        const loadingPromise = this.performAssetLoad(assetInfo);
        this.loadingPromises.set(assetId, loadingPromise);

        try {
            const asset = await loadingPromise;
            this.loadedAssets.set(assetId, asset);
            this.loadingPromises.delete(assetId);
            
            console.log(`[DemoAssets] Asset carregado: ${assetInfo.name} (${assetInfo.size}KB)`);
            return asset;
        } catch (error) {
            console.error(`[DemoAssets] Erro ao carregar asset ${assetId}:`, error);
            this.loadingPromises.delete(assetId);
            return null;
        }
    }

    /**
     * Executa o carregamento real do asset
     */
    private async performAssetLoad(assetInfo: AssetInfo): Promise<any> {
        // Simular carregamento baseado no tipo de asset
        switch (assetInfo.type) {
            case 'model':
                return this.loadModel(assetInfo);
            case 'texture':
                return this.loadTexture(assetInfo);
            case 'audio':
                return this.loadAudio(assetInfo);
            case 'particle':
                return this.loadParticleEffect(assetInfo);
            case 'material':
                return this.loadMaterial(assetInfo);
            default:
                throw new Error(`Tipo de asset não suportado: ${assetInfo.type}`);
        }
    }

    /**
     * Carrega modelo 3D
     */
    private async loadModel(assetInfo: AssetInfo): Promise<any> {
        // Simular carregamento de modelo
        await this.delay(assetInfo.size / 1000 * 100); // Simular tempo baseado no tamanho
        
        // No Horizon Worlds real, seria algo como:
        // return AssetLibrary.loadModel(assetInfo.id);
        
        return {
            id: assetInfo.id,
            type: 'model',
            data: `model_data_${assetInfo.id}`
        };
    }

    /**
     * Carrega textura
     */
    private async loadTexture(assetInfo: AssetInfo): Promise<any> {
        await this.delay(assetInfo.size / 1000 * 50);
        
        return {
            id: assetInfo.id,
            type: 'texture',
            data: `texture_data_${assetInfo.id}`
        };
    }

    /**
     * Carrega áudio
     */
    private async loadAudio(assetInfo: AssetInfo): Promise<any> {
        await this.delay(assetInfo.size / 1000 * 80);
        
        return {
            id: assetInfo.id,
            type: 'audio',
            data: `audio_data_${assetInfo.id}`
        };
    }

    /**
     * Carrega efeito de partículas
     */
    private async loadParticleEffect(assetInfo: AssetInfo): Promise<any> {
        await this.delay(assetInfo.size / 1000 * 60);
        
        return {
            id: assetInfo.id,
            type: 'particle',
            data: `particle_data_${assetInfo.id}`
        };
    }

    /**
     * Carrega material
     */
    private async loadMaterial(assetInfo: AssetInfo): Promise<any> {
        await this.delay(assetInfo.size / 1000 * 40);
        
        return {
            id: assetInfo.id,
            type: 'material',
            data: `material_data_${assetInfo.id}`
        };
    }

    /**
     * Obtém asset carregado
     */
    public getAsset(assetId: string): any | null {
        return this.loadedAssets.get(assetId) || null;
    }

    /**
     * Verifica se asset está carregado
     */
    public isAssetLoaded(assetId: string): boolean {
        return this.loadedAssets.has(assetId);
    }

    /**
     * Carrega asset sob demanda se necessário
     */
    public async ensureAssetLoaded(assetId: string): Promise<any> {
        if (this.isAssetLoaded(assetId)) {
            return this.getAsset(assetId);
        }
        
        return await this.loadAsset(assetId);
    }

    /**
     * Descarrega asset para liberar memória
     */
    public unloadAsset(assetId: string): void {
        if (this.loadedAssets.has(assetId)) {
            const asset = this.loadedAssets.get(assetId);
            
            // Cleanup específico por tipo
            if (asset && asset.cleanup) {
                asset.cleanup();
            }
            
            this.loadedAssets.delete(assetId);
            console.log(`[DemoAssets] Asset descarregado: ${assetId}`);
        }
    }

    /**
     * Descarrega assets não essenciais para liberar memória
     */
    public unloadNonEssentialAssets(): void {
        const essentialAssets = this.preloadQueue;
        
        this.loadedAssets.forEach((asset, assetId) => {
            if (!essentialAssets.includes(assetId)) {
                this.unloadAsset(assetId);
            }
        });
        
        console.log("[DemoAssets] Assets não essenciais descarregados");
    }

    /**
     * Obtém informações de um asset
     */
    public getAssetInfo(assetId: string): AssetInfo | undefined {
        return this.assetRegistry.get(assetId);
    }

    /**
     * Lista assets por categoria
     */
    public getAssetsByCategory(category: string): AssetInfo[] {
        return Array.from(this.assetRegistry.values())
            .filter(asset => asset.category === category);
    }

    /**
     * Obtém estatísticas de uso de memória
     */
    public getMemoryStats(): any {
        const loadedCount = this.loadedAssets.size;
        const totalSize = Array.from(this.loadedAssets.keys())
            .map(assetId => this.assetRegistry.get(assetId)?.size || 0)
            .reduce((sum, size) => sum + size, 0);

        return {
            loadedAssets: loadedCount,
            totalAssets: this.assetRegistry.size,
            memoryUsage: `${(totalSize / 1024).toFixed(2)} MB`,
            loadingProgress: `${loadedCount}/${this.assetRegistry.size}`
        };
    }

    /**
     * Força recarregamento de um asset
     */
    public async reloadAsset(assetId: string): Promise<any> {
        this.unloadAsset(assetId);
        return await this.loadAsset(assetId);
    }

    /**
     * Pré-carrega assets de uma categoria específica
     */
    public async preloadCategory(category: string): Promise<void> {
        const categoryAssets = this.getAssetsByCategory(category);
        const assetIds = categoryAssets.map(asset => asset.id);
        
        console.log(`[DemoAssets] Pré-carregando categoria ${category}: ${assetIds.length} assets`);
        
        for (const assetId of assetIds) {
            await this.loadAsset(assetId);
        }
    }

    /**
     * Otimiza assets para o perfil atual
     */
    public optimizeForCurrentProfile(): void {
        const profile = performanceOptimizer.getCurrentProfile();
        
        if (profile.isMobile) {
            // Descarregar assets não otimizados para mobile
            this.loadedAssets.forEach((asset, assetId) => {
                const assetInfo = this.assetRegistry.get(assetId);
                if (assetInfo && !assetInfo.mobileOptimized) {
                    this.unloadAsset(assetId);
                }
            });
        }
        
        if (profile.performanceLevel === 'low') {
            // Descarregar assets de alta qualidade
            this.loadedAssets.forEach((asset, assetId) => {
                const assetInfo = this.assetRegistry.get(assetId);
                if (assetInfo && assetInfo.quality === 'high' && !assetInfo.preload) {
                    this.unloadAsset(assetId);
                }
            });
        }
        
        console.log(`[DemoAssets] Assets otimizados para perfil ${profile.name}`);
    }

    /**
     * Função utilitária para delay
     */
    private delay(ms: number): Promise<void> {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Limpa todos os assets carregados
     */
    public cleanup(): void {
        this.loadedAssets.forEach((asset, assetId) => {
            this.unloadAsset(assetId);
        });
        
        this.loadingPromises.clear();
        console.log("[DemoAssets] Cleanup completo realizado");
    }
}

// Instância global do gerenciador de assets da demo
export const demoAssets = new DemoAssets();
