"""
Storm RPG - Raças Élficas
Elfos da Floresta e El<PERSON> (Shade-Elves)
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus, create_darkvision
from core.attributes import AttributeType


class ForestElf(Race):
    """
    Elfos da Floresta - Guardiões da natureza
    
    Os elfos da floresta são uma raça antiga e sábia que vive em harmonia
    com a natureza. Ágeis e graciosos, possuem uma conexão profunda com
    as florestas de Aethra e são mestres no uso do arco e flecha.
    """
    
    def get_name(self) -> str:
        return "<PERSON><PERSON> da Floresta"
    
    def get_description(self) -> str:
        return ("Ágeis e graciosos, os elfos da floresta vivem em harmonia "
                "com a natureza e são mestres arqueiros.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.DEXTERITY, 2, "Agilidade Élfica"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Sentidos Aguçados",
                description="Recebe +2 em testes de Percepção",
                trait_type=TraitType.SKILL_BONUS,
                value=("Percepção", 2)
            ),
            
            RacialTrait(
                name="Resistência a Encantamentos",
                description="Recebe +2 em testes de resistência contra magias de encantamento",
                trait_type=TraitType.RESISTANCE,
                value="encantamento"
            ),
            
            RacialTrait(
                name="Transe Élfico",
                description="Elfos não dormem, mas entram em transe por 4 horas",
                trait_type=TraitType.OTHER,
                value="trance"
            ),
            
            RacialTrait(
                name="Maestria com Arcos",
                description="Recebe proficiência com arcos e +1 em ataques à distância",
                trait_type=TraitType.OTHER,
                value="bow_mastery"
            ),
            
            RacialTrait(
                name="Passo Sombrio",
                description="Pode se mover silenciosamente pela floresta",
                trait_type=TraitType.SKILL_BONUS,
                value=("Furtividade", 2)
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Élfico", "Silvestre"]


class ShadeElf(Race):
    """
    Elfos da Noite (Shade-Elves) - Nascidos sob a Storm
    
    Os elfos da noite são uma subraça élfica nascida sob a influência
    direta da Storm. Possuem pele prateada e olhos violeta, e são
    naturalmente conectados à magia caótica da tempestade.
    """
    
    def get_name(self) -> str:
        return "Elfo da Noite"
    
    def get_description(self) -> str:
        return ("Nascidos sob a Storm, possuem pele prateada e olhos violeta. "
                "São naturalmente conectados à magia caótica.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CHARISMA, 2, "Presença da Storm"),
            
            RacialTrait(
                name="Visão na Penumbra",
                description="Pode ver na penumbra como se fosse luz normal",
                trait_type=TraitType.SPECIAL_SENSE,
                value="low_light_vision"
            ),
            
            RacialTrait(
                name="Marca da Storm",
                description="Veios luminosos sob a pele brilham quando usa magia",
                trait_type=TraitType.OTHER,
                value="storm_mark"
            ),
            
            RacialTrait(
                name="Magia Inata - Névoa da Tempestade",
                description="Pode conjurar Névoa da Tempestade 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="storm_mist"
            ),
            
            RacialTrait(
                name="Resistência à Storm",
                description="Recebe +2 em testes de resistência contra efeitos da Storm",
                trait_type=TraitType.RESISTANCE,
                value="storm"
            ),
            
            RacialTrait(
                name="Sensibilidade Mágica",
                description="Pode detectar magia ativa num raio de 9m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="detect_magic"
            ),
            
            RacialTrait(
                name="Transe Élfico",
                description="Elfos não dormem, mas entram em transe por 4 horas",
                trait_type=TraitType.OTHER,
                value="trance"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Élfico", "Primordial"]
    
    def apply_to_character(self, character):
        """Aplica características dos elfos da noite"""
        super().apply_to_character(character)
        
        # Elfos da noite ganham pontos de mana extras
        character.max_mana_points += 2
        character.mana_points = character.max_mana_points


class HighElf(Race):
    """
    Altos Elfos - Mestres da magia arcana
    
    Uma subraça élfica dedicada ao estudo da magia arcana.
    Vivem em torres cristalinas e são os guardiões do conhecimento
    mágico mais antigo de Aethra.
    """
    
    def get_name(self) -> str:
        return "Alto Elfo"
    
    def get_description(self) -> str:
        return ("Mestres da magia arcana, vivem em torres cristalinas e "
                "guardam o conhecimento mágico mais antigo.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.INTELLIGENCE, 2, "Intelecto Arcano"),
            
            create_darkvision(18),
            
            RacialTrait(
                name="Conhecimento Arcano",
                description="Recebe +2 em testes de Conhecimento Arcano",
                trait_type=TraitType.SKILL_BONUS,
                value=("Conhecimento Arcano", 2)
            ),
            
            RacialTrait(
                name="Magia Inata - Truque",
                description="Conhece um truque arcano à escolha",
                trait_type=TraitType.SPELL_LIKE,
                value="cantrip"
            ),
            
            RacialTrait(
                name="Resistência a Encantamentos",
                description="Recebe +2 em testes de resistência contra magias de encantamento",
                trait_type=TraitType.RESISTANCE,
                value="encantamento"
            ),
            
            RacialTrait(
                name="Memória Ancestral",
                description="Pode acessar memórias élficas antigas 1x por dia",
                trait_type=TraitType.OTHER,
                value="ancestral_memory"
            ),
            
            RacialTrait(
                name="Transe Élfico",
                description="Elfos não dormem, mas entram em transe por 4 horas",
                trait_type=TraitType.OTHER,
                value="trance"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Élfico", "Arcano", "Celestial"]


class WoodElf(ForestElf):
    """
    Elfos Selvagens - Mais primitivos que os elfos da floresta
    
    Uma subraça élfica que vive nas partes mais selvagens das florestas,
    mantendo tradições ainda mais antigas e primitivas.
    """
    
    def get_name(self) -> str:
        return "Elfo Selvagem"
    
    def get_description(self) -> str:
        return ("Vivem nas partes mais selvagens das florestas, mantendo "
                "tradições primitivas e uma conexão profunda com a natureza.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Adiciona características selvagens
        traits.append(
            RacialTrait(
                name="Comunicação Animal",
                description="Pode se comunicar com animais da floresta",
                trait_type=TraitType.SPELL_LIKE,
                value="speak_with_animals"
            )
        )
        
        traits.append(
            RacialTrait(
                name="Camuflagem Natural",
                description="Recebe +4 em Furtividade em ambientes naturais",
                trait_type=TraitType.SKILL_BONUS,
                value=("Furtividade", 4)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Élfico", "Silvestre", "Druídico"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste das Raças Élficas ===")
    
    forest_elf = ForestElf()
    print(forest_elf.get_summary())
    
    print(f"\n" + "="*50)
    
    shade_elf = ShadeElf()
    print(shade_elf.get_summary())
    
    print(f"\n" + "="*50)
    
    high_elf = HighElf()
    print(high_elf.get_summary())
