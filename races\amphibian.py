"""
Storm RPG - <PERSON><PERSON> (Anfíbios)
Originários de pântanos profundos
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus
from core.attributes import AttributeType


class Luran(Race):
    """
    Lurans - Anfíbios dos pântanos
    
    Os lurans são uma raça anfíbia que habita os pântanos profundos
    de Vorth. Com pele escamosa e capacidade de respirar tanto na
    água quanto no ar, são mestres de seu ambiente aquático.
    """
    
    def get_name(self) -> str:
        return "Luran"
    
    def get_description(self) -> str:
        return ("Anfíbios dos pântanos com pele escamosa. Podem respirar "
                "tanto na água quanto no ar, sendo mestres aquáticos.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CONSTITUTION, 2, "Resistência Anfíbia"),
            create_attribute_bonus(AttributeType.WISDOM, 1, "Instintos Aquáticos"),
            
            RacialTrait(
                name="Respiração Anfíbia",
                description="Pode respirar tanto no ar quanto na água indefinidamente",
                trait_type=TraitType.OTHER,
                value="amphibious"
            ),
            
            RacialTrait(
                name="Velocidade de Natação",
                description="Velocidade de natação igual à velocidade terrestre (9m)",
                trait_type=TraitType.MOVEMENT,
                value=("swim", 9)
            ),
            
            RacialTrait(
                name="Pele Escamosa",
                description="Recebe +1 de bônus natural na CA",
                trait_type=TraitType.OTHER,
                value="natural_armor"
            ),
            
            RacialTrait(
                name="Visão Aquática",
                description="Pode ver claramente debaixo d'água até 18m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="underwater_vision"
            ),
            
            RacialTrait(
                name="Resistência a Veneno",
                description="Recebe +2 em testes de resistência contra veneno e doenças",
                trait_type=TraitType.RESISTANCE,
                value="poison_disease"
            ),
            
            RacialTrait(
                name="Salto Poderoso",
                description="Pode saltar distâncias impressionantes (+4 em testes de Atletismo para saltar)",
                trait_type=TraitType.SKILL_BONUS,
                value=("Atletismo", 4)
            ),
            
            RacialTrait(
                name="Conhecimento dos Pântanos",
                description="Recebe +2 em testes de Sobrevivência em ambientes aquáticos",
                trait_type=TraitType.SKILL_BONUS,
                value=("Sobrevivência", 2)
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Luran", "Aquático"]
    
    def apply_to_character(self, character):
        """Aplica características luran ao personagem"""
        super().apply_to_character(character)
        
        # Lurans ganham CA natural
        character.armor_class += 1
        
        # Podem nadar sem penalidades
        character.add_feature("Natação: Nada na velocidade normal sem penalidades")


class DeepLuran(Luran):
    """
    Luran das Profundezas - Adaptado às águas mais profundas
    
    Uma subraça luran que vive nas profundezas aquáticas
    e desenvolveu adaptações para pressão e escuridão.
    """
    
    def get_name(self) -> str:
        return "Luran das Profundezas"
    
    def get_description(self) -> str:
        return ("Adaptados às profundezas aquáticas, desenvolveram "
                "resistência à pressão e visão no escuro.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Adiciona visão no escuro
        traits.append(
            RacialTrait(
                name="Visão no Escuro Aquático",
                description="Pode ver no escuro até 18m, mesmo debaixo d'água",
                trait_type=TraitType.SPECIAL_SENSE,
                value="darkvision_aquatic"
            )
        )
        
        # Adiciona resistência à pressão
        traits.append(
            RacialTrait(
                name="Resistência à Pressão",
                description="Imune a efeitos de pressão aquática",
                trait_type=TraitType.IMMUNITY,
                value="pressure"
            )
        )
        
        return traits


class ToxicLuran(Luran):
    """
    Luran Tóxico - Com glândulas venenosas
    
    Uma subraça luran que desenvolveu glândulas venenosas
    como mecanismo de defesa.
    """
    
    def get_name(self) -> str:
        return "Luran Tóxico"
    
    def get_description(self) -> str:
        return ("Desenvolveram glândulas venenosas como defesa, "
                "podendo secretar toxinas através da pele.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui resistência a veneno por imunidade
        traits[6] = RacialTrait(
            name="Imunidade a Veneno",
            description="Imune a todos os venenos e doenças",
            trait_type=TraitType.IMMUNITY,
            value="poison_disease"
        )
        
        # Adiciona secreção tóxica
        traits.append(
            RacialTrait(
                name="Secreção Tóxica",
                description="Pode secretar veneno pela pele 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="poison_skin"
            )
        )
        
        # Adiciona mordida venenosa
        traits.append(
            RacialTrait(
                name="Mordida Venenosa",
                description="Mordida causa veneno (CD 12 + nível + Con)",
                trait_type=TraitType.NATURAL_WEAPON,
                value={"damage": "1d4", "poison": True}
            )
        )
        
        return traits


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Raça Luran ===")
    
    luran = Luran()
    print(luran.get_summary())
    
    print(f"\n" + "="*50)
    
    deep_luran = DeepLuran()
    print(deep_luran.get_summary())
    
    print(f"\n" + "="*50)
    
    toxic_luran = ToxicLuran()
    print(toxic_luran.get_summary())
