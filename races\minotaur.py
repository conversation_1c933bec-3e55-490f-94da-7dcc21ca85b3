"""
Storm RPG - Raça Minotauro
Fortes e com faro sobrenatural
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus
from core.attributes import AttributeType


class Minotaur(Race):
    """
    Minotauros - Fortes e com faro sobrenatural
    
    Os minotauros são uma raça poderosa que habita as planícies de Taurun.
    Com corpos humanoides e cabeças de touro, possuem força impressionante
    e um faro aguçado que os torna excelentes rastreadores e guerreiros.
    """
    
    def get_name(self) -> str:
        return "Minotauro"
    
    def get_description(self) -> str:
        return ("Poderosos humanoides com cabeças de touro. Possuem força "
                "impressionante e faro aguçado que os torna excelentes guerreiros.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.STRENGTH, 2, "Força do Touro"),
            create_attribute_bonus(AttributeType.CONSTITUTION, 1, "Resistência Bestial"),
            
            RacialTrait(
                name="Chifres",
                description="Arma natural que causa 1d8 de dano perfurante",
                trait_type=TraitType.NATURAL_WEAPON,
                value={"damage": "1d8", "type": "perfurante"}
            ),
            
            RacialTrait(
                name="Faro Aguçado",
                description="Pode rastrear por cheiro e detectar criaturas num raio de 9m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="scent"
            ),
            
            RacialTrait(
                name="Investida Poderosa",
                description="Ao correr 6m em linha reta, causa +1d8 de dano com chifres",
                trait_type=TraitType.OTHER,
                value="powerful_charge"
            ),
            
            RacialTrait(
                name="Sobrevivência Natural",
                description="Recebe +2 em testes de Sobrevivência e Percepção",
                trait_type=TraitType.SKILL_BONUS,
                value=("Sobrevivência", 2)
            ),
            
            RacialTrait(
                name="Resistência a Medo",
                description="Recebe +2 em testes de resistência contra medo",
                trait_type=TraitType.RESISTANCE,
                value="fear"
            ),
            
            RacialTrait(
                name="Senso de Direção",
                description="Nunca se perde e sempre sabe a direção do norte",
                trait_type=TraitType.OTHER,
                value="direction_sense"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Minotauro"]
    
    def apply_to_character(self, character):
        """Aplica características minotauro ao personagem"""
        super().apply_to_character(character)
        
        # Minotauros podem usar chifres como arma
        character.add_feature("Chifres: Arma natural 1d8 perfurante")


class BullMinotaur(Minotaur):
    """
    Minotauro Touro - Focado em força e resistência
    
    Uma variante mais robusta dos minotauros, com
    características ainda mais bovinas.
    """
    
    def get_name(self) -> str:
        return "Minotauro Touro"
    
    def get_description(self) -> str:
        return ("Variante mais robusta dos minotauros, com "
                "características bovinas mais pronunciadas.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Aumenta bônus de Força
        traits[0] = create_attribute_bonus(AttributeType.STRENGTH, 3, "Força do Touro Superior")
        
        # Adiciona resistência extra
        traits.append(
            RacialTrait(
                name="Couro Resistente",
                description="Recebe +1 de bônus natural na CA",
                trait_type=TraitType.OTHER,
                value="natural_armor"
            )
        )
        
        return traits


class WildMinotaur(Minotaur):
    """
    Minotauro Selvagem - Conectado com a natureza
    
    Minotauros que vivem nas florestas e desenvolveram
    uma conexão mais profunda com a natureza.
    """
    
    def get_name(self) -> str:
        return "Minotauro Selvagem"
    
    def get_description(self) -> str:
        return ("Minotauros que vivem nas florestas e desenvolveram "
                "conexão profunda com a natureza.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui alguns traços por habilidades naturais
        traits[1] = create_attribute_bonus(AttributeType.WISDOM, 1, "Sabedoria Selvagem")
        
        # Adiciona habilidades naturais
        traits.append(
            RacialTrait(
                name="Comunicação Animal",
                description="Pode se comunicar com animais bovinos e cervídeos",
                trait_type=TraitType.SPELL_LIKE,
                value="speak_with_animals"
            )
        )
        
        traits.append(
            RacialTrait(
                name="Camuflagem Natural",
                description="Recebe +2 em Furtividade em ambientes naturais",
                trait_type=TraitType.SKILL_BONUS,
                value=("Furtividade", 2)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Minotauro", "Silvestre"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Raça Minotauro ===")
    
    minotaur = Minotaur()
    print(minotaur.get_summary())
    
    print(f"\n" + "="*50)
    
    bull_minotaur = BullMinotaur()
    print(bull_minotaur.get_summary())
    
    print(f"\n" + "="*50)
    
    wild_minotaur = WildMinotaur()
    print(wild_minotaur.get_summary())
