/**
 * Storm RPG - Constants
 * Constantes globais do jogo
 */

export const GAME_CONFIG = {
    // Configurações gerais
    MAX_PLAYERS: 4,
    RESPAWN_TIME: 5000, // 5 segundos
    GAME_DURATION: 1800000, // 30 minutos
    
    // Sistema de combate
    BASE_ARMOR_CLASS: 10,
    CRITICAL_HIT_THRESHOLD: 20,
    CRITICAL_FAIL_THRESHOLD: 1,
    
    // Experiência e progressão
    XP_PER_ENEMY_KILL: 100,
    XP_PER_GEM_COLLECTED: 10,
    XP_TO_LEVEL_UP: 1000,
    MAX_LEVEL: 10,
    
    // Pontos de vida e mana
    BASE_HP_PER_LEVEL: 8,
    BASE_MANA_PER_LEVEL: 4,
    
    // Cooldowns (em milissegundos)
    SPELL_COOLDOWN: 3000,
    POTION_COOLDOWN: 10000,
    SPECIAL_ABILITY_COOLDOWN: 30000
};

export const CLA<PERSON>_CONFIGS = {
    WARRIOR: {
        name: "<PERSON>uerre<PERSON>",
        hitDie: 10,
        baseAttributes: {
            strength: 16,
            dexterity: 12,
            constitution: 14,
            intelligence: 8,
            wisdom: 10,
            charisma: 10
        },
        primaryAttribute: "strength",
        spellcasting: false,
        armorProficiency: ["light", "medium", "heavy"],
        weaponProficiency: ["simple", "martial"]
    },
    
    MAGE: {
        name: "Mago",
        hitDie: 6,
        baseAttributes: {
            strength: 8,
            dexterity: 14,
            constitution: 10,
            intelligence: 18,
            wisdom: 12,
            charisma: 10
        },
        primaryAttribute: "intelligence",
        spellcasting: true,
        armorProficiency: ["light"],
        weaponProficiency: ["simple"]
    },
    
    ROGUE: {
        name: "Ladino",
        hitDie: 8,
        baseAttributes: {
            strength: 10,
            dexterity: 18,
            constitution: 12,
            intelligence: 12,
            wisdom: 14,
            charisma: 14
        },
        primaryAttribute: "dexterity",
        spellcasting: false,
        armorProficiency: ["light"],
        weaponProficiency: ["simple", "finesse"]
    }
};

export const SPELL_CONFIGS = {
    FIREBALL: {
        name: "Bola de Fogo",
        manaCost: 6,
        damage: "6d6",
        range: 30,
        areaOfEffect: 6,
        castTime: 1000,
        cooldown: 5000,
        particleEffect: "fireball_explosion",
        soundEffect: "fireball_cast"
    },
    
    HEAL: {
        name: "Cura",
        manaCost: 4,
        healing: "2d8+2",
        range: 9,
        castTime: 500,
        cooldown: 3000,
        particleEffect: "healing_light",
        soundEffect: "heal_cast"
    },
    
    STORM_BOLT: {
        name: "Raio da Tempestade",
        manaCost: 3,
        damage: "3d6",
        range: 24,
        castTime: 750,
        cooldown: 4000,
        particleEffect: "lightning_bolt",
        soundEffect: "thunder_crack"
    }
};

export const ENEMY_CONFIGS = {
    GOBLIN: {
        name: "Goblin",
        maxHealth: 15,
        armorClass: 13,
        attackBonus: 4,
        damage: "1d6+2",
        speed: 6,
        xpReward: 50,
        lootTable: ["gem_small", "potion_health"]
    },
    
    STORM_ELEMENTAL: {
        name: "Elemental da Tempestade",
        maxHealth: 45,
        armorClass: 16,
        attackBonus: 7,
        damage: "2d8+3",
        speed: 9,
        xpReward: 200,
        lootTable: ["gem_large", "storm_crystal"],
        abilities: ["storm_blast", "lightning_aura"]
    }
};

export const ITEM_CONFIGS = {
    GEM_SMALL: {
        name: "Gema Pequena",
        value: 10,
        rarity: "common",
        effect: "currency"
    },
    
    GEM_LARGE: {
        name: "Gema Grande",
        value: 50,
        rarity: "uncommon",
        effect: "currency"
    },
    
    POTION_HEALTH: {
        name: "Poção de Vida",
        healing: "2d4+2",
        rarity: "common",
        effect: "instant_heal"
    },
    
    STORM_CRYSTAL: {
        name: "Cristal da Tempestade",
        manaRestore: 10,
        rarity: "rare",
        effect: "mana_restore"
    }
};

export const UI_CONSTANTS = {
    // Cores da interface
    COLORS: {
        PRIMARY: "#4A90E2",
        SECONDARY: "#7B68EE",
        SUCCESS: "#5CB85C",
        WARNING: "#F0AD4E",
        DANGER: "#D9534F",
        STORM: "#9932CC"
    },
    
    // Posições da UI
    HUD_POSITIONS: {
        HEALTH_BAR: { x: 50, y: 50 },
        MANA_BAR: { x: 50, y: 80 },
        MINIMAP: { x: 850, y: 50 },
        CHAT: { x: 50, y: 400 },
        SCOREBOARD: { x: 400, y: 50 }
    },
    
    // Tamanhos
    BUTTON_SIZE: { width: 120, height: 40 },
    BAR_SIZE: { width: 200, height: 20 },
    ICON_SIZE: 32
};

export const ASSET_IDS = {
    // Efeitos de partículas (IDs fictícios - substituir pelos reais)
    PARTICLES: {
        FIREBALL_EXPLOSION: "particle_fireball_001",
        HEALING_LIGHT: "particle_heal_001",
        LIGHTNING_BOLT: "particle_lightning_001",
        LEVEL_UP: "particle_levelup_001",
        DEATH_EXPLOSION: "particle_death_001"
    },
    
    // Efeitos sonoros
    SOUNDS: {
        FIREBALL_CAST: "sound_fireball_001",
        HEAL_CAST: "sound_heal_001",
        THUNDER_CRACK: "sound_thunder_001",
        SWORD_HIT: "sound_sword_001",
        LEVEL_UP: "sound_levelup_001",
        ENEMY_DEATH: "sound_death_001",
        GEM_PICKUP: "sound_pickup_001"
    },
    
    // Modelos 3D
    MODELS: {
        GOBLIN: "model_goblin_001",
        STORM_ELEMENTAL: "model_elemental_001",
        GEM_SMALL: "model_gem_small_001",
        GEM_LARGE: "model_gem_large_001",
        POTION: "model_potion_001",
        CRYSTAL: "model_crystal_001"
    }
};

export const WORLD_SETTINGS = {
    // Configurações do mundo
    GRAVITY: -9.81,
    DAY_CYCLE_DURATION: 600000, // 10 minutos
    WEATHER_CHANGE_INTERVAL: 120000, // 2 minutos
    
    // Spawn points
    PLAYER_SPAWN_POINTS: [
        { x: 0, y: 1, z: 0 },
        { x: 5, y: 1, z: 0 },
        { x: -5, y: 1, z: 0 },
        { x: 0, y: 1, z: 5 }
    ],
    
    // Áreas do mundo
    COMBAT_ZONES: [
        { center: { x: 20, y: 0, z: 20 }, radius: 15 },
        { center: { x: -20, y: 0, z: -20 }, radius: 15 }
    ],
    
    SAFE_ZONES: [
        { center: { x: 0, y: 0, z: 0 }, radius: 10 }
    ]
};

// Enums para tipagem
export enum PlayerClass {
    WARRIOR = "WARRIOR",
    MAGE = "MAGE",
    ROGUE = "ROGUE"
}

export enum SpellType {
    FIREBALL = "FIREBALL",
    HEAL = "HEAL",
    STORM_BOLT = "STORM_BOLT"
}

export enum EnemyType {
    GOBLIN = "GOBLIN",
    STORM_ELEMENTAL = "STORM_ELEMENTAL"
}

export enum ItemType {
    GEM_SMALL = "GEM_SMALL",
    GEM_LARGE = "GEM_LARGE",
    POTION_HEALTH = "POTION_HEALTH",
    STORM_CRYSTAL = "STORM_CRYSTAL"
}

export enum GameState {
    WAITING_FOR_PLAYERS = "WAITING_FOR_PLAYERS",
    STARTING = "STARTING",
    IN_PROGRESS = "IN_PROGRESS",
    ENDING = "ENDING",
    FINISHED = "FINISHED"
}
