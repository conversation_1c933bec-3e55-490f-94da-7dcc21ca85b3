# 🌩️ Storm RPG - Unreal Engine 5 Implementation

> **Complete d20 RPG system for Unreal Engine 5 with multiple dice visualization options**

## 📋 Overview

This is the **Unreal Engine 5** implementation of the Storm RPG dice system, providing three different approaches to dice rolling:

1. **🧮 Pure Logic** - Fast, no visuals, perfect for turn-based combat
2. **🎨 2D UI Dice** - Beautiful animated interface for dialogue and inventory
3. **🎲 3D Physics Dice** - Cinematic realistic dice for boss fights and tavern games

## 🏗️ Project Structure

```
unreal-engine/
├── Source/StormRPG/
│   ├── Public/
│   │   ├── Dice/
│   │   │   └── StormDiceLibrary.h          # Core dice system
│   │   ├── UI/
│   │   │   └── StormDiceWidget.h           # 2D UI dice widget
│   │   ├── Actors/
│   │   │   └── StormPhysicalDice.h         # 3D physics dice
│   │   └── GAS/
│   │       └── StormAttackAbility.h        # Gameplay Ability integration
│   └── Private/
│       ├── Dice/
│       │   └── StormDiceLibrary.cpp        # Implementation
│       └── [Other implementations...]
├── Content/
│   ├── Blueprints/
│   │   ├── Dice/
│   │   │   ├── BP_DiceManager              # Main dice coordinator
│   │   │   ├── BP_PhysicalDice_D20         # Physical d20 blueprint
│   │   │   └── WB_DiceUI                   # UI widget blueprint
│   │   └── GAS/
│   │       ├── GA_StormAttack              # Attack ability
│   │       ├── GA_StormSpell               # Spell casting ability
│   │       └── GE_Damage                   # Damage effect
│   ├── UI/
│   │   ├── Textures/
│   │   │   ├── DiceFaces/                  # Dice face textures
│   │   │   └── UI_Elements/                # UI graphics
│   │   └── Materials/
│   │       └── M_DiceUI                    # UI materials
│   ├── Meshes/
│   │   ├── Dice/
│   │   │   ├── SM_D4                       # 4-sided die
│   │   │   ├── SM_D6                       # 6-sided die
│   │   │   ├── SM_D8                       # 8-sided die
│   │   │   ├── SM_D10                      # 10-sided die
│   │   │   ├── SM_D12                      # 12-sided die
│   │   │   └── SM_D20                      # 20-sided die
│   │   └── Materials/
│   │       ├── M_Dice_Standard             # Standard dice material
│   │       ├── M_Dice_Critical             # Critical hit material
│   │       └── M_Dice_Storm                # Storm-themed material
│   ├── Audio/
│   │   ├── Dice/
│   │   │   ├── SFX_DiceRoll                # Rolling sound
│   │   │   ├── SFX_DiceBounce              # Bounce sound
│   │   │   ├── SFX_DiceSettle              # Settle sound
│   │   │   └── SFX_Critical                # Critical hit sound
│   │   └── UI/
│   │       ├── SFX_UIClick                 # UI click sound
│   │       └── SFX_UIHover                 # UI hover sound
│   └── Particles/
│       ├── P_DiceRoll                      # Dice roll effect
│       ├── P_Critical                      # Critical hit effect
│       ├── P_Fumble                        # Critical failure effect
│       └── P_StormSurge                    # Storm surge effect
└── Config/
    ├── DefaultEngine.ini                   # Engine configuration
    ├── DefaultGame.ini                     # Game configuration
    └── DefaultInput.ini                    # Input bindings
```

## 🚀 Quick Start

### 1. Setup Project

1. **Create new UE5 project** with C++ support
2. **Copy source files** to `Source/StormRPG/`
3. **Generate project files** and compile
4. **Import content** from `Content/` folder

### 2. Basic Usage

#### Pure Logic (Fastest)
```cpp
// In any Blueprint or C++ class
#include "Dice/StormDiceLibrary.h"

// Roll a d20 with modifier
FStormDiceResult Result = UStormDiceLibrary::RollD20(5);
UE_LOG(LogTemp, Log, TEXT("Rolled: %d"), Result.Total);

// Roll attack vs AC
FStormDiceResult Attack = UStormDiceLibrary::RollAttack(8, 15);
bool bHit = UStormDiceLibrary::CheckSuccess(Attack, 15);
```

#### 2D UI Dice
```cpp
// In your HUD or UI controller
if (DiceWidget)
{
    DiceWidget->RollDiceAnimated("2d6+3");
    DiceWidget->OnDiceAnimationComplete.AddDynamic(this, &AMyController::OnDiceResult);
}
```

#### 3D Physics Dice
```cpp
// Spawn and roll physical dice
AStormPhysicalDice* PhysicalDice = GetWorld()->SpawnActor<AStormPhysicalDice>();
PhysicalDice->SetDiceType(EDiceType::D20);
PhysicalDice->RollDice();
PhysicalDice->OnDiceResult.AddDynamic(this, &AMyActor::OnPhysicalDiceResult);
```

### 3. Console Commands

Open console (`~` key) and try:
```
StormRoll 1d20+5
StormRoll 2d6+3
StormRoll 1d8
StormRollPhysical 1d20
StormClearDice
```

## 🎯 Implementation Guide

### Method 1: Pure Logic (30 minutes)

**Best for:** Turn-based combat, background calculations, server-side rolls

```cpp
// Example: Simple attack calculation
int32 AttackBonus = 8;
int32 TargetAC = 15;

FStormDiceResult AttackRoll = UStormDiceLibrary::RollAttack(AttackBonus, TargetAC);

if (UStormDiceLibrary::CheckSuccess(AttackRoll, TargetAC))
{
    // Hit! Roll damage
    int32 Damage = UStormDiceLibrary::RollWithModifier(1, 8, 3); // 1d8+3
    
    if (AttackRoll.bIsCriticalHit)
    {
        Damage *= 2; // Double damage on crit
        UE_LOG(LogTemp, Warning, TEXT("CRITICAL HIT! %d damage"), Damage);
    }
}
```

### Method 2: 2D UI Dice (2 hours)

**Best for:** Player-facing rolls, dialogue checks, inventory interactions

1. **Create Widget Blueprint** based on `UStormDiceWidget`
2. **Add to viewport** in your HUD or menu
3. **Bind events** for roll completion

```cpp
// In your PlayerController or HUD
void AMyPlayerController::ShowDiceRoll(const FString& Expression)
{
    if (!DiceWidget)
    {
        DiceWidget = CreateWidget<UStormDiceWidget>(this, DiceWidgetClass);
        DiceWidget->AddToViewport();
    }
    
    DiceWidget->RollDiceAnimated(Expression);
}

void AMyPlayerController::OnDiceResult(FStormDiceResult Result)
{
    // Handle the result
    if (Result.bIsCriticalHit)
    {
        // Play special effects
        PlayCriticalHitEffects();
    }
}
```

### Method 3: 3D Physics Dice (4-8 hours)

**Best for:** Cinematic moments, boss fights, tavern mini-games

1. **Create dice meshes** in Blender (exact 1-unit cubes)
2. **Import with proper collision** (Box Simplified)
3. **Setup face arrows** pointing to each face normal
4. **Configure physics** settings for realistic rolling

```cpp
// Example: Cinematic boss fight dice roll
void AMyBossActor::PerformSpecialAttack()
{
    // Spawn dramatic dice roll
    FVector SpawnLocation = GetActorLocation() + FVector(0, 0, 200);
    AStormPhysicalDice* Dice = GetWorld()->SpawnActor<AStormPhysicalDice>(
        PhysicalDiceClass, SpawnLocation, FRotator::ZeroRotator);
    
    Dice->SetDiceType(EDiceType::D20);
    Dice->OnDiceResult.AddDynamic(this, &AMyBossActor::OnSpecialAttackRoll);
    
    // Add dramatic force
    FVector Force = FVector(
        FMath::RandRange(-500, 500),
        FMath::RandRange(-500, 500),
        FMath::RandRange(600, 1000)
    );
    Dice->RollDiceWithForce(Force, FVector(100, 200, 150));
}
```

## 🎮 Gameplay Ability System Integration

### Attack Ability Example

```cpp
// In GA_StormAttack Blueprint or C++
void UStormAttackAbility::ActivateAbility(...)
{
    // Get target and attack bonus
    AActor* Target = GetTargetActor();
    int32 AttackBonus = GetAttackBonus();
    int32 TargetAC = GetTargetArmorClass();
    
    // Roll attack
    FStormDiceResult AttackRoll = UStormDiceLibrary::RollAttack(
        AttackBonus, TargetAC, HasAdvantage(), HasDisadvantage());
    
    // Apply gameplay tags based on result
    if (AttackRoll.bIsCriticalHit)
    {
        ApplyGameplayTagToOwner(CriticalHitTag);
    }
    else if (AttackRoll.bIsCriticalFail)
    {
        ApplyGameplayTagToOwner(CriticalMissTag);
    }
    
    // Continue with damage if hit
    if (UStormDiceLibrary::CheckSuccess(AttackRoll, TargetAC))
    {
        PerformDamageRoll(AttackRoll.bIsCriticalHit);
    }
}
```

## 🔧 Configuration

### Dice Physics Settings

```cpp
// In BP_PhysicalDice or C++
FDicePhysicsSettings PhysicsSettings;
PhysicsSettings.Mass = 0.1f;              // Light dice
PhysicsSettings.LinearDamping = 0.3f;     // Air resistance
PhysicsSettings.AngularDamping = 0.5f;    // Rotation resistance
PhysicsSettings.Restitution = 0.4f;       // Bounciness
PhysicsSettings.Friction = 0.7f;          // Surface friction
PhysicsSettings.MinImpulseForce = 300.0f; // Minimum roll force
PhysicsSettings.MaxImpulseForce = 800.0f; // Maximum roll force
```

### UI Appearance

```cpp
// In WB_DiceUI Blueprint
DiceColors.Add(6, FLinearColor::Red);      // d6 = Red
DiceColors.Add(20, FLinearColor::Blue);    // d20 = Blue
RollAnimationDuration = 1.5f;              // 1.5 second animation
RollAnimationFrames = 20;                  // 20 intermediate frames
```

## 🌐 Multiplayer Support

### Replication Setup

```cpp
// In AStormPhysicalDice
void AStormPhysicalDice::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);
    
    DOREPLIFETIME(AStormPhysicalDice, ReplicatedResult);
    DOREPLIFETIME(AStormPhysicalDice, bReplicatedIsRolling);
}

// Server authoritative rolling
void AStormPhysicalDice::ServerRollDice_Implementation(const FVector& Force, const FVector& Torque)
{
    if (HasAuthority())
    {
        // Only server calculates physics
        RollDiceWithForce(Force, Torque);
        
        // Replicate visual effects to all clients
        MulticastShowRollEffects(Force, Torque);
    }
}
```

## 🎨 Customization Examples

### Adding New Dice Type

```cpp
// 1. Add to enum
enum class EDiceType : uint8
{
    // ... existing types
    D30     UMETA(DisplayName = "d30")  // New 30-sided die
};

// 2. Update GetNumFacesForDiceType
int32 AStormPhysicalDice::GetNumFacesForDiceType(EDiceType DiceType)
{
    switch (DiceType)
    {
        // ... existing cases
        case EDiceType::D30: return 30;
        default: return 20;
    }
}

// 3. Add mesh and materials in Blueprint
```

### Custom Storm Effects

```cpp
// Storm-specific dice rolling
FStormDiceResult UStormDiceLibrary::RollStormSurge(int32 CasterLevel)
{
    FStormDiceResult Result = RollD20(CasterLevel);
    
    // Storm surge chaos effects
    if (Result.Total >= 25)
    {
        // Major chaos effect
        TriggerChaosEffect(EChaosLevel::Major);
    }
    else if (Result.Total >= 20)
    {
        // Minor chaos effect
        TriggerChaosEffect(EChaosLevel::Minor);
    }
    
    return Result;
}
```

## 🐛 Debugging

### Console Commands

```cpp
// Debug dice rolling
UFUNCTION(Exec)
void StormRoll(const FString& Expression)
{
    FStormDiceResult Result = UStormDiceLibrary::RollExpression(Expression);
    UE_LOG(LogTemp, Warning, TEXT("STORM ROLL: %s → %d"), *Expression, Result.Total);
    
    if (GEngine)
    {
        GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Yellow, 
            FString::Printf(TEXT("%s = %d"), *Expression, Result.Total));
    }
}
```

### Logging

```cpp
// Enable detailed dice logging
UE_LOG(LogTemp, VeryVerbose, TEXT("StormDice Advantage: %d, %d → %d"), Roll1, Roll2, FinalRoll);
```

## 📊 Performance Notes

### Optimization Tips

1. **Use Pure Logic** for frequent calculations
2. **Pool Physical Dice** instead of spawning/destroying
3. **Limit UI Animations** to visible dice only
4. **Cache Dice Meshes** and materials
5. **Use Object Pooling** for particle effects

```cpp
// Example: Dice pooling
class STORMRPG_API UDicePool : public UObject
{
    TArray<AStormPhysicalDice*> AvailableDice;
    
    AStormPhysicalDice* GetDice()
    {
        if (AvailableDice.Num() > 0)
        {
            return AvailableDice.Pop();
        }
        return SpawnNewDice();
    }
    
    void ReturnDice(AStormPhysicalDice* Dice)
    {
        Dice->ResetDice();
        Dice->SetActorHiddenInGame(true);
        AvailableDice.Add(Dice);
    }
};
```

## 🎯 Best Practices

### When to Use Each Method

| Method | Use Case | Performance | Visual Impact |
|--------|----------|-------------|---------------|
| **Pure Logic** | Background calculations, AI decisions, server-side | ⭐⭐⭐⭐⭐ | ⭐ |
| **2D UI** | Player interactions, dialogue, inventory | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| **3D Physics** | Cinematic moments, boss fights, mini-games | ⭐⭐ | ⭐⭐⭐⭐⭐ |

### Code Organization

```cpp
// Good: Centralized dice management
class AStormDiceManager : public AActor
{
    void RollForContext(const FString& Context, const FString& Expression, bool bUsePhysical = false);
};

// Usage
DiceManager->RollForContext("Attack", "1d20+8", false);      // UI dice
DiceManager->RollForContext("Boss Special", "1d20+15", true); // Physical dice
```

---

## 📚 Additional Resources

- **Unreal Engine 5 Documentation:** [UE5 Docs](https://docs.unrealengine.com/5.0/)
- **Gameplay Ability System:** [GAS Documentation](https://docs.unrealengine.com/5.0/gameplay-ability-system-for-unreal-engine/)
- **Blueprint Visual Scripting:** [Blueprint Docs](https://docs.unrealengine.com/5.0/blueprints-visual-scripting-in-unreal-engine/)
- **Physics Simulation:** [Physics Documentation](https://docs.unrealengine.com/5.0/physics-simulation-in-unreal-engine/)

---

**Created for Storm RPG - Tormenta 20 adaptation for Unreal Engine 5**  
*May your dice always roll high!* 🎲⚡
