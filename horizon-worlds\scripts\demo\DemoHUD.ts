/**
 * Storm RPG - Demo HUD System
 * Interface específica para o modo demonstração
 */

import { stormEvents } from '../utils/EventSystem';
import { UI_CONSTANTS } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const UI: any;

export interface DemoHUDData {
    playerId: string;
    objective: string;
    timer: string;
    progress: string;
    hints?: string[];
}

export class DemoHUD {
    private hudElements: Map<string, any> = new Map();
    private notificationQueue: Map<string, any[]> = new Map();

    constructor() {
        this.setupEventListeners();
        console.log("[DemoHUD] Sistema de HUD da demo inicializado");
    }

    /**
     * Configura listeners de eventos
     */
    private setupEventListeners(): void {
        // Criar HUD para jogador
        stormEvents.eventSystem.on('create_demo_hud', (data: DemoHUDData) => {
            this.createHUD(data);
        });

        // Atualizar HUD
        stormEvents.eventSystem.on('update_demo_hud', (data: DemoHUDData) => {
            this.updateHUD(data);
        });

        // Mostrar notificação
        stormEvents.eventSystem.on('show_notification', (data: any) => {
            this.showNotification(data.playerId, data.message, data.duration);
        });

        // Remover HUD
        stormEvents.eventSystem.on('remove_demo_hud', (data: any) => {
            this.removeHUD(data.playerId);
        });
    }

    /**
     * Cria HUD principal da demo
     */
    public createHUD(data: DemoHUDData): void {
        const playerId = data.playerId;

        // Remover HUD existente se houver
        this.removeHUD(playerId);

        // Container principal
        const hudContainer = Scene.createRootEntity();
        hudContainer.name = `DemoHUD_${playerId}`;

        // Painel principal (canto superior esquerdo)
        const mainPanel = this.createMainPanel(hudContainer, data);

        // Painel de objetivo (centro superior)
        const objectivePanel = this.createObjectivePanel(hudContainer, data);

        // Painel de dicas (canto inferior direito)
        const hintsPanel = this.createHintsPanel(hudContainer, data);

        // Minimapa simplificado (canto superior direito)
        const minimapPanel = this.createMinimapPanel(hudContainer);

        // Armazenar referências
        this.hudElements.set(playerId, {
            container: hudContainer,
            mainPanel,
            objectivePanel,
            hintsPanel,
            minimapPanel
        });

        console.log(`[DemoHUD] HUD criado para jogador ${playerId}`);
    }

    /**
     * Cria painel principal com informações básicas
     */
    private createMainPanel(parent: any, data: DemoHUDData): any {
        const panel = Scene.createRootEntity();
        panel.setParent(parent);
        panel.name = "MainPanel";

        // Fundo do painel
        const background = panel.createComponent("engine:mesh", {
            assetId: "hud_panel_background",
            position: { x: -4, y: 3, z: 0 },
            scale: { x: 2, y: 1, z: 0.1 }
        });

        // Título da demo
        const title = panel.createComponent("engine:text", {
            text: "DEMO: ARTON",
            fontSize: 0.4,
            color: UI_CONSTANTS.COLORS.PRIMARY,
            position: { x: -4, y: 3.5, z: 0.1 },
            billboard: true
        });

        // Timer
        const timer = panel.createComponent("engine:text", {
            text: `Tempo: ${data.timer}`,
            fontSize: 0.3,
            color: UI_CONSTANTS.COLORS.SECONDARY,
            position: { x: -4, y: 3.2, z: 0.1 },
            billboard: true
        });

        // Progresso
        const progress = panel.createComponent("engine:text", {
            text: `Progresso: ${data.progress}`,
            fontSize: 0.3,
            color: UI_CONSTANTS.COLORS.SUCCESS,
            position: { x: -4, y: 2.9, z: 0.1 },
            billboard: true
        });

        return {
            background,
            title,
            timer,
            progress
        };
    }

    /**
     * Cria painel de objetivo atual
     */
    private createObjectivePanel(parent: any, data: DemoHUDData): any {
        const panel = Scene.createRootEntity();
        panel.setParent(parent);
        panel.name = "ObjectivePanel";

        // Fundo do objetivo
        const background = panel.createComponent("engine:mesh", {
            assetId: "objective_panel_background",
            position: { x: 0, y: 3, z: 0 },
            scale: { x: 3, y: 0.8, z: 0.1 }
        });

        // Texto do objetivo
        const objectiveText = panel.createComponent("engine:text", {
            text: `🎯 ${data.objective}`,
            fontSize: 0.35,
            color: UI_CONSTANTS.COLORS.PRIMARY,
            position: { x: 0, y: 3, z: 0.1 },
            billboard: true,
            textAlign: "center"
        });

        // Efeito de brilho no objetivo
        const glow = panel.createComponent("engine:persistentParticleEffect", {
            assetId: "objective_glow_effect",
            position: { x: 0, y: 3, z: 0.05 }
        });

        return {
            background,
            objectiveText,
            glow
        };
    }

    /**
     * Cria painel de dicas
     */
    private createHintsPanel(parent: any, data: DemoHUDData): any {
        const panel = Scene.createRootEntity();
        panel.setParent(parent);
        panel.name = "HintsPanel";

        // Fundo das dicas
        const background = panel.createComponent("engine:mesh", {
            assetId: "hints_panel_background",
            position: { x: 3, y: -2, z: 0 },
            scale: { x: 2.5, y: 1.5, z: 0.1 }
        });

        // Título das dicas
        const hintsTitle = panel.createComponent("engine:text", {
            text: "💡 DICAS",
            fontSize: 0.3,
            color: UI_CONSTANTS.COLORS.SECONDARY,
            position: { x: 3, y: -1.5, z: 0.1 },
            billboard: true
        });

        // Lista de dicas padrão
        const defaultHints = [
            "• Toque nos NPCs para conversar",
            "• Explore as zonas marcadas",
            "• Procure pelo brilho do cristal",
            "• Use WASD para se mover"
        ];

        const hints = data.hints || defaultHints;
        const hintTexts: any[] = [];

        hints.forEach((hint, index) => {
            const hintText = panel.createComponent("engine:text", {
                text: hint,
                fontSize: 0.25,
                color: UI_CONSTANTS.COLORS.TEXT,
                position: { x: 3, y: -1.8 - (index * 0.3), z: 0.1 },
                billboard: true
            });
            hintTexts.push(hintText);
        });

        return {
            background,
            hintsTitle,
            hintTexts
        };
    }

    /**
     * Cria minimapa simplificado
     */
    private createMinimapPanel(parent: any): any {
        const panel = Scene.createRootEntity();
        panel.setParent(parent);
        panel.name = "MinimapPanel";

        // Fundo do minimapa
        const background = panel.createComponent("engine:mesh", {
            assetId: "minimap_background",
            position: { x: 4, y: 2, z: 0 },
            scale: { x: 1.5, y: 1.5, z: 0.1 }
        });

        // Marcadores das zonas
        const khalmyrMarker = panel.createComponent("engine:mesh", {
            assetId: "zone_marker_blue",
            position: { x: 4, y: 2, z: 0.1 },
            scale: { x: 0.1, y: 0.1, z: 0.1 }
        });

        const tollonMarker = panel.createComponent("engine:mesh", {
            assetId: "zone_marker_green",
            position: { x: 4.3, y: 2, z: 0.1 },
            scale: { x: 0.1, y: 0.1, z: 0.1 }
        });

        const crystalMarker = panel.createComponent("engine:mesh", {
            assetId: "crystal_marker_gold",
            position: { x: 4.4, y: 2.1, z: 0.1 },
            scale: { x: 0.15, y: 0.15, z: 0.15 }
        });

        // Marcador do jogador (será atualizado dinamicamente)
        const playerMarker = panel.createComponent("engine:mesh", {
            assetId: "player_marker_red",
            position: { x: 4, y: 2, z: 0.12 },
            scale: { x: 0.08, y: 0.08, z: 0.08 }
        });

        // Animar marcador do jogador
        this.animatePlayerMarker(playerMarker);

        return {
            background,
            khalmyrMarker,
            tollonMarker,
            crystalMarker,
            playerMarker
        };
    }

    /**
     * Anima o marcador do jogador no minimapa
     */
    private animatePlayerMarker(marker: any): void {
        let scale = 0.08;
        let growing = true;

        setInterval(() => {
            if (growing) {
                scale += 0.005;
                if (scale >= 0.12) growing = false;
            } else {
                scale -= 0.005;
                if (scale <= 0.08) growing = true;
            }

            marker.setScale(scale, scale, scale);
        }, 100);
    }

    /**
     * Atualiza HUD existente
     */
    public updateHUD(data: DemoHUDData): void {
        const hudElements = this.hudElements.get(data.playerId);
        if (!hudElements) return;

        // Atualizar timer
        if (hudElements.mainPanel.timer) {
            hudElements.mainPanel.timer.text = `Tempo: ${data.timer}`;
        }

        // Atualizar progresso
        if (hudElements.mainPanel.progress) {
            hudElements.mainPanel.progress.text = `Progresso: ${data.progress}`;
        }

        // Atualizar objetivo
        if (hudElements.objectivePanel.objectiveText) {
            hudElements.objectivePanel.objectiveText.text = `🎯 ${data.objective}`;
        }

        // Atualizar dicas se fornecidas
        if (data.hints && hudElements.hintsPanel.hintTexts) {
            data.hints.forEach((hint, index) => {
                if (hudElements.hintsPanel.hintTexts[index]) {
                    hudElements.hintsPanel.hintTexts[index].text = hint;
                }
            });
        }
    }

    /**
     * Mostra notificação temporária
     */
    public showNotification(playerId: string, message: string, duration: number = 3000): void {
        // Criar entidade de notificação
        const notification = Scene.createRootEntity();
        notification.name = `Notification_${Date.now()}`;

        // Posicionar no centro da tela
        notification.setPosition(0, 1, 2);

        // Fundo da notificação
        const background = notification.createComponent("engine:mesh", {
            assetId: "notification_background",
            scale: { x: 4, y: 1, z: 0.1 }
        });

        // Texto da notificação
        const text = notification.createComponent("engine:text", {
            text: message,
            fontSize: 0.4,
            color: UI_CONSTANTS.COLORS.PRIMARY,
            position: { x: 0, y: 0, z: 0.1 },
            billboard: true,
            textAlign: "center",
            maxWidth: 3.5
        });

        // Efeito de entrada (fade in)
        this.animateNotificationIn(notification);

        // Adicionar à fila de notificações
        if (!this.notificationQueue.has(playerId)) {
            this.notificationQueue.set(playerId, []);
        }
        this.notificationQueue.get(playerId)!.push(notification);

        // Remover após duração especificada
        setTimeout(() => {
            this.animateNotificationOut(notification, () => {
                notification.destroy();
                
                // Remover da fila
                const queue = this.notificationQueue.get(playerId);
                if (queue) {
                    const index = queue.indexOf(notification);
                    if (index > -1) {
                        queue.splice(index, 1);
                    }
                }
            });
        }, duration);

        console.log(`[DemoHUD] Notificação mostrada para ${playerId}: ${message}`);
    }

    /**
     * Anima entrada da notificação
     */
    private animateNotificationIn(notification: any): void {
        let scale = 0;
        const targetScale = 1;
        const step = 0.05;

        const animate = () => {
            scale += step;
            if (scale >= targetScale) {
                scale = targetScale;
            } else {
                requestAnimationFrame(animate);
            }
            notification.setScale(scale, scale, scale);
        };

        animate();
    }

    /**
     * Anima saída da notificação
     */
    private animateNotificationOut(notification: any, callback: () => void): void {
        let scale = 1;
        const targetScale = 0;
        const step = 0.05;

        const animate = () => {
            scale -= step;
            if (scale <= targetScale) {
                scale = targetScale;
                callback();
            } else {
                requestAnimationFrame(animate);
                notification.setScale(scale, scale, scale);
            }
        };

        animate();
    }

    /**
     * Mostra popup de diálogo
     */
    public showDialoguePopup(playerId: string, npcName: string, message: string, duration: number = 5000): void {
        const popup = Scene.createRootEntity();
        popup.name = `DialoguePopup_${Date.now()}`;

        // Posicionar acima do jogador
        popup.setPosition(0, 2.5, 1);

        // Fundo do diálogo
        const background = popup.createComponent("engine:mesh", {
            assetId: "dialogue_background",
            scale: { x: 3.5, y: 1.5, z: 0.1 }
        });

        // Nome do NPC
        const nameText = popup.createComponent("engine:text", {
            text: npcName,
            fontSize: 0.3,
            color: UI_CONSTANTS.COLORS.SECONDARY,
            position: { x: 0, y: 0.4, z: 0.1 },
            billboard: true,
            textAlign: "center"
        });

        // Mensagem do NPC
        const messageText = popup.createComponent("engine:text", {
            text: `"${message}"`,
            fontSize: 0.25,
            color: UI_CONSTANTS.COLORS.TEXT,
            position: { x: 0, y: 0, z: 0.1 },
            billboard: true,
            textAlign: "center",
            maxWidth: 3
        });

        // Efeito de brilho
        const glow = popup.createComponent("engine:persistentParticleEffect", {
            assetId: "dialogue_glow_effect",
            position: { x: 0, y: 0, z: 0.05 }
        });

        // Animar entrada
        this.animateNotificationIn(popup);

        // Remover após duração
        setTimeout(() => {
            this.animateNotificationOut(popup, () => {
                popup.destroy();
            });
        }, duration);
    }

    /**
     * Mostra indicador de progresso
     */
    public showProgressIndicator(playerId: string, current: number, total: number): void {
        const hudElements = this.hudElements.get(playerId);
        if (!hudElements) return;

        // Criar barra de progresso temporária
        const progressBar = Scene.createRootEntity();
        progressBar.setParent(hudElements.container);
        progressBar.name = "ProgressBar";

        // Fundo da barra
        const background = progressBar.createComponent("engine:mesh", {
            assetId: "progress_bar_background",
            position: { x: 0, y: -3, z: 0 },
            scale: { x: 3, y: 0.3, z: 0.1 }
        });

        // Preenchimento da barra
        const fill = progressBar.createComponent("engine:mesh", {
            assetId: "progress_bar_fill",
            position: { x: 0, y: -3, z: 0.05 },
            scale: { x: 3 * (current / total), y: 0.25, z: 0.05 }
        });

        // Texto do progresso
        const text = progressBar.createComponent("engine:text", {
            text: `${current}/${total}`,
            fontSize: 0.25,
            color: UI_CONSTANTS.COLORS.TEXT,
            position: { x: 0, y: -3, z: 0.1 },
            billboard: true,
            textAlign: "center"
        });

        // Remover após 3 segundos
        setTimeout(() => {
            progressBar.destroy();
        }, 3000);
    }

    /**
     * Remove HUD de um jogador
     */
    public removeHUD(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        if (hudElements && hudElements.container) {
            hudElements.container.destroy();
            this.hudElements.delete(playerId);
            
            // Limpar fila de notificações
            const queue = this.notificationQueue.get(playerId);
            if (queue) {
                queue.forEach(notification => notification.destroy());
                this.notificationQueue.delete(playerId);
            }
            
            console.log(`[DemoHUD] HUD removido para jogador ${playerId}`);
        }
    }

    /**
     * Limpa todas as notificações de um jogador
     */
    public clearNotifications(playerId: string): void {
        const queue = this.notificationQueue.get(playerId);
        if (queue) {
            queue.forEach(notification => notification.destroy());
            this.notificationQueue.set(playerId, []);
        }
    }

    /**
     * Obtém elementos do HUD de um jogador
     */
    public getHUDElements(playerId: string): any {
        return this.hudElements.get(playerId);
    }
}

// Instância global do sistema de HUD da demo
export const demoHUD = new DemoHUD();
