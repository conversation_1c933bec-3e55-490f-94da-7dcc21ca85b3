/**
 * Storm RPG - Game Manager
 * Gerenciador principal que coordena todos os sistemas do jogo
 */

import { PlayerManager } from './PlayerManager';
import { CombatSystem } from './CombatSystem';
import { HUDManager } from '../ui/HUDManager';
import { ItemPickup } from '../items/ItemPickup';
import { spellManager } from '../spells/BaseSpell';
import { Fireball } from '../spells/Fireball';
import { Heal } from '../spells/Heal';
import { StormBolt } from '../spells/StormBolt';
import { stormEvents } from '../utils/EventSystem';
import { GAME_CONFIG, GameState, ItemType } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Players: any;

export class GameManager {
    private playerManager: PlayerManager;
    private combatSystem: CombatSystem;
    private hudManager: HUDManager;
    private itemPickup: ItemPickup;
    
    private gameState: GameState = GameState.WAITING_FOR_PLAYERS;
    private gameStartTime: number = 0;
    private gameEndTime: number = 0;
    
    private updateInterval: any;
    private spawnInterval: any;

    constructor() {
        console.log("[GameManager] Inicializando Storm RPG...");
        
        this.initializeSystems();
        this.setupEventListeners();
        this.registerSpells();
        this.startGameLoop();
        
        console.log("[GameManager] Storm RPG inicializado com sucesso!");
    }

    /**
     * Inicializa todos os sistemas do jogo
     */
    private initializeSystems(): void {
        // Inicializar sistemas na ordem correta
        this.playerManager = new PlayerManager();
        this.combatSystem = new CombatSystem(this.playerManager);
        this.hudManager = new HUDManager(this.playerManager);
        this.itemPickup = new ItemPickup(this.playerManager);
        
        console.log("[GameManager] Sistemas inicializados");
    }

    /**
     * Configura listeners de eventos
     */
    private setupEventListeners(): void {
        // Eventos de jogador
        stormEvents.onPlayerJoined((playerId, playerName) => {
            this.onPlayerJoined(playerId, playerName);
        });

        stormEvents.eventSystem.on('player_left', (data) => {
            this.onPlayerLeft(data.playerId);
        });

        // Eventos de combate
        stormEvents.onDamageDealt((attacker, target, damage) => {
            this.onDamageDealt(attacker, target, damage);
        });

        // Eventos de magias
        stormEvents.eventSystem.on('spell_button_clicked', (data) => {
            this.onSpellButtonClicked(data.playerId, data.spellName);
        });

        // Eventos de itens
        stormEvents.onItemPickedUp((playerId, itemType, value) => {
            this.onItemPickedUp(playerId, itemType, value);
        });

        // Eventos especiais da Storm
        stormEvents.onStormSurge((intensity, duration) => {
            this.onStormSurge(intensity, duration);
        });

        console.log("[GameManager] Event listeners configurados");
    }

    /**
     * Registra todas as magias disponíveis
     */
    private registerSpells(): void {
        spellManager.registerSpell('fireball', new Fireball());
        spellManager.registerSpell('heal', new Heal());
        spellManager.registerSpell('storm_bolt', new StormBolt());
        
        console.log("[GameManager] Magias registradas");
    }

    /**
     * Inicia o loop principal do jogo
     */
    private startGameLoop(): void {
        // Update a cada 100ms
        this.updateInterval = setInterval(() => {
            this.update();
        }, 100);

        // Spawn de itens a cada 30 segundos
        this.spawnInterval = setInterval(() => {
            this.spawnRandomItems();
        }, 30000);

        console.log("[GameManager] Game loop iniciado");
    }

    /**
     * Update principal do jogo
     */
    private update(): void {
        // Verificar estado do jogo
        this.checkGameState();
        
        // Atualizar sistemas
        this.updateGameSystems();
        
        // Verificar condições de vitória/derrota
        this.checkWinConditions();
    }

    /**
     * Verifica e atualiza o estado do jogo
     */
    private checkGameState(): void {
        const playerCount = this.playerManager.getPlayerCount();
        
        switch (this.gameState) {
            case GameState.WAITING_FOR_PLAYERS:
                if (playerCount >= 1) {
                    this.startGame();
                }
                break;
                
            case GameState.IN_PROGRESS:
                // Verificar se o tempo acabou
                const elapsed = Date.now() - this.gameStartTime;
                if (elapsed >= GAME_CONFIG.GAME_DURATION) {
                    this.endGame();
                }
                
                // Verificar se não há jogadores
                if (playerCount === 0) {
                    this.gameState = GameState.WAITING_FOR_PLAYERS;
                }
                break;
        }
    }

    /**
     * Atualiza sistemas do jogo
     */
    private updateGameSystems(): void {
        // Atualizar HUD de todos os jogadores
        const players = this.playerManager.getAllPlayers();
        players.forEach(player => {
            this.hudManager.updateAllHUDElements(player.id);
        });
    }

    /**
     * Verifica condições de vitória
     */
    private checkWinConditions(): void {
        if (this.gameState !== GameState.IN_PROGRESS) return;

        // Exemplo: Primeiro jogador a chegar no nível 10 vence
        const players = this.playerManager.getAllPlayers();
        const winner = players.find(player => player.stats.level >= 10);
        
        if (winner) {
            this.declareWinner(winner.id);
        }
    }

    /**
     * Inicia o jogo
     */
    private startGame(): void {
        this.gameState = GameState.STARTING;
        this.gameStartTime = Date.now();
        
        console.log("[GameManager] Iniciando jogo...");
        
        // Notificar todos os jogadores
        const players = this.playerManager.getAllPlayers();
        players.forEach(player => {
            this.hudManager.showNotification(player.id, "Jogo iniciado!", 3000);
        });
        
        // Spawn inicial de itens
        this.spawnInitialItems();
        
        // Spawn inicial de inimigos
        this.spawnInitialEnemies();
        
        this.gameState = GameState.IN_PROGRESS;
        
        // Emitir evento
        stormEvents.eventSystem.emit('game_started', {
            startTime: this.gameStartTime,
            playerCount: players.length
        });
    }

    /**
     * Termina o jogo
     */
    private endGame(): void {
        this.gameState = GameState.ENDING;
        this.gameEndTime = Date.now();
        
        console.log("[GameManager] Jogo terminado");
        
        // Mostrar placar final
        this.showFinalScoreboard();
        
        // Limpar mundo
        setTimeout(() => {
            this.resetGame();
        }, 10000); // 10 segundos para ver o placar
    }

    /**
     * Declara vencedor
     */
    private declareWinner(winnerId: string): void {
        const winner = this.playerManager.getPlayerData(winnerId);
        if (!winner) return;
        
        console.log(`[GameManager] ${winner.name} venceu o jogo!`);
        
        // Notificar todos os jogadores
        const players = this.playerManager.getAllPlayers();
        players.forEach(player => {
            const message = player.id === winnerId ? 
                "Você venceu!" : 
                `${winner.name} venceu!`;
            this.hudManager.showNotification(player.id, message, 5000);
        });
        
        this.endGame();
    }

    /**
     * Mostra placar final
     */
    private showFinalScoreboard(): void {
        const leaderboard = this.playerManager.getLeaderboard();
        
        console.log("=== PLACAR FINAL ===");
        leaderboard.forEach((player, index) => {
            console.log(`${index + 1}. ${player.name} - ${player.score} pontos (Nível ${player.level})`);
        });
    }

    /**
     * Reseta o jogo
     */
    private resetGame(): void {
        console.log("[GameManager] Resetando jogo...");
        
        // Limpar itens
        this.itemPickup.clearAllItems();
        
        // Resetar estado
        this.gameState = GameState.WAITING_FOR_PLAYERS;
        this.gameStartTime = 0;
        this.gameEndTime = 0;
        
        // Emitir evento
        stormEvents.eventSystem.emit('game_reset', {});
    }

    /**
     * Spawn inicial de itens
     */
    private spawnInitialItems(): void {
        const spawnPoints = [
            { x: 10, y: 1, z: 10 },
            { x: -10, y: 1, z: 10 },
            { x: 10, y: 1, z: -10 },
            { x: -10, y: 1, z: -10 },
            { x: 0, y: 1, z: 15 },
            { x: 0, y: 1, z: -15 }
        ];
        
        spawnPoints.forEach(point => {
            // Spawn aleatório de diferentes tipos de itens
            const itemTypes = [ItemType.GEM_SMALL, ItemType.POTION_HEALTH];
            const randomType = itemTypes[Math.floor(Math.random() * itemTypes.length)];
            
            this.itemPickup.createItem(randomType, point);
        });
        
        console.log("[GameManager] Itens iniciais criados");
    }

    /**
     * Spawn aleatório de itens
     */
    private spawnRandomItems(): void {
        if (this.gameState !== GameState.IN_PROGRESS) return;
        
        // Spawn 2-4 itens aleatórios
        const itemCount = 2 + Math.floor(Math.random() * 3);
        
        for (let i = 0; i < itemCount; i++) {
            const randomPosition = {
                x: (Math.random() - 0.5) * 40, // -20 a 20
                y: 1,
                z: (Math.random() - 0.5) * 40
            };
            
            // Tipo aleatório baseado em raridade
            let itemType: ItemType;
            const rarity = Math.random();
            
            if (rarity < 0.6) {
                itemType = ItemType.GEM_SMALL;
            } else if (rarity < 0.85) {
                itemType = ItemType.POTION_HEALTH;
            } else if (rarity < 0.95) {
                itemType = ItemType.GEM_LARGE;
            } else {
                itemType = ItemType.STORM_CRYSTAL;
            }
            
            this.itemPickup.createItem(itemType, randomPosition);
        }
        
        console.log(`[GameManager] ${itemCount} itens aleatórios criados`);
    }

    /**
     * Spawn inicial de inimigos
     */
    private spawnInitialEnemies(): void {
        // TODO: Implementar sistema de inimigos
        console.log("[GameManager] Sistema de inimigos será implementado");
    }

    // Event Handlers

    private onPlayerJoined(playerId: string, playerName: string): void {
        console.log(`[GameManager] ${playerName} entrou no jogo`);
        
        // Se o jogo não começou e temos jogadores suficientes, iniciar
        if (this.gameState === GameState.WAITING_FOR_PLAYERS && 
            this.playerManager.canStartGame()) {
            setTimeout(() => {
                if (this.gameState === GameState.WAITING_FOR_PLAYERS) {
                    this.startGame();
                }
            }, 3000); // 3 segundos de delay
        }
    }

    private onPlayerLeft(playerId: string): void {
        console.log(`[GameManager] Jogador ${playerId} saiu do jogo`);
        
        // Remover HUD do jogador
        this.hudManager.removePlayerHUD(playerId);
    }

    private onDamageDealt(attacker: string, target: string, damage: number): void {
        // Log de combate
        console.log(`[GameManager] ${attacker} causou ${damage} de dano em ${target}`);
    }

    private onSpellButtonClicked(playerId: string, spellName: string): void {
        console.log(`[GameManager] ${playerId} tentou usar ${spellName}`);
        
        const playerStats = this.playerManager.getPlayerStats(playerId);
        if (!playerStats) return;
        
        // Tentar conjurar a magia
        const spellId = spellName.toLowerCase().replace(' ', '_');
        
        spellManager.castSpell(playerId, spellId, playerStats)
            .then(result => {
                if (result) {
                    this.hudManager.showNotification(playerId, `${spellName} conjurada!`, 2000);
                } else {
                    this.hudManager.showNotification(playerId, "Não foi possível conjurar", 2000);
                }
            });
    }

    private onItemPickedUp(playerId: string, itemType: string, value: number): void {
        console.log(`[GameManager] ${playerId} coletou ${itemType} (valor: ${value})`);
        
        // Atualizar HUD
        this.hudManager.updateScoreDisplay(playerId);
    }

    private onStormSurge(intensity: number, duration: number): void {
        console.log(`[GameManager] Storm Surge! Intensidade: ${intensity}, Duração: ${duration}ms`);
        
        // Notificar todos os jogadores
        const players = this.playerManager.getAllPlayers();
        players.forEach(player => {
            this.hudManager.showNotification(
                player.id, 
                "TEMPESTADE MÁGICA!", 
                duration
            );
        });
        
        // TODO: Implementar efeitos da tempestade
    }

    // Métodos públicos para interação externa

    /**
     * Obtém estado atual do jogo
     */
    getGameState(): GameState {
        return this.gameState;
    }

    /**
     * Obtém tempo restante do jogo
     */
    getRemainingTime(): number {
        if (this.gameState !== GameState.IN_PROGRESS) return 0;
        
        const elapsed = Date.now() - this.gameStartTime;
        const remaining = GAME_CONFIG.GAME_DURATION - elapsed;
        return Math.max(0, remaining);
    }

    /**
     * Força início do jogo (para testes)
     */
    forceStartGame(): void {
        if (this.gameState === GameState.WAITING_FOR_PLAYERS) {
            this.startGame();
        }
    }

    /**
     * Para o jogo
     */
    stopGame(): void {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
        
        if (this.spawnInterval) {
            clearInterval(this.spawnInterval);
        }
        
        console.log("[GameManager] Jogo parado");
    }
}

// Instância global do GameManager
export let gameManager: GameManager;

// Função para inicializar o jogo (chamada pelo Horizon Worlds)
export function initializeStormRPG(): void {
    gameManager = new GameManager();
}

// Auto-inicializar se estivermos no ambiente correto
if (typeof Scene !== 'undefined') {
    initializeStormRPG();
}
