// Storm RPG - Dice System Library
// Complete d20 system implementation for Tormenta 20 / Storm RPG

#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/DataTable.h"
#include "GameplayTagContainer.h"
#include "StormDiceLibrary.generated.h"

USTRUCT(BlueprintType)
struct STORMRPG_API FStormDiceResult
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    int32 Roll = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    int32 Modifier = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    int32 Total = 0;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    bool bIsCriticalHit = false;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    bool bIsCriticalFail = false;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    bool bHasAdvantage = false;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    bool bHasDisadvantage = false;

    UPROPERTY(BlueprintReadOnly, Category = "Dice")
    FString Expression = "";

    FStormDiceResult()
    {
        Roll = 0;
        Modifier = 0;
        Total = 0;
        bIsCriticalHit = false;
        bIsCriticalFail = false;
        bHasAdvantage = false;
        bHasDisadvantage = false;
    }

    FStormDiceResult(int32 InRoll, int32 InModifier)
    {
        Roll = InRoll;
        Modifier = InModifier;
        Total = Roll + Modifier;
        bIsCriticalHit = (Roll == 20);
        bIsCriticalFail = (Roll == 1);
        bHasAdvantage = false;
        bHasDisadvantage = false;
    }
};

USTRUCT(BlueprintType)
struct STORMRPG_API FStormDiceExpression
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    int32 NumDice = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    int32 DiceSides = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    int32 Modifier = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    bool bExploding = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    bool bAdvantage = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice")
    bool bDisadvantage = false;

    FStormDiceExpression()
    {
        NumDice = 1;
        DiceSides = 20;
        Modifier = 0;
        bExploding = false;
        bAdvantage = false;
        bDisadvantage = false;
    }

    FStormDiceExpression(int32 InNumDice, int32 InSides, int32 InModifier = 0)
    {
        NumDice = InNumDice;
        DiceSides = InSides;
        Modifier = InModifier;
        bExploding = false;
        bAdvantage = false;
        bDisadvantage = false;
    }
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDiceRolled, FStormDiceResult, Result);

/**
 * Storm RPG Dice Library
 * Comprehensive d20 system implementation with multiple visualization options
 */
UCLASS()
class STORMRPG_API UStormDiceLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    // ========================================
    // BASIC DICE FUNCTIONS (Pure Logic)
    // ========================================

    /** Roll a single die with specified number of sides */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Basic")
    static int32 RollDie(int32 Sides = 20);

    /** Roll multiple dice and return total */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Basic")
    static int32 RollMultipleDice(int32 NumDice, int32 Sides);

    /** Roll dice with modifier */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Basic")
    static int32 RollWithModifier(int32 NumDice, int32 Sides, int32 Modifier);

    // ========================================
    // D20 SYSTEM FUNCTIONS
    // ========================================

    /** Roll d20 with full result information */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|D20")
    static FStormDiceResult RollD20(int32 Modifier = 0, bool bAdvantage = false, bool bDisadvantage = false);

    /** Roll attack vs AC */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|D20")
    static FStormDiceResult RollAttack(int32 AttackBonus, int32 TargetAC, bool bAdvantage = false, bool bDisadvantage = false);

    /** Roll saving throw */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|D20")
    static FStormDiceResult RollSavingThrow(int32 SaveBonus, int32 DifficultyClass, bool bAdvantage = false, bool bDisadvantage = false);

    /** Roll skill check */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|D20")
    static FStormDiceResult RollSkillCheck(int32 SkillBonus, int32 DifficultyClass, bool bAdvantage = false, bool bDisadvantage = false);

    // ========================================
    // EXPRESSION PARSING
    // ========================================

    /** Parse and roll dice expression (e.g., "2d6+3", "1d20+5") */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Expression")
    static FStormDiceResult RollExpression(const FString& Expression);

    /** Parse dice expression into components */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Expression")
    static FStormDiceExpression ParseDiceExpression(const FString& Expression);

    /** Roll using dice expression struct */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Expression")
    static FStormDiceResult RollDiceExpression(const FStormDiceExpression& Expression);

    // ========================================
    // SPECIAL MECHANICS
    // ========================================

    /** Roll exploding dice (reroll on max value) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Special")
    static int32 RollExploding(int32 Sides, int32 MaxExplosions = 10);

    /** Roll with advantage (roll twice, take higher) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Special")
    static FStormDiceResult RollWithAdvantage(int32 Modifier = 0);

    /** Roll with disadvantage (roll twice, take lower) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Special")
    static FStormDiceResult RollWithDisadvantage(int32 Modifier = 0);

    /** Roll initiative */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Special")
    static FStormDiceResult RollInitiative(int32 DexterityModifier, int32 LevelBonus = 0);

    // ========================================
    // UTILITY FUNCTIONS
    // ========================================

    /** Check if roll succeeds against difficulty class */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Utility")
    static bool CheckSuccess(const FStormDiceResult& Result, int32 DifficultyClass);

    /** Get critical hit multiplier based on roll */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Utility")
    static float GetCriticalMultiplier(const FStormDiceResult& Result, float BaseCritMultiplier = 2.0f);

    /** Roll percentile (d100) */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Utility")
    static int32 RollPercentile();

    /** Check percentage chance */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice|Utility")
    static bool CheckPercentage(float Percentage);

    // ========================================
    // STORM-SPECIFIC FUNCTIONS
    // ========================================

    /** Roll Storm surge (chaos magic effect) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Storm")
    static FStormDiceResult RollStormSurge(int32 CasterLevel);

    /** Roll Tempestado awakening check */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Storm")
    static FStormDiceResult RollTempestadoAwakening(int32 CharacterLevel, int32 StormExposure);

    /** Roll chaos effect table */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Storm")
    static int32 RollChaosEffect();

    // ========================================
    // DEBUGGING & CONSOLE
    // ========================================

    /** Console command for testing dice rolls */
    UFUNCTION(Exec, Category = "Storm|Dice|Debug")
    static void StormRoll(const FString& Expression);

    /** Log dice result to console */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Debug")
    static void LogDiceResult(const FStormDiceResult& Result, const FString& Context = "");

private:
    /** Internal function to roll single d20 */
    static int32 RollSingleD20();

    /** Internal function to parse regex patterns */
    static bool ParseDicePattern(const FString& Expression, int32& OutNumDice, int32& OutSides, int32& OutModifier);

    /** Internal function to apply advantage/disadvantage */
    static FStormDiceResult ApplyAdvantageDisadvantage(int32 Modifier, bool bAdvantage, bool bDisadvantage);
};
