// Storm RPG - 2D UI Dice Widget
// Beautiful animated dice interface for Storm RPG

#pragma once

#include "CoreMinimal.h"
#include "Blueprint/UserWidget.h"
#include "Components/Image.h"
#include "Components/TextBlock.h"
#include "Components/Border.h"
#include "Components/HorizontalBox.h"
#include "Animation/WidgetAnimation.h"
#include "Dice/StormDiceLibrary.h"
#include "StormDiceWidget.generated.h"

DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FOnDiceAnimationComplete, FStormDiceResult, Result);

/**
 * 2D UI Dice Widget for Storm RPG
 * Provides beautiful animated dice rolling interface
 */
UCLASS()
class STORMRPG_API UStormDiceWidget : public UUserWidget
{
    GENERATED_BODY()

public:
    UStormDiceWidget(const FObjectInitializer& ObjectInitializer);

protected:
    virtual void NativeConstruct() override;
    virtual void NativeTick(const FGeometry& MyGeometry, float InDeltaTime) override;

public:
    // ========================================
    // WIDGET COMPONENTS
    // ========================================

    /** Main container for dice display */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UHorizontalBox* DiceContainer;

    /** Background border */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UBorder* BackgroundBorder;

    /** Total result text */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* TotalText;

    /** Expression text (shows what was rolled) */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* ExpressionText;

    /** Modifier text */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* ModifierText;

    /** Critical hit indicator */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* CriticalText;

    /** Advantage/Disadvantage indicator */
    UPROPERTY(BlueprintReadOnly, meta = (BindWidget))
    class UTextBlock* AdvantageText;

    // ========================================
    // ANIMATIONS
    // ========================================

    /** Roll animation */
    UPROPERTY(BlueprintReadOnly, Transient, meta = (BindWidgetAnim))
    class UWidgetAnimation* RollAnimation;

    /** Critical hit animation */
    UPROPERTY(BlueprintReadOnly, Transient, meta = (BindWidgetAnim))
    class UWidgetAnimation* CriticalAnimation;

    /** Fumble animation */
    UPROPERTY(BlueprintReadOnly, Transient, meta = (BindWidgetAnim))
    class UWidgetAnimation* FumbleAnimation;

    /** Advantage glow animation */
    UPROPERTY(BlueprintReadOnly, Transient, meta = (BindWidgetAnim))
    class UWidgetAnimation* AdvantageAnimation;

    // ========================================
    // CONFIGURATION
    // ========================================

    /** Dice face textures (1-6 for d6, 1-20 for d20, etc.) */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Appearance")
    TArray<class UTexture2D*> DiceFaceTextures;

    /** Colors for different dice types */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dice Appearance")
    TMap<int32, FLinearColor> DiceColors;

    /** Animation duration for rolling */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    float RollAnimationDuration = 1.5f;

    /** Number of intermediate frames during roll animation */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Animation")
    int32 RollAnimationFrames = 20;

    /** Sound effects */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* RollSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* CriticalSound;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Audio")
    class USoundBase* FumbleSound;

    // ========================================
    // EVENTS
    // ========================================

    /** Called when dice animation completes */
    UPROPERTY(BlueprintAssignable, Category = "Events")
    FOnDiceAnimationComplete OnDiceAnimationComplete;

    // ========================================
    // PUBLIC FUNCTIONS
    // ========================================

    /** Roll dice with expression and animate */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void RollDiceAnimated(const FString& Expression);

    /** Roll dice with struct and animate */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void RollDiceExpressionAnimated(const FStormDiceExpression& Expression);

    /** Roll d20 with modifiers and animate */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void RollD20Animated(int32 Modifier = 0, bool bAdvantage = false, bool bDisadvantage = false);

    /** Set dice result without animation (instant) */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void SetDiceResult(const FStormDiceResult& Result);

    /** Clear dice display */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void ClearDice();

    /** Show/hide the widget with animation */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice")
    void ShowDiceWidget(bool bShow = true);

protected:
    // ========================================
    // INTERNAL FUNCTIONS
    // ========================================

    /** Create individual die widget */
    UFUNCTION(BlueprintImplementableEvent, Category = "Storm|Dice")
    class UWidget* CreateDieWidget(int32 DieValue, int32 DieSides);

    /** Update die widget with new value */
    UFUNCTION(BlueprintImplementableEvent, Category = "Storm|Dice")
    void UpdateDieWidget(class UWidget* DieWidget, int32 NewValue);

    /** Start roll animation sequence */
    void StartRollAnimation(const FStormDiceResult& FinalResult);

    /** Update animation frame */
    void UpdateAnimationFrame();

    /** Complete animation and show final result */
    void CompleteAnimation();

    /** Apply visual effects based on result */
    void ApplyResultEffects(const FStormDiceResult& Result);

    /** Update text displays */
    void UpdateTextDisplays(const FStormDiceResult& Result);

    /** Play appropriate sound effect */
    void PlaySoundEffect(const FStormDiceResult& Result);

    /** Get color for dice type */
    FLinearColor GetDiceColor(int32 DieSides) const;

    /** Get texture for die face */
    UTexture2D* GetDieFaceTexture(int32 DieValue, int32 DieSides) const;

private:
    // ========================================
    // ANIMATION STATE
    // ========================================

    /** Current animation state */
    bool bIsAnimating = false;

    /** Final result to show when animation completes */
    FStormDiceResult PendingResult;

    /** Current animation time */
    float CurrentAnimationTime = 0.0f;

    /** Animation timer handle */
    FTimerHandle AnimationTimerHandle;

    /** Current dice widgets being animated */
    TArray<class UWidget*> CurrentDiceWidgets;

    /** Random values for animation frames */
    TArray<TArray<int32>> AnimationFrameValues;

    /** Current animation frame */
    int32 CurrentAnimationFrame = 0;

public:
    // ========================================
    // BLUEPRINT EVENTS
    // ========================================

    /** Blueprint event called when roll starts */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnRollStarted(const FString& Expression);

    /** Blueprint event called when roll completes */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnRollCompleted(const FStormDiceResult& Result);

    /** Blueprint event called for critical hit */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnCriticalHit(const FStormDiceResult& Result);

    /** Blueprint event called for critical failure */
    UFUNCTION(BlueprintImplementableEvent, Category = "Events")
    void OnCriticalFailure(const FStormDiceResult& Result);

    // ========================================
    // UTILITY FUNCTIONS
    // ========================================

    /** Get formatted result string */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    FString GetFormattedResult(const FStormDiceResult& Result) const;

    /** Get result color based on success/failure */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    FLinearColor GetResultColor(const FStormDiceResult& Result, int32 DifficultyClass = 15) const;

    /** Check if currently animating */
    UFUNCTION(BlueprintCallable, BlueprintPure, Category = "Storm|Dice")
    bool IsAnimating() const { return bIsAnimating; }

    // ========================================
    // PRESET ROLL FUNCTIONS
    // ========================================

    /** Quick roll for common expressions */
    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Presets")
    void RollAttack(int32 AttackBonus, bool bAdvantage = false);

    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Presets")
    void RollDamage(const FString& DamageExpression);

    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Presets")
    void RollSave(int32 SaveBonus, bool bAdvantage = false);

    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Presets")
    void RollSkill(int32 SkillBonus, bool bAdvantage = false);

    UFUNCTION(BlueprintCallable, Category = "Storm|Dice|Presets")
    void RollInitiative(int32 DexModifier, int32 LevelBonus = 0);
};
