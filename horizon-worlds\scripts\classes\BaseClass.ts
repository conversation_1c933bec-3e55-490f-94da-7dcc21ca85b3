/**
 * Storm RPG - Base Class System
 * Sistema base para classes de personagem
 */

import { PlayerStats } from '../core/AttributeSystem';
import { PlayerClass } from '../utils/Constants';

export interface ClassAbility {
    name: string;
    description: string;
    level: number;
    cooldown: number;
    manaCost: number;
    type: 'active' | 'passive' | 'toggle';
    execute: (caster: PlayerStats, target?: any) => void;
}

export abstract class BaseCharacterClass {
    public name: string;
    public description: string;
    public abilities: Map<number, ClassAbility[]> = new Map();

    constructor(name: string, description: string) {
        this.name = name;
        this.description = description;
        this.initializeAbilities();
    }

    /**
     * Inicializa as habilidades da classe
     */
    protected abstract initializeAbilities(): void;

    /**
     * Obtém habilidades disponíveis para um nível
     */
    getAbilitiesForLevel(level: number): ClassAbility[] {
        const abilities: ClassAbility[] = [];
        
        for (let i = 1; i <= level; i++) {
            const levelAbilities = this.abilities.get(i);
            if (levelAbilities) {
                abilities.push(...levelAbilities);
            }
        }
        
        return abilities;
    }

    /**
     * Adiciona uma habilidade a um nível específico
     */
    protected addAbility(level: number, ability: ClassAbility): void {
        if (!this.abilities.has(level)) {
            this.abilities.set(level, []);
        }
        this.abilities.get(level)!.push(ability);
    }

    /**
     * Executa uma habilidade
     */
    executeAbility(abilityName: string, caster: PlayerStats, target?: any): boolean {
        const allAbilities = this.getAbilitiesForLevel(caster.level);
        const ability = allAbilities.find(a => a.name === abilityName);
        
        if (!ability) {
            console.error(`Habilidade ${abilityName} não encontrada`);
            return false;
        }

        // Verificar se tem mana suficiente
        if (ability.manaCost > 0 && !caster.spendMana(ability.manaCost)) {
            console.log(`Mana insuficiente para ${abilityName}`);
            return false;
        }

        try {
            ability.execute(caster, target);
            console.log(`${caster.classConfig.name} usou ${abilityName}`);
            return true;
        } catch (error) {
            console.error(`Erro ao executar habilidade ${abilityName}:`, error);
            return false;
        }
    }
}

export class WarriorClass extends BaseCharacterClass {
    constructor() {
        super("Guerreiro", "Mestre do combate corpo a corpo com alta defesa e ataques poderosos");
    }

    protected initializeAbilities(): void {
        // Nível 1: Golpe Poderoso
        this.addAbility(1, {
            name: "Golpe Poderoso",
            description: "Ataque que causa dano extra",
            level: 1,
            cooldown: 5000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                // Implementar lógica do golpe poderoso
                console.log("Executando Golpe Poderoso!");
            }
        });

        // Nível 2: Defesa
        this.addAbility(2, {
            name: "Postura Defensiva",
            description: "Aumenta a CA temporariamente",
            level: 2,
            cooldown: 10000,
            manaCost: 0,
            type: 'toggle',
            execute: (caster: PlayerStats) => {
                // Implementar bônus de CA
                console.log("Ativando Postura Defensiva!");
            }
        });

        // Nível 3: Provocar
        this.addAbility(3, {
            name: "Provocar",
            description: "Força inimigos a atacar o guerreiro",
            level: 3,
            cooldown: 8000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Provocando inimigos!");
            }
        });

        // Nível 5: Ataque Giratório
        this.addAbility(5, {
            name: "Ataque Giratório",
            description: "Ataca todos os inimigos ao redor",
            level: 5,
            cooldown: 15000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats) => {
                console.log("Executando Ataque Giratório!");
            }
        });
    }
}

export class MageClass extends BaseCharacterClass {
    constructor() {
        super("Mago", "Conjurador de magias poderosas com controle do campo de batalha");
    }

    protected initializeAbilities(): void {
        // Nível 1: Míssil Mágico
        this.addAbility(1, {
            name: "Míssil Mágico",
            description: "Projétil mágico que sempre acerta",
            level: 1,
            cooldown: 3000,
            manaCost: 2,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Lançando Míssil Mágico!");
            }
        });

        // Nível 2: Escudo Mágico
        this.addAbility(2, {
            name: "Escudo Mágico",
            description: "Cria uma barreira mágica protetora",
            level: 2,
            cooldown: 12000,
            manaCost: 4,
            type: 'active',
            execute: (caster: PlayerStats) => {
                console.log("Conjurando Escudo Mágico!");
            }
        });

        // Nível 3: Bola de Fogo
        this.addAbility(3, {
            name: "Bola de Fogo",
            description: "Explosão de fogo em área",
            level: 3,
            cooldown: 8000,
            manaCost: 6,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Lançando Bola de Fogo!");
            }
        });

        // Nível 5: Raio da Tempestade
        this.addAbility(5, {
            name: "Raio da Tempestade",
            description: "Raio elétrico devastador",
            level: 5,
            cooldown: 10000,
            manaCost: 8,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Invocando Raio da Tempestade!");
            }
        });
    }
}

export class RogueClass extends BaseCharacterClass {
    constructor() {
        super("Ladino", "Especialista em ataques furtivos e mobilidade");
    }

    protected initializeAbilities(): void {
        // Nível 1: Ataque Furtivo
        this.addAbility(1, {
            name: "Ataque Furtivo",
            description: "Ataque que causa dano extra quando não detectado",
            level: 1,
            cooldown: 0,
            manaCost: 0,
            type: 'passive',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Ataque Furtivo ativado!");
            }
        });

        // Nível 2: Esquiva
        this.addAbility(2, {
            name: "Esquiva Ágil",
            description: "Aumenta chance de esquivar ataques",
            level: 2,
            cooldown: 8000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats) => {
                console.log("Ativando Esquiva Ágil!");
            }
        });

        // Nível 3: Invisibilidade
        this.addAbility(3, {
            name: "Invisibilidade",
            description: "Torna-se invisível por um curto período",
            level: 3,
            cooldown: 20000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats) => {
                console.log("Ativando Invisibilidade!");
            }
        });

        // Nível 5: Ataque Duplo
        this.addAbility(5, {
            name: "Ataque Duplo",
            description: "Realiza dois ataques rápidos",
            level: 5,
            cooldown: 12000,
            manaCost: 0,
            type: 'active',
            execute: (caster: PlayerStats, target?: any) => {
                console.log("Executando Ataque Duplo!");
            }
        });
    }
}

// Factory para criar classes
export class ClassFactory {
    private static classes: Map<PlayerClass, () => BaseCharacterClass> = new Map([
        [PlayerClass.WARRIOR, () => new WarriorClass()],
        [PlayerClass.MAGE, () => new MageClass()],
        [PlayerClass.ROGUE, () => new RogueClass()]
    ]);

    static createClass(playerClass: PlayerClass): BaseCharacterClass {
        const classConstructor = this.classes.get(playerClass);
        if (!classConstructor) {
            throw new Error(`Classe ${playerClass} não encontrada`);
        }
        return classConstructor();
    }

    static getAvailableClasses(): PlayerClass[] {
        return Array.from(this.classes.keys());
    }
}

// Sistema de cooldowns para habilidades
export class AbilityCooldownManager {
    private cooldowns: Map<string, number> = new Map();

    /**
     * Verifica se uma habilidade está em cooldown
     */
    isOnCooldown(playerId: string, abilityName: string): boolean {
        const key = `${playerId}_${abilityName}`;
        const cooldownEnd = this.cooldowns.get(key);
        
        if (!cooldownEnd) return false;
        
        const now = Date.now();
        if (now >= cooldownEnd) {
            this.cooldowns.delete(key);
            return false;
        }
        
        return true;
    }

    /**
     * Inicia cooldown de uma habilidade
     */
    startCooldown(playerId: string, abilityName: string, duration: number): void {
        const key = `${playerId}_${abilityName}`;
        const cooldownEnd = Date.now() + duration;
        this.cooldowns.set(key, cooldownEnd);
    }

    /**
     * Obtém tempo restante de cooldown
     */
    getRemainingCooldown(playerId: string, abilityName: string): number {
        const key = `${playerId}_${abilityName}`;
        const cooldownEnd = this.cooldowns.get(key);
        
        if (!cooldownEnd) return 0;
        
        const remaining = cooldownEnd - Date.now();
        return Math.max(0, remaining);
    }

    /**
     * Remove todos os cooldowns de um jogador
     */
    clearPlayerCooldowns(playerId: string): void {
        const keysToDelete: string[] = [];
        
        for (const key of this.cooldowns.keys()) {
            if (key.startsWith(`${playerId}_`)) {
                keysToDelete.push(key);
            }
        }
        
        keysToDelete.forEach(key => this.cooldowns.delete(key));
    }
}
