/**
 * Storm RPG - Item Pickup System
 * Sistema de coleta de itens com efeitos visuais e progressão
 */

import { PlayerManager } from '../core/PlayerManager';
import { stormEvents } from '../utils/EventSystem';
import { ITEM_CONFIGS, ASSET_IDS, ItemType } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Entity: any;
declare const Players: any;

export interface ItemData {
    type: ItemType;
    name: string;
    value: number;
    rarity: 'common' | 'uncommon' | 'rare' | 'epic' | 'legendary';
    effect: string;
    description?: string;
}

export class ItemPickup {
    private playerManager: PlayerManager;
    private activeItems: Map<string, ItemData> = new Map();

    constructor(playerManager: PlayerManager) {
        this.playerManager = playerManager;
        this.setupItemEvents();
    }

    /**
     * Configura eventos de itens
     */
    private setupItemEvents(): void {
        // Escutar eventos de coleta
        stormEvents.onItemPickedUp((playerId, itemType, value) => {
            this.processItemPickup(playerId, itemType, value);
        });
    }

    /**
     * Cria um item no mundo
     */
    createItem(
        itemType: ItemType,
        position: { x: number; y: number; z: number },
        customData?: Partial<ItemData>
    ): string {
        const itemConfig = ITEM_CONFIGS[itemType];
        if (!itemConfig) {
            console.error(`Tipo de item ${itemType} não encontrado`);
            return '';
        }

        const itemData: ItemData = {
            type: itemType,
            name: itemConfig.name,
            value: itemConfig.value,
            rarity: itemConfig.rarity as any,
            effect: itemConfig.effect,
            ...customData
        };

        const itemId = this.generateItemId();
        this.activeItems.set(itemId, itemData);

        // Criar entidade visual do item
        this.createItemEntity(itemId, itemData, position);

        return itemId;
    }

    /**
     * Cria entidade visual do item
     */
    private createItemEntity(
        itemId: string,
        itemData: ItemData,
        position: { x: number; y: number; z: number }
    ): void {
        try {
            const itemEntity = Scene.createRootEntity();
            itemEntity.setPosition(position.x, position.y, position.z);
            itemEntity.name = `Item_${itemId}`;

            // Adicionar modelo 3D baseado no tipo
            const modelAssetId = this.getModelAssetId(itemData.type);
            if (modelAssetId) {
                const modelComponent = itemEntity.createComponent("engine:mesh", {
                    assetId: modelAssetId
                });
            }

            // Adicionar efeito de brilho baseado na raridade
            this.addRarityGlow(itemEntity, itemData.rarity);

            // Adicionar animação de flutuação
            this.addFloatingAnimation(itemEntity);

            // Adicionar trigger de colisão
            this.addCollisionTrigger(itemEntity, itemId);

            // Adicionar texto informativo (opcional)
            this.addItemLabel(itemEntity, itemData);

        } catch (error) {
            console.error(`Erro ao criar entidade do item ${itemId}:`, error);
        }
    }

    /**
     * Obtém ID do asset do modelo baseado no tipo
     */
    private getModelAssetId(itemType: ItemType): string {
        switch (itemType) {
            case ItemType.GEM_SMALL:
                return ASSET_IDS.MODELS.GEM_SMALL;
            case ItemType.GEM_LARGE:
                return ASSET_IDS.MODELS.GEM_LARGE;
            case ItemType.POTION_HEALTH:
                return ASSET_IDS.MODELS.POTION;
            case ItemType.STORM_CRYSTAL:
                return ASSET_IDS.MODELS.CRYSTAL;
            default:
                return ASSET_IDS.MODELS.GEM_SMALL; // Fallback
        }
    }

    /**
     * Adiciona brilho baseado na raridade
     */
    private addRarityGlow(itemEntity: any, rarity: string): void {
        try {
            const glowColors = {
                common: { r: 0.8, g: 0.8, b: 0.8 },    // Branco
                uncommon: { r: 0.2, g: 1.0, b: 0.2 },  // Verde
                rare: { r: 0.2, g: 0.2, b: 1.0 },     // Azul
                epic: { r: 0.8, g: 0.2, b: 0.8 },     // Roxo
                legendary: { r: 1.0, g: 0.8, b: 0.2 } // Dourado
            };

            const color = glowColors[rarity as keyof typeof glowColors] || glowColors.common;

            const lightComponent = itemEntity.createComponent("engine:light", {
                type: "point",
                color: color,
                intensity: 1.5,
                range: 3.0
            });

            // Adicionar efeito de partículas
            const particleComponent = itemEntity.createComponent("engine:persistentParticleEffect", {
                assetId: `glow_${rarity}_particles`
            });

        } catch (error) {
            console.error("Erro ao adicionar brilho do item:", error);
        }
    }

    /**
     * Adiciona animação de flutuação
     */
    private addFloatingAnimation(itemEntity: any): void {
        const startY = itemEntity.getPosition().y;
        const amplitude = 0.3; // Altura da flutuação
        const frequency = 2.0; // Velocidade da flutuação
        let time = 0;

        const animate = () => {
            time += 0.016; // ~60 FPS
            const newY = startY + Math.sin(time * frequency) * amplitude;
            
            const currentPos = itemEntity.getPosition();
            itemEntity.setPosition(currentPos.x, newY, currentPos.z);

            // Rotação suave
            const rotation = itemEntity.getRotation();
            itemEntity.setRotation(rotation.x, rotation.y + 1, rotation.z);

            requestAnimationFrame(animate);
        };

        animate();
    }

    /**
     * Adiciona trigger de colisão
     */
    private addCollisionTrigger(itemEntity: any, itemId: string): void {
        try {
            // Adicionar componente de trigger
            const triggerComponent = itemEntity.createComponent("engine:trigger", {
                shape: "sphere",
                radius: 1.5
            });

            // Configurar evento de colisão
            triggerComponent.onTriggerEnter.add((other: any) => {
                // Verificar se é um jogador
                const player = this.getPlayerFromEntity(other);
                if (player) {
                    this.collectItem(player.id, itemId);
                }
            });

        } catch (error) {
            console.error("Erro ao adicionar trigger de colisão:", error);
        }
    }

    /**
     * Adiciona label informativo do item
     */
    private addItemLabel(itemEntity: any, itemData: ItemData): void {
        try {
            const labelEntity = Scene.createRootEntity();
            labelEntity.setParent(itemEntity);
            labelEntity.setPosition(0, 1.5, 0); // Acima do item

            const textComponent = labelEntity.createComponent("engine:text", {
                text: itemData.name,
                fontSize: 0.3,
                color: this.getRarityColor(itemData.rarity),
                billboard: true // Sempre virado para o jogador
            });

        } catch (error) {
            console.error("Erro ao adicionar label do item:", error);
        }
    }

    /**
     * Obtém cor baseada na raridade
     */
    private getRarityColor(rarity: string): { r: number; g: number; b: number } {
        const colors = {
            common: { r: 0.8, g: 0.8, b: 0.8 },
            uncommon: { r: 0.2, g: 1.0, b: 0.2 },
            rare: { r: 0.2, g: 0.2, b: 1.0 },
            epic: { r: 0.8, g: 0.2, b: 0.8 },
            legendary: { r: 1.0, g: 0.8, b: 0.2 }
        };
        return colors[rarity as keyof typeof colors] || colors.common;
    }

    /**
     * Obtém jogador a partir de uma entidade
     */
    private getPlayerFromEntity(entity: any): any {
        try {
            // TODO: Implementar lógica para identificar jogador
            // Por enquanto, assumir que a entidade tem propriedade player
            return entity.player || null;
        } catch (error) {
            return null;
        }
    }

    /**
     * Coleta um item
     */
    collectItem(playerId: string, itemId: string): void {
        const itemData = this.activeItems.get(itemId);
        if (!itemData) {
            console.error(`Item ${itemId} não encontrado`);
            return;
        }

        const playerData = this.playerManager.getPlayerData(playerId);
        if (!playerData) {
            console.error(`Jogador ${playerId} não encontrado`);
            return;
        }

        // Aplicar efeito do item
        this.applyItemEffect(playerId, itemData);

        // Criar efeito visual de coleta
        this.createPickupEffect(itemId, itemData);

        // Reproduzir som de coleta
        this.playPickupSound(itemData);

        // Remover item do mundo
        this.removeItem(itemId);

        // Emitir evento
        stormEvents.emitItemPickedUp(playerId, itemData.type, itemData.value);

        console.log(`${playerData.name} coletou ${itemData.name}`);
    }

    /**
     * Aplica efeito do item
     */
    private applyItemEffect(playerId: string, itemData: ItemData): void {
        const playerStats = this.playerManager.getPlayerStats(playerId);
        if (!playerStats) return;

        switch (itemData.effect) {
            case 'currency':
                // Gemas são moeda
                this.playerManager.addScore(playerId, itemData.value);
                break;

            case 'instant_heal':
                // Poções de vida
                const healing = 15 + Math.floor(Math.random() * 10); // 15-25 HP
                this.playerManager.healPlayer(playerId, healing);
                break;

            case 'mana_restore':
                // Cristais de mana
                const manaRestore = 10 + Math.floor(Math.random() * 5); // 10-15 MP
                playerStats.restoreMana(manaRestore);
                break;

            default:
                console.log(`Efeito ${itemData.effect} não implementado`);
        }
    }

    /**
     * Cria efeito visual de coleta
     */
    private createPickupEffect(itemId: string, itemData: ItemData): void {
        try {
            const itemEntity = Scene.root.findEntityByName(`Item_${itemId}`);
            if (itemEntity) {
                const position = itemEntity.getPosition();

                // Efeito de partículas de coleta
                const effectEntity = Scene.createRootEntity();
                effectEntity.setPosition(position.x, position.y, position.z);

                const particleComponent = effectEntity.createComponent("engine:persistentParticleEffect", {
                    assetId: "pickup_effect_particles"
                });

                // Remover efeito após animação
                setTimeout(() => {
                    effectEntity.destroy();
                }, 2000);
            }
        } catch (error) {
            console.error("Erro ao criar efeito de coleta:", error);
        }
    }

    /**
     * Reproduz som de coleta
     */
    private playPickupSound(itemData: ItemData): void {
        try {
            const soundEntity = Scene.createRootEntity();
            
            const audioComponent = soundEntity.createComponent("engine:audioSource", {
                assetId: ASSET_IDS.SOUNDS.GEM_PICKUP,
                loop: false,
                volume: 0.7
            });
            
            audioComponent.play();

            // Remover entidade após som
            setTimeout(() => {
                soundEntity.destroy();
            }, 3000);

        } catch (error) {
            console.error("Erro ao reproduzir som de coleta:", error);
        }
    }

    /**
     * Remove item do mundo
     */
    private removeItem(itemId: string): void {
        try {
            const itemEntity = Scene.root.findEntityByName(`Item_${itemId}`);
            if (itemEntity) {
                itemEntity.destroy();
            }
            this.activeItems.delete(itemId);
        } catch (error) {
            console.error(`Erro ao remover item ${itemId}:`, error);
        }
    }

    /**
     * Processa coleta de item
     */
    private processItemPickup(playerId: string, itemType: string, value: number): void {
        // Adicionar experiência baseada no valor do item
        const xpGain = Math.floor(value / 10);
        this.playerManager.addExperience(playerId, xpGain);

        // Atualizar estatísticas específicas
        if (itemType.includes('gem')) {
            this.playerManager.onGemCollected(playerId, value);
        }
    }

    /**
     * Gera ID único para item
     */
    private generateItemId(): string {
        return `item_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Obtém todos os itens ativos
     */
    getActiveItems(): Map<string, ItemData> {
        return new Map(this.activeItems);
    }

    /**
     * Remove todos os itens do mundo
     */
    clearAllItems(): void {
        for (const itemId of this.activeItems.keys()) {
            this.removeItem(itemId);
        }
    }

    /**
     * Cria loot drop de um inimigo
     */
    createLootDrop(
        position: { x: number; y: number; z: number },
        lootTable: Array<{ item: ItemType; chance: number }>
    ): void {
        lootTable.forEach(entry => {
            const roll = Math.random() * 100;
            if (roll <= entry.chance) {
                // Adicionar pequena variação na posição
                const dropPosition = {
                    x: position.x + (Math.random() - 0.5) * 2,
                    y: position.y + 0.5,
                    z: position.z + (Math.random() - 0.5) * 2
                };
                
                this.createItem(entry.item, dropPosition);
            }
        });
    }
}
