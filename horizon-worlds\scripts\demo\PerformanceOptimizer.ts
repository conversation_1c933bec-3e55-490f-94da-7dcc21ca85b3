/**
 * Storm RPG - Performance Optimizer
 * Otimizações específicas para dispositivos móveis e demo
 */

import { stormEvents } from '../utils/EventSystem';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Device: any;

export interface PerformanceSettings {
    maxParticles: number;
    particleQuality: 'low' | 'medium' | 'high';
    shadowQuality: 'off' | 'low' | 'medium' | 'high';
    textureQuality: 'low' | 'medium' | 'high';
    maxAudioSources: number;
    maxEntities: number;
    enableLOD: boolean;
    cullingDistance: number;
}

export interface DeviceProfile {
    name: string;
    isMobile: boolean;
    performanceLevel: 'low' | 'medium' | 'high';
    settings: PerformanceSettings;
}

export class PerformanceOptimizer {
    private currentProfile: DeviceProfile;
    private activeEntities: Set<any> = new Set();
    private particlePool: any[] = [];
    private audioPool: any[] = [];
    private lodLevels: Map<string, any[]> = new Map();

    constructor() {
        this.detectDevice();
        this.applyOptimizations();
        this.setupMonitoring();
        console.log(`[PerformanceOptimizer] Inicializado para ${this.currentProfile.name}`);
    }

    /**
     * Detecta o dispositivo e define perfil de performance
     */
    private detectDevice(): void {
        // Simulação de detecção de dispositivo
        const isMobile = this.isMobileDevice();
        const performanceLevel = this.getPerformanceLevel();

        if (isMobile) {
            if (performanceLevel === 'low') {
                this.currentProfile = this.getMobileLowProfile();
            } else if (performanceLevel === 'medium') {
                this.currentProfile = this.getMobileMediumProfile();
            } else {
                this.currentProfile = this.getMobileHighProfile();
            }
        } else {
            // Desktop/VR
            this.currentProfile = this.getDesktopProfile();
        }

        console.log(`[PerformanceOptimizer] Perfil detectado: ${this.currentProfile.name}`);
    }

    /**
     * Verifica se é dispositivo móvel
     */
    private isMobileDevice(): boolean {
        // No Horizon Worlds, isso seria detectado automaticamente
        // Simulação baseada em user agent ou API do dispositivo
        return typeof Device !== 'undefined' && Device.isMobile;
    }

    /**
     * Determina nível de performance do dispositivo
     */
    private getPerformanceLevel(): 'low' | 'medium' | 'high' {
        // Simulação baseada em specs do dispositivo
        // No mundo real, isso seria baseado em benchmarks ou APIs do sistema
        if (typeof Device !== 'undefined') {
            const ram = Device.getMemoryInfo?.() || 4;
            const gpu = Device.getGPUInfo?.() || 'unknown';
            
            if (ram < 4) return 'low';
            if (ram < 8) return 'medium';
            return 'high';
        }
        
        return 'medium'; // Padrão
    }

    /**
     * Perfil para mobile baixo desempenho
     */
    private getMobileLowProfile(): DeviceProfile {
        return {
            name: 'Mobile Low',
            isMobile: true,
            performanceLevel: 'low',
            settings: {
                maxParticles: 20,
                particleQuality: 'low',
                shadowQuality: 'off',
                textureQuality: 'low',
                maxAudioSources: 3,
                maxEntities: 50,
                enableLOD: true,
                cullingDistance: 30
            }
        };
    }

    /**
     * Perfil para mobile médio desempenho
     */
    private getMobileMediumProfile(): DeviceProfile {
        return {
            name: 'Mobile Medium',
            isMobile: true,
            performanceLevel: 'medium',
            settings: {
                maxParticles: 50,
                particleQuality: 'medium',
                shadowQuality: 'low',
                textureQuality: 'medium',
                maxAudioSources: 5,
                maxEntities: 100,
                enableLOD: true,
                cullingDistance: 50
            }
        };
    }

    /**
     * Perfil para mobile alto desempenho
     */
    private getMobileHighProfile(): DeviceProfile {
        return {
            name: 'Mobile High',
            isMobile: true,
            performanceLevel: 'high',
            settings: {
                maxParticles: 100,
                particleQuality: 'high',
                shadowQuality: 'medium',
                textureQuality: 'high',
                maxAudioSources: 8,
                maxEntities: 150,
                enableLOD: true,
                cullingDistance: 75
            }
        };
    }

    /**
     * Perfil para desktop/VR
     */
    private getDesktopProfile(): DeviceProfile {
        return {
            name: 'Desktop/VR',
            isMobile: false,
            performanceLevel: 'high',
            settings: {
                maxParticles: 200,
                particleQuality: 'high',
                shadowQuality: 'high',
                textureQuality: 'high',
                maxAudioSources: 12,
                maxEntities: 300,
                enableLOD: false,
                cullingDistance: 100
            }
        };
    }

    /**
     * Aplica otimizações baseadas no perfil
     */
    private applyOptimizations(): void {
        this.optimizeParticles();
        this.optimizeTextures();
        this.optimizeShadows();
        this.optimizeAudio();
        this.setupEntityCulling();
        this.setupLODSystem();
    }

    /**
     * Otimiza sistema de partículas
     */
    private optimizeParticles(): void {
        const settings = this.currentProfile.settings;
        
        // Configurar qualidade global de partículas
        if (typeof Scene.setParticleQuality === 'function') {
            Scene.setParticleQuality(settings.particleQuality);
        }

        // Criar pool de partículas para reutilização
        this.createParticlePool(settings.maxParticles);

        console.log(`[PerformanceOptimizer] Partículas otimizadas: max ${settings.maxParticles}, qualidade ${settings.particleQuality}`);
    }

    /**
     * Cria pool de partículas reutilizáveis
     */
    private createParticlePool(maxParticles: number): void {
        for (let i = 0; i < maxParticles; i++) {
            const particle = Scene.createRootEntity();
            particle.setActive(false);
            this.particlePool.push(particle);
        }
    }

    /**
     * Obtém partícula do pool
     */
    public getParticleFromPool(): any | null {
        const particle = this.particlePool.find(p => !p.isActive());
        if (particle) {
            particle.setActive(true);
            return particle;
        }
        return null; // Pool esgotado
    }

    /**
     * Retorna partícula para o pool
     */
    public returnParticleToPool(particle: any): void {
        if (particle) {
            particle.setActive(false);
            particle.setPosition(0, -1000, 0); // Mover para fora da vista
        }
    }

    /**
     * Otimiza texturas
     */
    private optimizeTextures(): void {
        const settings = this.currentProfile.settings;
        
        // Configurar qualidade de textura global
        if (typeof Scene.setTextureQuality === 'function') {
            Scene.setTextureQuality(settings.textureQuality);
        }

        // Para mobile, usar texturas comprimidas
        if (this.currentProfile.isMobile) {
            this.enableTextureCompression();
        }

        console.log(`[PerformanceOptimizer] Texturas otimizadas: qualidade ${settings.textureQuality}`);
    }

    /**
     * Habilita compressão de texturas
     */
    private enableTextureCompression(): void {
        // Implementação específica do Horizon Worlds
        if (typeof Scene.enableTextureCompression === 'function') {
            Scene.enableTextureCompression(true);
        }
    }

    /**
     * Otimiza sombras
     */
    private optimizeShadows(): void {
        const settings = this.currentProfile.settings;
        
        // Configurar qualidade de sombras
        if (typeof Scene.setShadowQuality === 'function') {
            Scene.setShadowQuality(settings.shadowQuality);
        }

        // Desabilitar sombras em dispositivos de baixo desempenho
        if (settings.shadowQuality === 'off') {
            this.disableAllShadows();
        }

        console.log(`[PerformanceOptimizer] Sombras otimizadas: qualidade ${settings.shadowQuality}`);
    }

    /**
     * Desabilita todas as sombras
     */
    private disableAllShadows(): void {
        // Encontrar todas as luzes e desabilitar sombras
        const lights = Scene.root.findEntitiesByComponent('engine:light');
        lights.forEach((light: any) => {
            const lightComponent = light.getComponent('engine:light');
            if (lightComponent) {
                lightComponent.castShadows = false;
            }
        });
    }

    /**
     * Otimiza sistema de áudio
     */
    private optimizeAudio(): void {
        const settings = this.currentProfile.settings;
        
        // Criar pool de fontes de áudio
        this.createAudioPool(settings.maxAudioSources);

        // Configurar qualidade de áudio para mobile
        if (this.currentProfile.isMobile) {
            this.optimizeAudioForMobile();
        }

        console.log(`[PerformanceOptimizer] Áudio otimizado: max ${settings.maxAudioSources} fontes`);
    }

    /**
     * Cria pool de fontes de áudio
     */
    private createAudioPool(maxSources: number): void {
        for (let i = 0; i < maxSources; i++) {
            const audioEntity = Scene.createRootEntity();
            const audioSource = audioEntity.createComponent('engine:audioSource', {
                volume: 0,
                loop: false
            });
            audioEntity.setActive(false);
            this.audioPool.push(audioEntity);
        }
    }

    /**
     * Obtém fonte de áudio do pool
     */
    public getAudioFromPool(): any | null {
        const audio = this.audioPool.find(a => !a.isActive());
        if (audio) {
            audio.setActive(true);
            return audio;
        }
        return null;
    }

    /**
     * Retorna fonte de áudio para o pool
     */
    public returnAudioToPool(audio: any): void {
        if (audio) {
            const audioSource = audio.getComponent('engine:audioSource');
            if (audioSource) {
                audioSource.stop();
                audioSource.volume = 0;
            }
            audio.setActive(false);
        }
    }

    /**
     * Otimiza áudio para mobile
     */
    private optimizeAudioForMobile(): void {
        // Reduzir qualidade de áudio
        if (typeof Scene.setAudioQuality === 'function') {
            Scene.setAudioQuality('medium');
        }

        // Limitar alcance de áudio espacial
        const audioSources = Scene.root.findEntitiesByComponent('engine:audioSource');
        audioSources.forEach((entity: any) => {
            const audioSource = entity.getComponent('engine:audioSource');
            if (audioSource && audioSource.spatialAudio) {
                audioSource.maxDistance = Math.min(audioSource.maxDistance || 50, 30);
            }
        });
    }

    /**
     * Configura sistema de culling de entidades
     */
    private setupEntityCulling(): void {
        const settings = this.currentProfile.settings;
        
        // Monitorar entidades ativas
        setInterval(() => {
            this.cullDistantEntities(settings.cullingDistance);
        }, 2000); // Verificar a cada 2 segundos

        console.log(`[PerformanceOptimizer] Culling configurado: distância ${settings.cullingDistance}m`);
    }

    /**
     * Remove entidades distantes da vista
     */
    private cullDistantEntities(maxDistance: number): void {
        // Obter posição dos jogadores
        const players = Scene.root.findEntitiesByComponent('player');
        if (players.length === 0) return;

        // Para cada entidade ativa
        this.activeEntities.forEach(entity => {
            let shouldCull = true;

            // Verificar distância para cada jogador
            players.forEach((player: any) => {
                const playerPos = player.getPosition();
                const entityPos = entity.getPosition();
                const distance = this.calculateDistance(playerPos, entityPos);

                if (distance <= maxDistance) {
                    shouldCull = false;
                }
            });

            // Culling da entidade se muito distante
            if (shouldCull && entity.isActive()) {
                entity.setActive(false);
            } else if (!shouldCull && !entity.isActive()) {
                entity.setActive(true);
            }
        });
    }

    /**
     * Calcula distância entre duas posições
     */
    private calculateDistance(pos1: any, pos2: any): number {
        const dx = pos1.x - pos2.x;
        const dy = pos1.y - pos2.y;
        const dz = pos1.z - pos2.z;
        return Math.sqrt(dx * dx + dy * dy + dz * dz);
    }

    /**
     * Configura sistema de Level of Detail (LOD)
     */
    private setupLODSystem(): void {
        if (!this.currentProfile.settings.enableLOD) return;

        // Configurar LODs para modelos complexos
        this.setupModelLODs();
        
        // Monitorar e aplicar LODs
        setInterval(() => {
            this.updateLODs();
        }, 1000);

        console.log("[PerformanceOptimizer] Sistema LOD configurado");
    }

    /**
     * Configura LODs para modelos
     */
    private setupModelLODs(): void {
        const complexModels = ['npc_model', 'building_model', 'tree_model'];
        
        complexModels.forEach(modelType => {
            const entities = Scene.root.findEntitiesByName(modelType);
            entities.forEach((entity: any) => {
                this.createLODVersions(entity);
            });
        });
    }

    /**
     * Cria versões LOD de uma entidade
     */
    private createLODVersions(entity: any): void {
        const lodVersions = [
            { distance: 0, quality: 'high' },
            { distance: 25, quality: 'medium' },
            { distance: 50, quality: 'low' }
        ];

        this.lodLevels.set(entity.id, lodVersions);
    }

    /**
     * Atualiza LODs baseado na distância dos jogadores
     */
    private updateLODs(): void {
        const players = Scene.root.findEntitiesByComponent('player');
        if (players.length === 0) return;

        this.lodLevels.forEach((lodVersions, entityId) => {
            const entity = Scene.root.findEntityById(entityId);
            if (!entity) return;

            // Encontrar distância mínima para qualquer jogador
            let minDistance = Infinity;
            players.forEach((player: any) => {
                const distance = this.calculateDistance(
                    player.getPosition(),
                    entity.getPosition()
                );
                minDistance = Math.min(minDistance, distance);
            });

            // Aplicar LOD apropriado
            this.applyLOD(entity, minDistance);
        });
    }

    /**
     * Aplica LOD apropriado para uma entidade
     */
    private applyLOD(entity: any, distance: number): void {
        let targetQuality = 'high';

        if (distance > 50) {
            targetQuality = 'low';
        } else if (distance > 25) {
            targetQuality = 'medium';
        }

        // Aplicar qualidade (implementação específica do modelo)
        const mesh = entity.getComponent('engine:mesh');
        if (mesh) {
            mesh.quality = targetQuality;
        }
    }

    /**
     * Configura monitoramento de performance
     */
    private setupMonitoring(): void {
        // Monitorar FPS e ajustar configurações dinamicamente
        setInterval(() => {
            this.monitorPerformance();
        }, 5000);
    }

    /**
     * Monitora performance e ajusta configurações
     */
    private monitorPerformance(): void {
        // Simulação de monitoramento de FPS
        const currentFPS = this.getCurrentFPS();
        const targetFPS = this.currentProfile.isMobile ? 30 : 60;

        if (currentFPS < targetFPS * 0.8) {
            // Performance baixa, reduzir qualidade
            this.reduceQuality();
        } else if (currentFPS > targetFPS * 1.1) {
            // Performance boa, pode aumentar qualidade
            this.increaseQuality();
        }
    }

    /**
     * Obtém FPS atual (simulado)
     */
    private getCurrentFPS(): number {
        // No mundo real, isso seria obtido de APIs do sistema
        return Math.random() * 60 + 30; // Simulação
    }

    /**
     * Reduz qualidade para melhorar performance
     */
    private reduceQuality(): void {
        const settings = this.currentProfile.settings;
        
        // Reduzir partículas
        if (settings.maxParticles > 10) {
            settings.maxParticles = Math.max(10, settings.maxParticles - 10);
        }

        // Reduzir distância de culling
        if (settings.cullingDistance > 20) {
            settings.cullingDistance = Math.max(20, settings.cullingDistance - 5);
        }

        console.log("[PerformanceOptimizer] Qualidade reduzida para melhorar performance");
    }

    /**
     * Aumenta qualidade se performance permitir
     */
    private increaseQuality(): void {
        const settings = this.currentProfile.settings;
        const maxSettings = this.getMaxSettingsForProfile();
        
        // Aumentar partículas
        if (settings.maxParticles < maxSettings.maxParticles) {
            settings.maxParticles = Math.min(maxSettings.maxParticles, settings.maxParticles + 5);
        }

        // Aumentar distância de culling
        if (settings.cullingDistance < maxSettings.cullingDistance) {
            settings.cullingDistance = Math.min(maxSettings.cullingDistance, settings.cullingDistance + 5);
        }

        console.log("[PerformanceOptimizer] Qualidade aumentada");
    }

    /**
     * Obtém configurações máximas para o perfil atual
     */
    private getMaxSettingsForProfile(): PerformanceSettings {
        // Retornar configurações originais do perfil
        if (this.currentProfile.isMobile) {
            if (this.currentProfile.performanceLevel === 'low') {
                return this.getMobileLowProfile().settings;
            } else if (this.currentProfile.performanceLevel === 'medium') {
                return this.getMobileMediumProfile().settings;
            } else {
                return this.getMobileHighProfile().settings;
            }
        } else {
            return this.getDesktopProfile().settings;
        }
    }

    /**
     * Registra entidade para monitoramento
     */
    public registerEntity(entity: any): void {
        this.activeEntities.add(entity);
    }

    /**
     * Remove entidade do monitoramento
     */
    public unregisterEntity(entity: any): void {
        this.activeEntities.delete(entity);
    }

    /**
     * Obtém perfil atual
     */
    public getCurrentProfile(): DeviceProfile {
        return this.currentProfile;
    }

    /**
     * Força aplicação de um perfil específico
     */
    public applyProfile(profile: DeviceProfile): void {
        this.currentProfile = profile;
        this.applyOptimizations();
        console.log(`[PerformanceOptimizer] Perfil aplicado: ${profile.name}`);
    }

    /**
     * Obtém estatísticas de performance
     */
    public getPerformanceStats(): any {
        return {
            profile: this.currentProfile.name,
            activeEntities: this.activeEntities.size,
            particlePoolUsage: this.particlePool.filter(p => p.isActive()).length,
            audioPoolUsage: this.audioPool.filter(a => a.isActive()).length,
            currentFPS: this.getCurrentFPS()
        };
    }
}

// Instância global do otimizador de performance
export const performanceOptimizer = new PerformanceOptimizer();
