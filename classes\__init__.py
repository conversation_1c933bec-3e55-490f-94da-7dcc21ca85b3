"""
Storm RPG - Sistema de Classes
Todas as classes de personagem de Storm RPG
"""

from .base_class import CharacterClass, ClassFeature, ClassLevel
from .warrior import Warrior
from .rogue import Rogue
from .cleric import Cleric
from .wizard import Wizard
from .sorcerer import Sorcerer
from .barbarian import Barbarian
from .druid import Druid
from .ranger import Ranger
from .paladin import Paladin
from .bard import Bard

__all__ = [
    "CharacterClass",
    "ClassFeature", 
    "ClassLevel",
    "Warrior",
    "Rogue",
    "Cleric",
    "Wizard",
    "Sorcerer",
    "Barbarian",
    "Druid",
    "Ranger",
    "Paladin",
    "Bard"
]

# Dicionário para fácil acesso às classes
AVAILABLE_CLASSES = {
    "warrior": Warrior,
    "rogue": Rogue,
    "cleric": C<PERSON>ic,
    "wizard": Wizard,
    "sorcerer": Sorcerer,
    "barbarian": Barbarian,
    "druid": Druid,
    "ranger": Ranger,
    "paladin": Paladin,
    "bard": Bard
}

def get_class_by_name(name: str) -> CharacterClass:
    """Retorna uma instância da classe pelo nome"""
    class_type = AVAILABLE_CLASSES.get(name.lower())
    if class_type:
        return class_type()
    raise ValueError(f"Classe '{name}' não encontrada")

def list_available_classes() -> list:
    """Lista todas as classes disponíveis"""
    return list(AVAILABLE_CLASSES.keys())
