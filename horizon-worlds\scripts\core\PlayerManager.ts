/**
 * Storm RPG - Player Manager
 * Gerenciamento de jogadores, spawn e seleção de classes
 */

import { PlayerStats } from './AttributeSystem';
import { PlayerClass, GAME_CONFIG, WORLD_SETTINGS } from '../utils/Constants';
import { stormEvents } from '../utils/EventSystem';

// Importações do Horizon Worlds (simuladas para TypeScript)
declare const Scene: any;
declare const Players: any;
declare const Player: any;

export interface PlayerData {
    id: string;
    name: string;
    stats: PlayerStats;
    entity: any; // Player entity do Horizon Worlds
    isAlive: boolean;
    lastRespawnTime: number;
    score: number;
    kills: number;
    deaths: number;
    gemsCollected: number;
}

export class PlayerManager {
    private players: Map<string, PlayerData> = new Map();
    private spawnPoints: Array<{ x: number; y: number; z: number }>;
    private nextSpawnIndex: number = 0;

    constructor() {
        this.spawnPoints = WORLD_SETTINGS.PLAYER_SPAWN_POINTS;
        this.setupPlayerEvents();
    }

    /**
     * Configura eventos de jogadores
     */
    private setupPlayerEvents(): void {
        // Evento quando jogador entra
        Players.onPlayerJoined.add((player: any) => {
            this.onPlayerJoined(player);
        });

        // Evento quando jogador sai
        Players.onPlayerLeft.add((player: any) => {
            this.onPlayerLeft(player);
        });

        // Evento quando jogador morre (se disponível)
        // Players.onPlayerDied?.add((player: any) => {
        //     this.onPlayerDied(player);
        // });
    }

    /**
     * Quando um jogador entra no mundo
     */
    private onPlayerJoined(player: any): void {
        console.log(`[PlayerManager] ${player.name} entrou no jogo`);

        // Criar dados do jogador
        const playerData: PlayerData = {
            id: player.id,
            name: player.name,
            stats: new PlayerStats(PlayerClass.WARRIOR), // Classe padrão temporária
            entity: player,
            isAlive: true,
            lastRespawnTime: 0,
            score: 0,
            kills: 0,
            deaths: 0,
            gemsCollected: 0
        };

        this.players.set(player.id, playerData);

        // Mostrar seleção de classe
        this.showClassSelection(player);

        // Emitir evento
        stormEvents.emitPlayerJoined(player.id, player.name);
    }

    /**
     * Quando um jogador sai do mundo
     */
    private onPlayerLeft(player: any): void {
        console.log(`[PlayerManager] ${player.name} saiu do jogo`);
        this.players.delete(player.id);
    }

    /**
     * Mostra interface de seleção de classe
     */
    private showClassSelection(player: any): void {
        // Em um mundo real, isso seria uma UI interativa
        // Por enquanto, vamos usar uma seleção automática ou baseada em input
        
        // Simular seleção aleatória por enquanto
        const classes = [PlayerClass.WARRIOR, PlayerClass.MAGE, PlayerClass.ROGUE];
        const randomClass = classes[Math.floor(Math.random() * classes.length)];
        
        this.selectPlayerClass(player.id, randomClass);
    }

    /**
     * Seleciona classe do jogador
     */
    selectPlayerClass(playerId: string, playerClass: PlayerClass): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        // Criar novas estatísticas baseadas na classe
        playerData.stats = new PlayerStats(playerClass);

        console.log(`[PlayerManager] ${playerData.name} escolheu a classe ${playerClass}`);

        // Fazer spawn do jogador
        this.spawnPlayer(playerId);

        // Emitir evento
        stormEvents.emitPlayerJoined(playerId, playerData.name);
    }

    /**
     * Faz spawn do jogador
     */
    spawnPlayer(playerId: string): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        // Escolher ponto de spawn
        const spawnPoint = this.getNextSpawnPoint();
        
        // Teleportar jogador
        try {
            playerData.entity.setPosition(spawnPoint.x, spawnPoint.y, spawnPoint.z);
            playerData.isAlive = true;
            playerData.lastRespawnTime = Date.now();

            // Restaurar HP/MP completos
            playerData.stats.derivedStats.hitPoints = playerData.stats.derivedStats.maxHitPoints;
            playerData.stats.derivedStats.manaPoints = playerData.stats.derivedStats.maxManaPoints;

            console.log(`[PlayerManager] ${playerData.name} fez spawn em ${spawnPoint.x}, ${spawnPoint.y}, ${spawnPoint.z}`);

            // Emitir evento
            stormEvents.eventSystem.emit('player_spawned', {
                playerId,
                position: spawnPoint,
                stats: playerData.stats.getSummary()
            });

        } catch (error) {
            console.error(`Erro ao fazer spawn do jogador ${playerId}:`, error);
        }
    }

    /**
     * Obtém próximo ponto de spawn
     */
    private getNextSpawnPoint(): { x: number; y: number; z: number } {
        const spawnPoint = this.spawnPoints[this.nextSpawnIndex];
        this.nextSpawnIndex = (this.nextSpawnIndex + 1) % this.spawnPoints.length;
        return spawnPoint;
    }

    /**
     * Quando jogador morre
     */
    onPlayerDied(playerId: string, killerId?: string): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        playerData.isAlive = false;
        playerData.deaths++;

        // Se foi morto por outro jogador
        if (killerId && killerId !== playerId) {
            const killer = this.players.get(killerId);
            if (killer) {
                killer.kills++;
                killer.score += 100; // Pontos por kill
            }
        }

        console.log(`[PlayerManager] ${playerData.name} morreu`);

        // Emitir evento
        stormEvents.emitPlayerDied(playerId, killerId);

        // Programar respawn
        setTimeout(() => {
            this.respawnPlayer(playerId);
        }, GAME_CONFIG.RESPAWN_TIME);
    }

    /**
     * Respawn do jogador
     */
    private respawnPlayer(playerId: string): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        // Verificar se pode fazer respawn
        const timeSinceDeath = Date.now() - playerData.lastRespawnTime;
        if (timeSinceDeath < GAME_CONFIG.RESPAWN_TIME) {
            return; // Ainda em cooldown
        }

        this.spawnPlayer(playerId);
    }

    /**
     * Adiciona experiência ao jogador
     */
    addExperience(playerId: string, xp: number): boolean {
        const playerData = this.players.get(playerId);
        if (!playerData) return false;

        const leveledUp = playerData.stats.addExperience(xp);
        
        if (leveledUp) {
            console.log(`[PlayerManager] ${playerData.name} subiu para o nível ${playerData.stats.level}!`);
            
            // Emitir evento de level up
            stormEvents.eventSystem.emit('player_level_up', {
                playerId,
                newLevel: playerData.stats.level,
                playerName: playerData.name
            });
        }

        return leveledUp;
    }

    /**
     * Adiciona pontos ao jogador
     */
    addScore(playerId: string, points: number): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        playerData.score += points;
    }

    /**
     * Registra coleta de gema
     */
    onGemCollected(playerId: string, gemValue: number): void {
        const playerData = this.players.get(playerId);
        if (!playerData) return;

        playerData.gemsCollected++;
        this.addScore(playerId, gemValue);
        this.addExperience(playerId, GAME_CONFIG.XP_PER_GEM_COLLECTED);

        // Emitir evento
        stormEvents.emitItemPickedUp(playerId, 'gem', gemValue);
    }

    /**
     * Obtém dados do jogador
     */
    getPlayerData(playerId: string): PlayerData | undefined {
        return this.players.get(playerId);
    }

    /**
     * Obtém estatísticas do jogador
     */
    getPlayerStats(playerId: string): PlayerStats | undefined {
        const playerData = this.players.get(playerId);
        return playerData?.stats;
    }

    /**
     * Obtém todos os jogadores
     */
    getAllPlayers(): PlayerData[] {
        return Array.from(this.players.values());
    }

    /**
     * Obtém jogadores vivos
     */
    getAlivePlayers(): PlayerData[] {
        return this.getAllPlayers().filter(player => player.isAlive);
    }

    /**
     * Obtém contagem de jogadores
     */
    getPlayerCount(): number {
        return this.players.size;
    }

    /**
     * Verifica se o jogo pode começar
     */
    canStartGame(): boolean {
        return this.getPlayerCount() >= 1 && this.getPlayerCount() <= GAME_CONFIG.MAX_PLAYERS;
    }

    /**
     * Obtém placar dos jogadores
     */
    getLeaderboard(): Array<{
        name: string;
        score: number;
        level: number;
        kills: number;
        deaths: number;
        gemsCollected: number;
    }> {
        return this.getAllPlayers()
            .map(player => ({
                name: player.name,
                score: player.score,
                level: player.stats.level,
                kills: player.kills,
                deaths: player.deaths,
                gemsCollected: player.gemsCollected
            }))
            .sort((a, b) => b.score - a.score);
    }

    /**
     * Aplica dano a um jogador
     */
    damagePlayer(playerId: string, damage: number, attackerId?: string): boolean {
        const playerData = this.players.get(playerId);
        if (!playerData || !playerData.isAlive) return false;

        const actualDamage = playerData.stats.takeDamage(damage);
        
        // Emitir evento de dano
        stormEvents.emitDamageDealt(attackerId || 'unknown', playerId, actualDamage);

        // Verificar se morreu
        if (!playerData.stats.isAlive()) {
            this.onPlayerDied(playerId, attackerId);
            return true; // Morreu
        }

        return false; // Ainda vivo
    }

    /**
     * Cura um jogador
     */
    healPlayer(playerId: string, healing: number): number {
        const playerData = this.players.get(playerId);
        if (!playerData) return 0;

        const actualHealing = playerData.stats.heal(healing);
        
        // Emitir evento de cura
        stormEvents.eventSystem.emit('healing_applied', {
            playerId,
            healing: actualHealing
        });

        return actualHealing;
    }

    /**
     * Limpa todos os jogadores (para reset do jogo)
     */
    clearAllPlayers(): void {
        this.players.clear();
        this.nextSpawnIndex = 0;
    }
}
