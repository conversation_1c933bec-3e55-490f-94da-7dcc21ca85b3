"""
Storm RPG - Classe Guerreiro
Mestre do combate corpo a corpo
"""

from typing import List, Dict, Optional
from .base_class import (CharacterClass, ClassFeature, ClassLevel, SpellcastingType,
                        create_full_bab_progression, create_good_save_progression, 
                        create_poor_save_progression)
from core.attributes import AttributeType


class Warrior(CharacterClass):
    """
    Guerreiro - Mestre do combate
    
    Os guerreiros são mestres das armas e táticas de combate.
    Possuem a maior progressão de ataque e podem escolher entre
    diferentes estilos de combate: Duelista, Tanque ou Arma Pesada.
    """
    
    def get_name(self) -> str:
        return "Guerreiro"
    
    def get_description(self) -> str:
        return ("Mestre do combate corpo a corpo. Especialista em armas, "
                "táticas e proteção de aliados.")
    
    def get_hit_die(self) -> int:
        return 10  # d10
    
    def get_skill_points_per_level(self) -> int:
        return 2
    
    def get_class_skills(self) -> List[str]:
        return [
            "Atletismo",
            "Intimida<PERSON>", 
            "Sobrevivência",
            "Conhecimento (Guerra)",
            "Montar",
            "Nadar",
            "Saltar"
        ]
    
    def get_weapon_proficiencies(self) -> List[str]:
        return [
            "Armas Simples",
            "Armas Marciais", 
            "Armas Exóticas (com treinamento)"
        ]
    
    def get_armor_proficiencies(self) -> List[str]:
        return [
            "Armaduras Leves",
            "Armaduras Médias",
            "Armaduras Pesadas",
            "Escudos"
        ]
    
    def get_spellcasting_type(self) -> SpellcastingType:
        return SpellcastingType.NONE
    
    def get_spellcasting_attribute(self) -> Optional[AttributeType]:
        return None
    
    def get_class_levels(self) -> Dict[int, ClassLevel]:
        bab_progression = create_full_bab_progression()
        fort_progression = create_good_save_progression()
        ref_progression = create_poor_save_progression()
        will_progression = create_poor_save_progression()
        
        levels = {}
        
        for level in range(1, 21):
            features = []
            
            # Características específicas por nível
            if level == 1:
                features.extend([
                    ClassFeature(
                        name="Estilo de Combate",
                        description="Escolha um estilo: Duelista, Tanque ou Arma Pesada",
                        level_gained=1,
                        feature_type="passive"
                    ),
                    ClassFeature(
                        name="Talento Adicional",
                        description="Guerreiros recebem talentos extras nos níveis ímpares",
                        level_gained=1,
                        feature_type="passive"
                    )
                ])
            
            elif level == 2:
                features.append(
                    ClassFeature(
                        name="Bravura",
                        description="Recebe +1 em testes de resistência contra medo",
                        level_gained=2,
                        feature_type="passive"
                    )
                )
            
            elif level == 3:
                features.append(
                    ClassFeature(
                        name="Especialização em Arma",
                        description="Escolha uma arma para receber +1 em dano",
                        level_gained=3,
                        feature_type="passive"
                    )
                )
            
            elif level == 4:
                features.append(
                    ClassFeature(
                        name="Resistência a Críticos",
                        description="25% de chance de negar críticos",
                        level_gained=4,
                        feature_type="passive"
                    )
                )
            
            elif level == 5:
                features.append(
                    ClassFeature(
                        name="Ataque Extra",
                        description="Pode fazer um ataque adicional por turno",
                        level_gained=5,
                        feature_type="passive"
                    )
                )
            
            elif level == 6:
                features.append(
                    ClassFeature(
                        name="Bravura Aprimorada",
                        description="Recebe +2 em testes de resistência contra medo",
                        level_gained=6,
                        feature_type="passive"
                    )
                )
            
            elif level == 7:
                features.append(
                    ClassFeature(
                        name="Especialização Maior",
                        description="Aumenta o bônus de especialização para +2",
                        level_gained=7,
                        feature_type="passive"
                    )
                )
            
            elif level == 8:
                features.append(
                    ClassFeature(
                        name="Resistência a Críticos Aprimorada",
                        description="50% de chance de negar críticos",
                        level_gained=8,
                        feature_type="passive"
                    )
                )
            
            elif level == 10:
                features.append(
                    ClassFeature(
                        name="Bravura Superior",
                        description="Imune a medo",
                        level_gained=10,
                        feature_type="passive"
                    )
                )
            
            elif level == 11:
                features.append(
                    ClassFeature(
                        name="Especialização Suprema",
                        description="Aumenta o bônus de especialização para +3",
                        level_gained=11,
                        feature_type="passive"
                    )
                )
            
            elif level == 12:
                features.append(
                    ClassFeature(
                        name="Resistência a Críticos Superior",
                        description="75% de chance de negar críticos",
                        level_gained=12,
                        feature_type="passive"
                    )
                )
            
            elif level == 15:
                features.append(
                    ClassFeature(
                        name="Ataque Triplo",
                        description="Pode fazer dois ataques adicionais por turno",
                        level_gained=15,
                        feature_type="passive"
                    )
                )
            
            elif level == 16:
                features.append(
                    ClassFeature(
                        name="Resistência a Críticos Suprema",
                        description="Imune a críticos",
                        level_gained=16,
                        feature_type="passive"
                    )
                )
            
            elif level == 20:
                features.append(
                    ClassFeature(
                        name="Mestre das Armas",
                        description="Crítico em 19-20 com todas as armas",
                        level_gained=20,
                        feature_type="passive"
                    )
                )
            
            # Talentos adicionais em níveis ímpares
            if level % 2 == 1:
                features.append(
                    ClassFeature(
                        name="Talento Adicional",
                        description=f"Recebe um talento adicional no nível {level}",
                        level_gained=level,
                        feature_type="passive"
                    )
                )
            
            levels[level] = ClassLevel(
                level=level,
                hit_die=10,
                base_attack_bonus=bab_progression[level],
                fortitude_save=fort_progression[level],
                reflex_save=ref_progression[level],
                will_save=will_progression[level],
                features=features
            )
        
        return levels


class Duelist(Warrior):
    """
    Especialização: Duelista
    Focado em agilidade e precisão com armas leves
    """
    
    def get_name(self) -> str:
        return "Guerreiro Duelista"
    
    def get_description(self) -> str:
        return ("Especialista em combate ágil com armas leves. "
                "Foca em precisão e mobilidade.")
    
    def get_class_levels(self) -> Dict[int, ClassLevel]:
        levels = super().get_class_levels()
        
        # Modificar características específicas do duelista
        levels[1].features.append(
            ClassFeature(
                name="Finesse",
                description="Pode usar Destreza em vez de Força para ataques com armas leves",
                level_gained=1,
                feature_type="passive"
            )
        )
        
        levels[2].features.append(
            ClassFeature(
                name="Esquiva",
                description="Recebe +1 de bônus de esquiva na CA",
                level_gained=2,
                feature_type="passive"
            )
        )
        
        return levels


class Tank(Warrior):
    """
    Especialização: Tanque
    Focado em proteção e resistência
    """
    
    def get_name(self) -> str:
        return "Guerreiro Tanque"
    
    def get_description(self) -> str:
        return ("Especialista em proteção e resistência. "
                "Foca em defender aliados e absorver dano.")
    
    def get_class_levels(self) -> Dict[int, ClassLevel]:
        levels = super().get_class_levels()
        
        # Modificar características específicas do tanque
        levels[1].features.append(
            ClassFeature(
                name="Proteção",
                description="Pode usar reação para dar +2 CA a aliado adjacente",
                level_gained=1,
                feature_type="active",
                uses_per_day=None  # Ilimitado
            )
        )
        
        levels[2].features.append(
            ClassFeature(
                name="Resistência",
                description="Recebe +1 HP por nível de guerreiro",
                level_gained=2,
                feature_type="passive"
            )
        )
        
        return levels


class HeavyWeapon(Warrior):
    """
    Especialização: Arma Pesada
    Focado em força bruta e dano massivo
    """
    
    def get_name(self) -> str:
        return "Guerreiro de Arma Pesada"
    
    def get_description(self) -> str:
        return ("Especialista em armas pesadas e força bruta. "
                "Foca em causar dano massivo.")
    
    def get_class_levels(self) -> Dict[int, ClassLevel]:
        levels = super().get_class_levels()
        
        # Modificar características específicas de arma pesada
        levels[1].features.append(
            ClassFeature(
                name="Golpe Poderoso",
                description="Pode trocar -1 de ataque por +2 de dano",
                level_gained=1,
                feature_type="active"
            )
        )
        
        levels[2].features.append(
            ClassFeature(
                name="Força Bruta",
                description="Recebe +2 em testes de Força",
                level_gained=2,
                feature_type="passive"
            )
        )
        
        return levels


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Classe Guerreiro ===")
    
    warrior = Warrior()
    print(warrior.get_summary())
    
    print(f"\n" + "="*50)
    
    duelist = Duelist()
    print(f"Especialização: {duelist.get_name()}")
    level_1_features = [f.name for f in duelist.get_features_at_level(1)]
    print(f"Características nível 1: {', '.join(level_1_features)}")
