"""
Storm RPG - <PERSON><PERSON> (Merrow)
Híbridos de humanos e criaturas marinhas
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus
from core.attributes import AttributeType


class Sereian(Race):
    """
    <PERSON><PERSON><PERSON>s (Merrow) - Híbridos marinhos
    
    Os sereianos são híbridos de humanos e criaturas marinhas,
    resultado de antigas magias ou influência da Storm aquática.
    Possuem características tanto terrestres quanto aquáticas.
    """
    
    def get_name(self) -> str:
        return "Sereiano"
    
    def get_description(self) -> str:
        return ("Híbridos de humanos e criaturas marinhas. Possuem "
                "características terrestres e aquáticas, sendo diplomatas naturais.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CHARISMA, 2, "Presença Hipnótica"),
            create_attribute_bonus(AttributeType.WISDOM, 1, "Intuição Aquática"),
            
            RacialTrait(
                name="Respiração Anfíbia",
                description="Pode respirar tanto no ar quanto na água",
                trait_type=TraitType.OTHER,
                value="amphibious"
            ),
            
            RacialTrait(
                name="Velocidade de Natação",
                description="Velocidade de natação de 12m",
                trait_type=TraitType.MOVEMENT,
                value=("swim", 12)
            ),
            
            RacialTrait(
                name="Comunicação Aquática",
                description="Pode falar com criaturas aquáticas inteligentes",
                trait_type=TraitType.SPELL_LIKE,
                value="speak_with_aquatic"
            ),
            
            RacialTrait(
                name="Canto Hipnótico",
                description="Pode usar canto para fascinar criaturas 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="hypnotic_song"
            ),
            
            RacialTrait(
                name="Empatia Aquática",
                description="Pode sentir emoções de criaturas aquáticas num raio de 18m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="aquatic_empathy"
            ),
            
            RacialTrait(
                name="Resistência ao Frio",
                description="Recebe resistência 5 contra dano de frio",
                trait_type=TraitType.RESISTANCE,
                value="cold"
            ),
            
            RacialTrait(
                name="Diplomacia Natural",
                description="Recebe +2 em testes de Diplomacia e Persuasão",
                trait_type=TraitType.SKILL_BONUS,
                value=("Diplomacia", 2)
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Aquático", "Sereiano"]
    
    def apply_to_character(self, character):
        """Aplica características sereiano ao personagem"""
        super().apply_to_character(character)
        
        # Sereianos podem nadar rapidamente
        character.add_feature("Natação Rápida: Nada a 12m sem penalidades")


class DeepSereian(Sereian):
    """
    Sereiano das Profundezas - Adaptado às águas abissais
    
    Uma subraça sereiana que vive nas profundezas oceânicas
    e desenvolveu características mais alienígenas.
    """
    
    def get_name(self) -> str:
        return "Sereiano das Profundezas"
    
    def get_description(self) -> str:
        return ("Adaptados às profundezas oceânicas, desenvolveram "
                "características mais alienígenas e misteriosas.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui alguns traços por adaptações abissais
        traits[0] = create_attribute_bonus(AttributeType.INTELLIGENCE, 2, "Mente Abissal")
        
        # Adiciona visão no escuro
        traits.append(
            RacialTrait(
                name="Visão no Escuro Abissal",
                description="Pode ver no escuro até 36m",
                trait_type=TraitType.SPECIAL_SENSE,
                value="darkvision_superior"
            )
        )
        
        # Adiciona bioluminescência
        traits.append(
            RacialTrait(
                name="Bioluminescência",
                description="Pode emitir luz como uma tocha 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="bioluminescence"
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Aquático", "Sereiano", "Abissal"]


class StormSereian(Sereian):
    """
    Sereiano da Tempestade - Conectado à Storm aquática
    
    Sereianos que foram tocados pela Storm em sua forma aquática,
    desenvolvendo poderes relacionados a tempestades marinhas.
    """
    
    def get_name(self) -> str:
        return "Sereiano da Tempestade"
    
    def get_description(self) -> str:
        return ("Tocados pela Storm aquática, desenvolveram poderes "
                "relacionados a tempestades marinhas.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Substitui canto hipnótico por poderes da tempestade
        traits[5] = RacialTrait(
            name="Chamado da Tempestade",
            description="Pode invocar uma pequena tempestade aquática 1x por dia",
            trait_type=TraitType.SPELL_LIKE,
            value="storm_call"
        )
        
        # Adiciona resistência elétrica
        traits.append(
            RacialTrait(
                name="Resistência Elétrica",
                description="Recebe resistência 5 contra dano elétrico",
                trait_type=TraitType.RESISTANCE,
                value="electricity"
            )
        )
        
        # Adiciona controle de marés
        traits.append(
            RacialTrait(
                name="Controle de Marés",
                description="Pode manipular pequenas quantidades de água 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="control_water"
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Aquático", "Sereiano", "Primordial"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Raça Sereiana ===")
    
    sereian = Sereian()
    print(sereian.get_summary())
    
    print(f"\n" + "="*50)
    
    deep_sereian = DeepSereian()
    print(deep_sereian.get_summary())
    
    print(f"\n" + "="*50)
    
    storm_sereian = StormSereian()
    print(storm_sereian.get_summary())
