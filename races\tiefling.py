"""
Storm RPG - <PERSON><PERSON>ling da Tempestade
Marcados pela energia caótica da Storm
"""

from typing import List
from .base_race import Race, RacialTrait, TraitType, create_attribute_bonus, create_resistance
from core.attributes import AttributeType


class StormTiefling(Race):
    """
    Tieflings da Tempestade (Filhos da Storm)
    
    Os tieflings da tempestade são descendentes de humanos que foram
    marcados pela energia caótica da Storm. Possuem características
    demoníacas ou elementais e uma conexão natural com a magia caótica.
    """
    
    def get_name(self) -> str:
        return "Tiefling da Tempestade"
    
    def get_description(self) -> str:
        return ("Descendentes marcados pela Storm caótica. Possuem "
                "características sobrenaturais e conexão com magia caótica.")
    
    def get_size(self) -> str:
        return "Médio"
    
    def get_base_speed(self) -> int:
        return 9
    
    def get_racial_traits(self) -> List[RacialTrait]:
        return [
            create_attribute_bonus(AttributeType.CHARISMA, 2, "Presen<PERSON> da Storm"),
            create_attribute_bonus(AttributeType.INTELLIGENCE, 1, "Mente Caótica"),
            
            RacialTrait(
                name="Visão no Escuro",
                description="Pode ver no escuro até 18m como se fosse penumbra",
                trait_type=TraitType.SPECIAL_SENSE,
                value=18
            ),
            
            RacialTrait(
                name="Resistência Elemental",
                description="Escolha resistência 5 contra fogo, eletricidade ou ácido",
                trait_type=TraitType.RESISTANCE,
                value="elemental_choice"
            ),
            
            RacialTrait(
                name="Magia Inata - Chama da Tempestade",
                description="Pode conjurar Chama da Tempestade 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="storm_flame"
            ),
            
            RacialTrait(
                name="Marca da Storm",
                description="Possui marcas visíveis da Storm (chifres, cauda, olhos brilhantes, etc.)",
                trait_type=TraitType.OTHER,
                value="storm_mark"
            ),
            
            RacialTrait(
                name="Afinidade Caótica",
                description="Recebe +1 no nível de conjurador para magias caóticas",
                trait_type=TraitType.OTHER,
                value="chaos_affinity"
            ),
            
            RacialTrait(
                name="Resistência a Charme",
                description="Recebe +2 em testes de resistência contra charme e compulsão",
                trait_type=TraitType.RESISTANCE,
                value="charm_compulsion"
            )
        ]
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Infernal", "Primordial"]
    
    def apply_to_character(self, character):
        """Aplica características tiefling ao personagem"""
        super().apply_to_character(character)
        
        # Tieflings ganham pontos de mana extras para magia inata
        character.max_mana_points += 1
        character.mana_points = character.max_mana_points
    
    def choose_elemental_resistance(self, character, element: str):
        """Permite escolher a resistência elemental"""
        valid_elements = ["fogo", "eletricidade", "ácido"]
        if element.lower() in valid_elements:
            character.add_feature(f"Resistência Elemental: Resistência 5 contra {element}")


class InfernalTiefling(StormTiefling):
    """
    Tiefling Infernal - Descendência demoníaca
    
    Tieflings com ancestralidade mais claramente demoníaca,
    possuindo características infernais pronunciadas.
    """
    
    def get_name(self) -> str:
        return "Tiefling Infernal"
    
    def get_description(self) -> str:
        return ("Descendência demoníaca pronunciada, com características "
                "infernais e poderes relacionados ao fogo.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Fixa resistência ao fogo
        traits[3] = create_resistance("fogo")
        
        # Substitui magia por poder infernal
        traits[4] = RacialTrait(
            name="Chamas Infernais",
            description="Pode conjurar Mãos Flamejantes 1x por dia",
            trait_type=TraitType.SPELL_LIKE,
            value="burning_hands"
        )
        
        # Adiciona intimidação natural
        traits.append(
            RacialTrait(
                name="Presença Intimidante",
                description="Recebe +2 em testes de Intimidação",
                trait_type=TraitType.SKILL_BONUS,
                value=("Intimidação", 2)
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Infernal"]


class CelestialTiefling(StormTiefling):
    """
    Tiefling Celestial - Toque angelical
    
    Tieflings que foram tocados por energia celestial,
    balanceando sua natureza caótica com aspectos divinos.
    """
    
    def get_name(self) -> str:
        return "Tiefling Celestial"
    
    def get_description(self) -> str:
        return ("Tocados por energia celestial, balanceiam natureza "
                "caótica com aspectos divinos e luminosos.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Muda atributos
        traits[0] = create_attribute_bonus(AttributeType.WISDOM, 2, "Sabedoria Celestial")
        
        # Substitui resistência por luz
        traits[3] = RacialTrait(
            name="Resistência Radiante",
            description="Recebe resistência 5 contra dano radiante",
            trait_type=TraitType.RESISTANCE,
            value="radiant"
        )
        
        # Substitui magia por poder celestial
        traits[4] = RacialTrait(
            name="Luz Celestial",
            description="Pode conjurar Luz 1x por dia",
            trait_type=TraitType.SPELL_LIKE,
            value="light"
        )
        
        # Adiciona cura menor
        traits.append(
            RacialTrait(
                name="Toque Curativo",
                description="Pode curar 1 HP em si mesmo ou aliado 1x por dia",
                trait_type=TraitType.SPELL_LIKE,
                value="healing_touch"
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Celestial", "Primordial"]


class FeralTiefling(StormTiefling):
    """
    Tiefling Feral - Aspectos bestiais
    
    Tieflings que desenvolveram características mais bestiais
    e primitivas devido à influência da Storm selvagem.
    """
    
    def get_name(self) -> str:
        return "Tiefling Feral"
    
    def get_description(self) -> str:
        return ("Desenvolveram características bestiais devido à "
                "influência da Storm selvagem.")
    
    def get_racial_traits(self) -> List[RacialTrait]:
        traits = super().get_racial_traits()
        
        # Muda atributos para físicos
        traits[0] = create_attribute_bonus(AttributeType.DEXTERITY, 2, "Agilidade Feral")
        traits[1] = create_attribute_bonus(AttributeType.CONSTITUTION, 1, "Resistência Bestial")
        
        # Adiciona garras
        traits.append(
            RacialTrait(
                name="Garras",
                description="Armas naturais que causam 1d4 de dano cortante",
                trait_type=TraitType.NATURAL_WEAPON,
                value={"damage": "1d4", "type": "cortante"}
            )
        )
        
        # Adiciona faro
        traits.append(
            RacialTrait(
                name="Faro",
                description="Pode rastrear por cheiro como um animal",
                trait_type=TraitType.SPECIAL_SENSE,
                value="scent"
            )
        )
        
        return traits
    
    def get_languages(self) -> List[str]:
        return ["Comum", "Silvestre"]


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste da Raça Tiefling ===")
    
    storm_tiefling = StormTiefling()
    print(storm_tiefling.get_summary())
    
    print(f"\n" + "="*50)
    
    infernal_tiefling = InfernalTiefling()
    print(infernal_tiefling.get_summary())
    
    print(f"\n" + "="*50)
    
    celestial_tiefling = CelestialTiefling()
    print(celestial_tiefling.get_summary())
    
    print(f"\n" + "="*50)
    
    feral_tiefling = FeralTiefling()
    print(feral_tiefling.get_summary())
