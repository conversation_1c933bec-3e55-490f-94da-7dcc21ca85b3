// Storm RPG - Dice System Library Implementation

#include "Dice/StormDiceLibrary.h"
#include "Engine/Engine.h"
#include "HAL/UnrealMemory.h"

// ========================================
// BASIC DICE FUNCTIONS
// ========================================

int32 UStormDiceLibrary::RollDie(int32 Sides)
{
    if (Sides <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("StormDice: Invalid dice sides (%d), defaulting to d20"), Sides);
        Sides = 20;
    }
    
    return FMath::RandRange(1, Sides);
}

int32 UStormDiceLibrary::RollMultipleDice(int32 NumDice, int32 Sides)
{
    if (NumDice <= 0 || Sides <= 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("StormDice: Invalid parameters (NumDice: %d, Sides: %d)"), NumDice, Sides);
        return 0;
    }

    int32 Total = 0;
    for (int32 i = 0; i < NumDice; ++i)
    {
        Total += RollDie(Sides);
    }
    
    return Total;
}

int32 UStormDiceLibrary::RollWithModifier(int32 NumDice, int32 Sides, int32 Modifier)
{
    return RollMultipleDice(NumDice, Sides) + Modifier;
}

// ========================================
// D20 SYSTEM FUNCTIONS
// ========================================

FStormDiceResult UStormDiceLibrary::RollD20(int32 Modifier, bool bAdvantage, bool bDisadvantage)
{
    return ApplyAdvantageDisadvantage(Modifier, bAdvantage, bDisadvantage);
}

FStormDiceResult UStormDiceLibrary::RollAttack(int32 AttackBonus, int32 TargetAC, bool bAdvantage, bool bDisadvantage)
{
    FStormDiceResult Result = RollD20(AttackBonus, bAdvantage, bDisadvantage);
    Result.Expression = FString::Printf(TEXT("Attack: 1d20+%d vs AC %d"), AttackBonus, TargetAC);
    
    // Log attack result
    bool bHit = CheckSuccess(Result, TargetAC);
    UE_LOG(LogTemp, Log, TEXT("Storm Attack: %s = %d (%s)"), 
           *Result.Expression, Result.Total, bHit ? TEXT("HIT") : TEXT("MISS"));
    
    return Result;
}

FStormDiceResult UStormDiceLibrary::RollSavingThrow(int32 SaveBonus, int32 DifficultyClass, bool bAdvantage, bool bDisadvantage)
{
    FStormDiceResult Result = RollD20(SaveBonus, bAdvantage, bDisadvantage);
    Result.Expression = FString::Printf(TEXT("Save: 1d20+%d vs DC %d"), SaveBonus, DifficultyClass);
    
    bool bSuccess = CheckSuccess(Result, DifficultyClass);
    UE_LOG(LogTemp, Log, TEXT("Storm Save: %s = %d (%s)"), 
           *Result.Expression, Result.Total, bSuccess ? TEXT("SUCCESS") : TEXT("FAILURE"));
    
    return Result;
}

FStormDiceResult UStormDiceLibrary::RollSkillCheck(int32 SkillBonus, int32 DifficultyClass, bool bAdvantage, bool bDisadvantage)
{
    FStormDiceResult Result = RollD20(SkillBonus, bAdvantage, bDisadvantage);
    Result.Expression = FString::Printf(TEXT("Skill: 1d20+%d vs DC %d"), SkillBonus, DifficultyClass);
    
    return Result;
}

// ========================================
// EXPRESSION PARSING
// ========================================

FStormDiceResult UStormDiceLibrary::RollExpression(const FString& Expression)
{
    FStormDiceExpression ParsedExpression = ParseDiceExpression(Expression);
    return RollDiceExpression(ParsedExpression);
}

FStormDiceExpression UStormDiceLibrary::ParseDiceExpression(const FString& Expression)
{
    FStormDiceExpression Result;
    
    // Clean the expression
    FString CleanExpression = Expression.ToLower().Replace(TEXT(" "), TEXT(""));
    
    int32 NumDice, Sides, Modifier;
    if (ParseDicePattern(CleanExpression, NumDice, Sides, Modifier))
    {
        Result.NumDice = NumDice;
        Result.DiceSides = Sides;
        Result.Modifier = Modifier;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("StormDice: Failed to parse expression '%s', using default 1d20"), *Expression);
        Result = FStormDiceExpression(1, 20, 0);
    }
    
    return Result;
}

FStormDiceResult UStormDiceLibrary::RollDiceExpression(const FStormDiceExpression& Expression)
{
    FStormDiceResult Result;
    
    if (Expression.DiceSides == 20 && Expression.NumDice == 1)
    {
        // Use d20 system for single d20 rolls
        Result = RollD20(Expression.Modifier, Expression.bAdvantage, Expression.bDisadvantage);
    }
    else
    {
        // Use standard dice rolling for other combinations
        int32 DiceTotal = 0;
        
        if (Expression.bExploding)
        {
            for (int32 i = 0; i < Expression.NumDice; ++i)
            {
                DiceTotal += RollExploding(Expression.DiceSides);
            }
        }
        else
        {
            DiceTotal = RollMultipleDice(Expression.NumDice, Expression.DiceSides);
        }
        
        Result.Roll = DiceTotal;
        Result.Modifier = Expression.Modifier;
        Result.Total = DiceTotal + Expression.Modifier;
        Result.bHasAdvantage = Expression.bAdvantage;
        Result.bHasDisadvantage = Expression.bDisadvantage;
    }
    
    Result.Expression = FString::Printf(TEXT("%dd%d%+d"), 
                                       Expression.NumDice, Expression.DiceSides, Expression.Modifier);
    
    return Result;
}

// ========================================
// SPECIAL MECHANICS
// ========================================

int32 UStormDiceLibrary::RollExploding(int32 Sides, int32 MaxExplosions)
{
    int32 Total = 0;
    int32 Explosions = 0;
    
    while (Explosions < MaxExplosions)
    {
        int32 Roll = RollDie(Sides);
        Total += Roll;
        
        if (Roll == Sides)
        {
            Explosions++;
            UE_LOG(LogTemp, Log, TEXT("StormDice: Exploding die! Roll: %d, Total so far: %d"), Roll, Total);
        }
        else
        {
            break;
        }
    }
    
    return Total;
}

FStormDiceResult UStormDiceLibrary::RollWithAdvantage(int32 Modifier)
{
    return RollD20(Modifier, true, false);
}

FStormDiceResult UStormDiceLibrary::RollWithDisadvantage(int32 Modifier)
{
    return RollD20(Modifier, false, true);
}

FStormDiceResult UStormDiceLibrary::RollInitiative(int32 DexterityModifier, int32 LevelBonus)
{
    FStormDiceResult Result = RollD20(DexterityModifier + LevelBonus);
    Result.Expression = FString::Printf(TEXT("Initiative: 1d20+%d+%d"), DexterityModifier, LevelBonus);
    
    UE_LOG(LogTemp, Log, TEXT("Storm Initiative: %s = %d"), *Result.Expression, Result.Total);
    
    return Result;
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

bool UStormDiceLibrary::CheckSuccess(const FStormDiceResult& Result, int32 DifficultyClass)
{
    // Critical hit always succeeds (unless also critical fail)
    if (Result.bIsCriticalHit && !Result.bIsCriticalFail)
    {
        return true;
    }
    
    // Critical fail always fails
    if (Result.bIsCriticalFail)
    {
        return false;
    }
    
    // Normal check
    return Result.Total >= DifficultyClass;
}

float UStormDiceLibrary::GetCriticalMultiplier(const FStormDiceResult& Result, float BaseCritMultiplier)
{
    if (Result.bIsCriticalHit && !Result.bIsCriticalFail)
    {
        return BaseCritMultiplier;
    }
    
    return 1.0f;
}

int32 UStormDiceLibrary::RollPercentile()
{
    return FMath::RandRange(1, 100);
}

bool UStormDiceLibrary::CheckPercentage(float Percentage)
{
    return RollPercentile() <= FMath::RoundToInt(Percentage);
}

// ========================================
// STORM-SPECIFIC FUNCTIONS
// ========================================

FStormDiceResult UStormDiceLibrary::RollStormSurge(int32 CasterLevel)
{
    FStormDiceResult Result = RollD20(CasterLevel);
    Result.Expression = FString::Printf(TEXT("Storm Surge: 1d20+%d"), CasterLevel);
    
    // Storm surge effects based on result
    if (Result.Total >= 25)
    {
        UE_LOG(LogTemp, Log, TEXT("Storm Surge: MAJOR EFFECT! (%d)"), Result.Total);
    }
    else if (Result.Total >= 20)
    {
        UE_LOG(LogTemp, Log, TEXT("Storm Surge: Minor effect (%d)"), Result.Total);
    }
    
    return Result;
}

FStormDiceResult UStormDiceLibrary::RollTempestadoAwakening(int32 CharacterLevel, int32 StormExposure)
{
    int32 TotalBonus = CharacterLevel + StormExposure;
    FStormDiceResult Result = RollD20(TotalBonus);
    Result.Expression = FString::Printf(TEXT("Tempestado Awakening: 1d20+%d+%d"), CharacterLevel, StormExposure);
    
    // Awakening thresholds
    if (Result.Total >= 30)
    {
        UE_LOG(LogTemp, Log, TEXT("Tempestado Awakening: FULL AWAKENING! (%d)"), Result.Total);
    }
    else if (Result.Total >= 25)
    {
        UE_LOG(LogTemp, Log, TEXT("Tempestado Awakening: Partial awakening (%d)"), Result.Total);
    }
    else if (Result.Total >= 20)
    {
        UE_LOG(LogTemp, Log, TEXT("Tempestado Awakening: Storm sensitivity (%d)"), Result.Total);
    }
    
    return Result;
}

int32 UStormDiceLibrary::RollChaosEffect()
{
    return RollDie(100); // d100 for chaos effect table
}

// ========================================
// DEBUGGING & CONSOLE
// ========================================

void UStormDiceLibrary::StormRoll(const FString& Expression)
{
    FStormDiceResult Result = RollExpression(Expression);
    
    FString LogMessage = FString::Printf(TEXT("STORM ROLL: %s → %d"), 
                                        *Expression, Result.Total);
    
    if (Result.bIsCriticalHit)
    {
        LogMessage += TEXT(" (CRITICAL!)");
    }
    else if (Result.bIsCriticalFail)
    {
        LogMessage += TEXT(" (FUMBLE!)");
    }
    
    UE_LOG(LogTemp, Warning, TEXT("%s"), *LogMessage);
    
    // Also display on screen if in game
    if (GEngine)
    {
        GEngine->AddOnScreenDebugMessage(-1, 5.0f, FColor::Yellow, LogMessage);
    }
}

void UStormDiceLibrary::LogDiceResult(const FStormDiceResult& Result, const FString& Context)
{
    FString LogMessage = Context.IsEmpty() ? TEXT("Dice Roll") : Context;
    LogMessage += FString::Printf(TEXT(": %s = %d"), *Result.Expression, Result.Total);
    
    if (Result.bIsCriticalHit)
    {
        LogMessage += TEXT(" (CRITICAL!)");
    }
    else if (Result.bIsCriticalFail)
    {
        LogMessage += TEXT(" (FUMBLE!)");
    }
    
    UE_LOG(LogTemp, Log, TEXT("%s"), *LogMessage);
}

// ========================================
// PRIVATE HELPER FUNCTIONS
// ========================================

int32 UStormDiceLibrary::RollSingleD20()
{
    return FMath::RandRange(1, 20);
}

bool UStormDiceLibrary::ParseDicePattern(const FString& Expression, int32& OutNumDice, int32& OutSides, int32& OutModifier)
{
    // Simple regex-like parsing for patterns like "2d6+3", "1d20-1", "d8"
    
    OutNumDice = 1;
    OutSides = 20;
    OutModifier = 0;
    
    // Find 'd' character
    int32 DIndex = Expression.Find(TEXT("d"));
    if (DIndex == INDEX_NONE)
    {
        return false;
    }
    
    // Parse number of dice (before 'd')
    if (DIndex > 0)
    {
        FString NumDiceStr = Expression.Left(DIndex);
        OutNumDice = FCString::Atoi(*NumDiceStr);
        if (OutNumDice <= 0) OutNumDice = 1;
    }
    
    // Parse sides and modifier (after 'd')
    FString RemainingStr = Expression.Mid(DIndex + 1);
    
    // Find modifier (+ or -)
    int32 ModifierIndex = INDEX_NONE;
    bool bPositiveModifier = true;
    
    int32 PlusIndex = RemainingStr.Find(TEXT("+"));
    int32 MinusIndex = RemainingStr.Find(TEXT("-"));
    
    if (PlusIndex != INDEX_NONE && (MinusIndex == INDEX_NONE || PlusIndex < MinusIndex))
    {
        ModifierIndex = PlusIndex;
        bPositiveModifier = true;
    }
    else if (MinusIndex != INDEX_NONE)
    {
        ModifierIndex = MinusIndex;
        bPositiveModifier = false;
    }
    
    // Parse sides
    FString SidesStr;
    if (ModifierIndex != INDEX_NONE)
    {
        SidesStr = RemainingStr.Left(ModifierIndex);
        
        // Parse modifier
        FString ModifierStr = RemainingStr.Mid(ModifierIndex + 1);
        OutModifier = FCString::Atoi(*ModifierStr);
        if (!bPositiveModifier)
        {
            OutModifier = -OutModifier;
        }
    }
    else
    {
        SidesStr = RemainingStr;
    }
    
    OutSides = FCString::Atoi(*SidesStr);
    if (OutSides <= 0) OutSides = 20;
    
    return true;
}

FStormDiceResult UStormDiceLibrary::ApplyAdvantageDisadvantage(int32 Modifier, bool bAdvantage, bool bDisadvantage)
{
    FStormDiceResult Result;
    
    if (bAdvantage && !bDisadvantage)
    {
        // Advantage: roll twice, take higher
        int32 Roll1 = RollSingleD20();
        int32 Roll2 = RollSingleD20();
        Result.Roll = FMath::Max(Roll1, Roll2);
        Result.bHasAdvantage = true;
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("StormDice Advantage: %d, %d → %d"), Roll1, Roll2, Result.Roll);
    }
    else if (bDisadvantage && !bAdvantage)
    {
        // Disadvantage: roll twice, take lower
        int32 Roll1 = RollSingleD20();
        int32 Roll2 = RollSingleD20();
        Result.Roll = FMath::Min(Roll1, Roll2);
        Result.bHasDisadvantage = true;
        
        UE_LOG(LogTemp, VeryVerbose, TEXT("StormDice Disadvantage: %d, %d → %d"), Roll1, Roll2, Result.Roll);
    }
    else
    {
        // Normal roll (advantage and disadvantage cancel out)
        Result.Roll = RollSingleD20();
    }
    
    Result.Modifier = Modifier;
    Result.Total = Result.Roll + Modifier;
    Result.bIsCriticalHit = (Result.Roll == 20);
    Result.bIsCriticalFail = (Result.Roll == 1);
    
    return Result;
}
