/**
 * Storm RPG - HUD Manager
 * Sistema de interface de usuário dinâmica
 */

import { PlayerManager, PlayerData } from '../core/PlayerManager';
import { stormEvents } from '../utils/EventSystem';
import { UI_CONSTANTS } from '../utils/Constants';

// Importações do Horizon Worlds (simuladas)
declare const Scene: any;
declare const Canvas: any;
declare const UI: any;

export interface HUDElements {
    healthBar: any;
    manaBar: any;
    levelText: any;
    scoreText: any;
    notificationPanel: any;
    minimap: any;
    spellButtons: any[];
    chatPanel: any;
}

export class HUDManager {
    private playerManager: PlayerManager;
    private hudElements: Map<string, HUDElements> = new Map();
    private globalElements: any = {};

    constructor(playerManager: PlayerManager) {
        this.playerManager = playerManager;
        this.setupHUDEvents();
        this.createGlobalUI();
    }

    /**
     * Configura eventos da HUD
     */
    private setupHUDEvents(): void {
        // Atualizar HUD quando jogador entra
        stormEvents.onPlayerJoined((playerId, playerName) => {
            this.createPlayerHUD(playerId);
        });

        // Atualizar HUD quando há dano
        stormEvents.onDamageDealt((attacker, target, damage) => {
            this.updateHealthBar(target);
            this.showDamageNumber(target, damage);
        });

        // Atualizar HUD quando há cura
        stormEvents.eventSystem.on('healing_applied', (data) => {
            this.updateHealthBar(data.targetId);
            this.showHealingNumber(data.targetId, data.healing);
        });

        // Atualizar HUD quando ganha XP
        stormEvents.eventSystem.on('player_level_up', (data) => {
            this.updateLevelDisplay(data.playerId);
            this.showLevelUpEffect(data.playerId);
        });

        // Atualizar score
        stormEvents.onItemPickedUp((playerId, itemType, value) => {
            this.updateScoreDisplay(playerId);
            this.showNotification(playerId, `+${value} pontos!`);
        });
    }

    /**
     * Cria elementos globais da UI
     */
    private createGlobalUI(): void {
        try {
            // Criar canvas principal
            const mainCanvas = Scene.createRootEntity();
            mainCanvas.name = "MainCanvas";

            const canvasComponent = mainCanvas.createComponent("engine:canvas", {
                renderMode: "screenSpace",
                sortingOrder: 100
            });

            // Placar global
            this.createScoreboard(mainCanvas);

            // Chat global
            this.createChatPanel(mainCanvas);

            // Minimapa
            this.createMinimap(mainCanvas);

            this.globalElements.mainCanvas = mainCanvas;

        } catch (error) {
            console.error("Erro ao criar UI global:", error);
        }
    }

    /**
     * Cria HUD específica do jogador
     */
    createPlayerHUD(playerId: string): void {
        const playerData = this.playerManager.getPlayerData(playerId);
        if (!playerData) return;

        try {
            // Criar canvas do jogador
            const playerCanvas = Scene.createRootEntity();
            playerCanvas.name = `PlayerHUD_${playerId}`;

            const canvasComponent = playerCanvas.createComponent("engine:canvas", {
                renderMode: "screenSpace",
                sortingOrder: 50
            });

            // Criar elementos da HUD
            const hudElements: HUDElements = {
                healthBar: this.createHealthBar(playerCanvas, playerId),
                manaBar: this.createManaBar(playerCanvas, playerId),
                levelText: this.createLevelText(playerCanvas, playerId),
                scoreText: this.createScoreText(playerCanvas, playerId),
                notificationPanel: this.createNotificationPanel(playerCanvas),
                minimap: null, // Minimapa é global
                spellButtons: this.createSpellButtons(playerCanvas, playerId),
                chatPanel: null // Chat é global
            };

            this.hudElements.set(playerId, hudElements);

            // Atualizar valores iniciais
            this.updateAllHUDElements(playerId);

        } catch (error) {
            console.error(`Erro ao criar HUD do jogador ${playerId}:`, error);
        }
    }

    /**
     * Cria barra de vida
     */
    private createHealthBar(parent: any, playerId: string): any {
        try {
            const healthBarBG = Scene.createRootEntity();
            healthBarBG.setParent(parent);
            healthBarBG.name = `HealthBarBG_${playerId}`;

            // Posicionar barra de vida
            const rectTransform = healthBarBG.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.05, y: 0.9 },
                anchorMax: { x: 0.35, y: 0.95 },
                offsetMin: { x: 0, y: 0 },
                offsetMax: { x: 0, y: 0 }
            });

            // Background da barra
            const bgImage = healthBarBG.createComponent("engine:image", {
                color: { r: 0.2, g: 0.2, b: 0.2, a: 0.8 }
            });

            // Barra de vida (fill)
            const healthFill = Scene.createRootEntity();
            healthFill.setParent(healthBarBG);
            healthFill.name = `HealthFill_${playerId}`;

            const fillTransform = healthFill.createComponent("engine:rectTransform", {
                anchorMin: { x: 0, y: 0 },
                anchorMax: { x: 1, y: 1 },
                offsetMin: { x: 2, y: 2 },
                offsetMax: { x: -2, y: -2 }
            });

            const fillImage = healthFill.createComponent("engine:image", {
                color: UI_CONSTANTS.COLORS.DANGER,
                fillMethod: "horizontal"
            });

            // Texto da vida
            const healthText = Scene.createRootEntity();
            healthText.setParent(healthBarBG);

            const textTransform = healthText.createComponent("engine:rectTransform", {
                anchorMin: { x: 0, y: 0 },
                anchorMax: { x: 1, y: 1 }
            });

            const textComponent = healthText.createComponent("engine:text", {
                text: "100/100",
                fontSize: 14,
                color: { r: 1, g: 1, b: 1, a: 1 },
                alignment: "center"
            });

            return { background: healthBarBG, fill: healthFill, text: healthText };

        } catch (error) {
            console.error("Erro ao criar barra de vida:", error);
            return null;
        }
    }

    /**
     * Cria barra de mana
     */
    private createManaBar(parent: any, playerId: string): any {
        try {
            const manaBarBG = Scene.createRootEntity();
            manaBarBG.setParent(parent);
            manaBarBG.name = `ManaBarBG_${playerId}`;

            // Posicionar barra de mana (abaixo da vida)
            const rectTransform = manaBarBG.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.05, y: 0.85 },
                anchorMax: { x: 0.35, y: 0.88 },
                offsetMin: { x: 0, y: 0 },
                offsetMax: { x: 0, y: 0 }
            });

            // Background da barra
            const bgImage = manaBarBG.createComponent("engine:image", {
                color: { r: 0.1, g: 0.1, b: 0.3, a: 0.8 }
            });

            // Barra de mana (fill)
            const manaFill = Scene.createRootEntity();
            manaFill.setParent(manaBarBG);

            const fillTransform = manaFill.createComponent("engine:rectTransform", {
                anchorMin: { x: 0, y: 0 },
                anchorMax: { x: 1, y: 1 },
                offsetMin: { x: 2, y: 2 },
                offsetMax: { x: -2, y: -2 }
            });

            const fillImage = manaFill.createComponent("engine:image", {
                color: UI_CONSTANTS.COLORS.PRIMARY,
                fillMethod: "horizontal"
            });

            // Texto da mana
            const manaText = Scene.createRootEntity();
            manaText.setParent(manaBarBG);

            const textComponent = manaText.createComponent("engine:text", {
                text: "50/50",
                fontSize: 12,
                color: { r: 1, g: 1, b: 1, a: 1 },
                alignment: "center"
            });

            return { background: manaBarBG, fill: manaFill, text: manaText };

        } catch (error) {
            console.error("Erro ao criar barra de mana:", error);
            return null;
        }
    }

    /**
     * Cria texto de nível
     */
    private createLevelText(parent: any, playerId: string): any {
        try {
            const levelEntity = Scene.createRootEntity();
            levelEntity.setParent(parent);

            const rectTransform = levelEntity.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.05, y: 0.8 },
                anchorMax: { x: 0.2, y: 0.84 }
            });

            const textComponent = levelEntity.createComponent("engine:text", {
                text: "Nível 1",
                fontSize: 16,
                color: UI_CONSTANTS.COLORS.SUCCESS,
                alignment: "left"
            });

            return levelEntity;

        } catch (error) {
            console.error("Erro ao criar texto de nível:", error);
            return null;
        }
    }

    /**
     * Cria texto de pontuação
     */
    private createScoreText(parent: any, playerId: string): any {
        try {
            const scoreEntity = Scene.createRootEntity();
            scoreEntity.setParent(parent);

            const rectTransform = scoreEntity.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.7, y: 0.9 },
                anchorMax: { x: 0.95, y: 0.95 }
            });

            const textComponent = scoreEntity.createComponent("engine:text", {
                text: "Pontos: 0",
                fontSize: 16,
                color: UI_CONSTANTS.COLORS.WARNING,
                alignment: "right"
            });

            return scoreEntity;

        } catch (error) {
            console.error("Erro ao criar texto de pontuação:", error);
            return null;
        }
    }

    /**
     * Cria painel de notificações
     */
    private createNotificationPanel(parent: any): any {
        try {
            const notificationPanel = Scene.createRootEntity();
            notificationPanel.setParent(parent);

            const rectTransform = notificationPanel.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.3, y: 0.7 },
                anchorMax: { x: 0.7, y: 0.8 }
            });

            const textComponent = notificationPanel.createComponent("engine:text", {
                text: "",
                fontSize: 18,
                color: { r: 1, g: 1, b: 0, a: 1 },
                alignment: "center"
            });

            // Inicialmente invisível
            notificationPanel.setActive(false);

            return notificationPanel;

        } catch (error) {
            console.error("Erro ao criar painel de notificações:", error);
            return null;
        }
    }

    /**
     * Cria botões de magias
     */
    private createSpellButtons(parent: any, playerId: string): any[] {
        const buttons: any[] = [];
        const spellNames = ["Fireball", "Heal", "Storm Bolt"];

        spellNames.forEach((spellName, index) => {
            try {
                const button = Scene.createRootEntity();
                button.setParent(parent);

                const rectTransform = button.createComponent("engine:rectTransform", {
                    anchorMin: { x: 0.05 + (index * 0.08), y: 0.05 },
                    anchorMax: { x: 0.12 + (index * 0.08), y: 0.12 }
                });

                const buttonComponent = button.createComponent("engine:button", {
                    normalColor: UI_CONSTANTS.COLORS.PRIMARY,
                    highlightedColor: UI_CONSTANTS.COLORS.SECONDARY,
                    pressedColor: UI_CONSTANTS.COLORS.STORM
                });

                const textComponent = button.createComponent("engine:text", {
                    text: spellName.charAt(0), // Primeira letra
                    fontSize: 16,
                    color: { r: 1, g: 1, b: 1, a: 1 },
                    alignment: "center"
                });

                // Configurar evento de clique
                buttonComponent.onClick.add(() => {
                    this.onSpellButtonClicked(playerId, spellName);
                });

                buttons.push(button);

            } catch (error) {
                console.error(`Erro ao criar botão de magia ${spellName}:`, error);
            }
        });

        return buttons;
    }

    /**
     * Cria placar global
     */
    private createScoreboard(parent: any): void {
        try {
            const scoreboard = Scene.createRootEntity();
            scoreboard.setParent(parent);
            scoreboard.name = "Scoreboard";

            const rectTransform = scoreboard.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.75, y: 0.5 },
                anchorMax: { x: 0.98, y: 0.9 }
            });

            const bgImage = scoreboard.createComponent("engine:image", {
                color: { r: 0, g: 0, b: 0, a: 0.7 }
            });

            // Título
            const title = Scene.createRootEntity();
            title.setParent(scoreboard);

            const titleText = title.createComponent("engine:text", {
                text: "PLACAR",
                fontSize: 18,
                color: UI_CONSTANTS.COLORS.STORM,
                alignment: "center"
            });

            this.globalElements.scoreboard = scoreboard;

        } catch (error) {
            console.error("Erro ao criar placar:", error);
        }
    }

    /**
     * Cria painel de chat
     */
    private createChatPanel(parent: any): void {
        try {
            const chatPanel = Scene.createRootEntity();
            chatPanel.setParent(parent);
            chatPanel.name = "ChatPanel";

            const rectTransform = chatPanel.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.02, y: 0.02 },
                anchorMax: { x: 0.4, y: 0.3 }
            });

            const bgImage = chatPanel.createComponent("engine:image", {
                color: { r: 0, g: 0, b: 0, a: 0.5 }
            });

            this.globalElements.chatPanel = chatPanel;

        } catch (error) {
            console.error("Erro ao criar painel de chat:", error);
        }
    }

    /**
     * Cria minimapa
     */
    private createMinimap(parent: any): void {
        try {
            const minimap = Scene.createRootEntity();
            minimap.setParent(parent);
            minimap.name = "Minimap";

            const rectTransform = minimap.createComponent("engine:rectTransform", {
                anchorMin: { x: 0.8, y: 0.8 },
                anchorMax: { x: 0.98, y: 0.98 }
            });

            const bgImage = minimap.createComponent("engine:image", {
                color: { r: 0.1, g: 0.1, b: 0.1, a: 0.8 }
            });

            this.globalElements.minimap = minimap;

        } catch (error) {
            console.error("Erro ao criar minimapa:", error);
        }
    }

    /**
     * Atualiza barra de vida
     */
    updateHealthBar(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        const playerStats = this.playerManager.getPlayerStats(playerId);
        
        if (!hudElements || !playerStats) return;

        try {
            const healthBar = hudElements.healthBar;
            const currentHP = playerStats.derivedStats.hitPoints;
            const maxHP = playerStats.derivedStats.maxHitPoints;
            const percentage = currentHP / maxHP;

            // Atualizar fill da barra
            const fillImage = healthBar.fill.getComponent("engine:image");
            fillImage.fillAmount = percentage;

            // Atualizar texto
            const textComponent = healthBar.text.getComponent("engine:text");
            textComponent.text = `${currentHP}/${maxHP}`;

            // Mudar cor baseada na porcentagem
            if (percentage > 0.6) {
                fillImage.color = { r: 0.2, g: 0.8, b: 0.2, a: 1 }; // Verde
            } else if (percentage > 0.3) {
                fillImage.color = { r: 0.8, g: 0.8, b: 0.2, a: 1 }; // Amarelo
            } else {
                fillImage.color = { r: 0.8, g: 0.2, b: 0.2, a: 1 }; // Vermelho
            }

        } catch (error) {
            console.error("Erro ao atualizar barra de vida:", error);
        }
    }

    /**
     * Atualiza barra de mana
     */
    updateManaBar(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        const playerStats = this.playerManager.getPlayerStats(playerId);
        
        if (!hudElements || !playerStats) return;

        try {
            const manaBar = hudElements.manaBar;
            const currentMP = playerStats.derivedStats.manaPoints;
            const maxMP = playerStats.derivedStats.maxManaPoints;
            const percentage = maxMP > 0 ? currentMP / maxMP : 0;

            // Atualizar fill da barra
            const fillImage = manaBar.fill.getComponent("engine:image");
            fillImage.fillAmount = percentage;

            // Atualizar texto
            const textComponent = manaBar.text.getComponent("engine:text");
            textComponent.text = `${currentMP}/${maxMP}`;

        } catch (error) {
            console.error("Erro ao atualizar barra de mana:", error);
        }
    }

    /**
     * Atualiza display de nível
     */
    updateLevelDisplay(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        const playerStats = this.playerManager.getPlayerStats(playerId);
        
        if (!hudElements || !playerStats) return;

        try {
            const levelText = hudElements.levelText.getComponent("engine:text");
            levelText.text = `Nível ${playerStats.level}`;

        } catch (error) {
            console.error("Erro ao atualizar display de nível:", error);
        }
    }

    /**
     * Atualiza display de pontuação
     */
    updateScoreDisplay(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        const playerData = this.playerManager.getPlayerData(playerId);
        
        if (!hudElements || !playerData) return;

        try {
            const scoreText = hudElements.scoreText.getComponent("engine:text");
            scoreText.text = `Pontos: ${playerData.score}`;

        } catch (error) {
            console.error("Erro ao atualizar display de pontuação:", error);
        }
    }

    /**
     * Mostra notificação
     */
    showNotification(playerId: string, message: string, duration: number = 3000): void {
        const hudElements = this.hudElements.get(playerId);
        if (!hudElements) return;

        try {
            const notification = hudElements.notificationPanel;
            const textComponent = notification.getComponent("engine:text");
            
            textComponent.text = message;
            notification.setActive(true);

            // Esconder após duração
            setTimeout(() => {
                notification.setActive(false);
            }, duration);

        } catch (error) {
            console.error("Erro ao mostrar notificação:", error);
        }
    }

    /**
     * Mostra número de dano
     */
    showDamageNumber(playerId: string, damage: number): void {
        // TODO: Implementar números flutuantes de dano
        console.log(`-${damage} HP`);
    }

    /**
     * Mostra número de cura
     */
    showHealingNumber(playerId: string, healing: number): void {
        // TODO: Implementar números flutuantes de cura
        console.log(`+${healing} HP`);
    }

    /**
     * Mostra efeito de level up
     */
    showLevelUpEffect(playerId: string): void {
        this.showNotification(playerId, "LEVEL UP!", 5000);
        // TODO: Adicionar efeitos visuais especiais
    }

    /**
     * Evento de clique em botão de magia
     */
    private onSpellButtonClicked(playerId: string, spellName: string): void {
        console.log(`Jogador ${playerId} clicou na magia ${spellName}`);
        
        // Emitir evento para sistema de magias
        stormEvents.eventSystem.emit('spell_button_clicked', {
            playerId,
            spellName
        });
    }

    /**
     * Atualiza todos os elementos da HUD
     */
    updateAllHUDElements(playerId: string): void {
        this.updateHealthBar(playerId);
        this.updateManaBar(playerId);
        this.updateLevelDisplay(playerId);
        this.updateScoreDisplay(playerId);
    }

    /**
     * Remove HUD do jogador
     */
    removePlayerHUD(playerId: string): void {
        const hudElements = this.hudElements.get(playerId);
        if (hudElements) {
            try {
                // Destruir elementos da HUD
                Object.values(hudElements).forEach(element => {
                    if (element && element.destroy) {
                        element.destroy();
                    }
                });
                
                this.hudElements.delete(playerId);
            } catch (error) {
                console.error(`Erro ao remover HUD do jogador ${playerId}:`, error);
            }
        }
    }
}
