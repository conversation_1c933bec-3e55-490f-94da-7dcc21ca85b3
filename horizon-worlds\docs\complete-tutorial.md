# 🌍 **Tutorial Completo: Criando um RPG de Tormenta 20 no Meta Horizon Worlds**

> **Guia definitivo para criar mundos de RPG imersivos baseados em Arton, com NPCs interativos e mecânicas d20 em TypeScript**

---

## 🎯 **Objetivo do Tutorial**

Criar um mundo chamado **"Arton: O Despertar da Tempestade"**, onde jogadores:
- ✅ Exploram regiões famosas de Arton (Khalmyr, Tollon, Montanhas Uivantes)
- ✅ Interagem com NPCs lendários (Lisa<PERSON>, <PERSON><PERSON>, Beluhga, Thelos)
- ✅ Vivem aventuras com mecânicas d20 autênticas
- ✅ Podem remixar o mundo para suas próprias campanhas

**Tempo estimado:** 4-6 horas  
**Nível:** Intermediário a Avançado  
**Resultado:** Mundo completo e remixável para a competição

---

## 📋 **Pré-requisitos**

| Item | Status | Link |
|------|--------|------|
| Conta no **Meta Horizon Worlds** | ⚠️ Obrigatório | [horizon.meta.com](https://horizon.meta.com) |
| **Meta Quest Link** (editor desktop) | ⚠️ Obrigatório | Instalado no PC |
| Membro do **MHCP** (Creator Program) | 🔄 Recomendado | [creator.meta.com](https://creator.meta.com) |
| Conhecimento básico de **TypeScript** | 🔄 Recomendado | [Tutorial TS](https://www.typescriptlang.org/docs/) |
| Acesso ao **Storm RPG** (base) | 🔄 Opcional | [Link do mundo](#) |

---

## 🧱 **Fase 1: Configuração Inicial (30 min)**

### Passo 1.1: Criar o Mundo Base

1. **Abra o Meta Quest Link** no seu PC
2. No **Library**, clique em **Meta Horizon**
3. Clique em **Start in Desktop Mode**
4. Na tela inicial: **Creation Home > Create New World**
5. Configure:
   - **Nome:** `Arton: O Despertar da Tempestade`
   - **Template:** `Blank World`
   - **Visibilidade:** `Public`
   - ✅ **Marque:** "Make this world remixable"
6. Clique em **Create**

### Passo 1.2: Estrutura de Pastas

Organize seus scripts seguindo esta estrutura:

```
/scripts
├── core/
│   ├── GameManager.ts          # Controlador principal
│   ├── PlayerManager.ts        # Gerenciamento de jogadores
│   └── WorldManager.ts         # Gerenciamento do mundo
├── npc/
│   ├── NPCBase.ts              # Classe base para NPCs
│   ├── NPCSpawner.ts           # Sistema de spawn
│   └── DialogueSystem.ts       # Sistema de diálogos
├── zones/
│   ├── ZoneManager.ts          # Gerenciador de zonas
│   ├── Khalmyr.ts              # Cidade de Khalmyr
│   ├── Tollon.ts               # Floresta de Tollon
│   └── MontanhasUivantes.ts    # Montanhas Uivantes
├── quests/
│   ├── QuestSystem.ts          # Sistema de missões
│   └── QuestData.ts            # Dados das missões
├── spells/
│   ├── SpellSystem.ts          # Sistema de magias
│   └── ArtonSpells.ts          # Magias específicas de Arton
├── ui/
│   ├── UIManager.ts            # Interface principal
│   └── HUDSystem.ts            # HUD do jogador
└── utils/
    ├── Constants.ts            # Constantes do jogo
    ├── DiceSystem.ts           # Sistema d20
    └── EventSystem.ts          # Sistema de eventos
```

---

## 🗺️ **Fase 2: Geografia de Arton (60 min)**

### Passo 2.1: Planejar as Zonas

Baseado no *Atlas de Arton*, divida o mundo em **6 zonas principais**:

| Zona | Coordenadas | Características | Clima |
|------|-------------|-----------------|-------|
| **Khalmyr** | (0, 0, 0) | Cidade fortificada, templos | Temperado |
| **Floresta de Tollon** | (50, 0, 0) | Árvores gigantes, névoa | Úmido |
| **Pântano dos Juncos** | (-50, 0, 0) | Lodo, criaturas submersas | Pantanoso |
| **Montanhas Uivantes** | (0, 0, 50) | Neve, vento, tempestades | Frio |
| **Deserto de Zalyra** | (0, 0, -50) | Areia, calor, ruínas | Árido |
| **Vale de Drais** | (-25, 0, 25) | Campos férteis, vilarejos | Ameno |

### Passo 2.2: Implementar o ZoneManager

```typescript
// scripts/zones/ZoneManager.ts
export interface ZoneData {
    name: string;
    center: Vector3;
    radius: number;
    climate: string;
    description: string;
    ambientSound?: string;
    lightingColor?: Color;
}

export class ZoneManager {
    private zones: Map<string, ZoneData> = new Map();
    private currentZone: string = "";

    constructor() {
        this.initializeZones();
        this.setupZoneDetection();
    }

    private initializeZones(): void {
        // Khalmyr - Cidade Principal
        this.zones.set("khalmyr", {
            name: "Khalmyr",
            center: new Vector3(0, 0, 0),
            radius: 20,
            climate: "temperado",
            description: "A grande cidade fortificada, coração do reino",
            ambientSound: "city_ambient",
            lightingColor: new Color(1.0, 0.9, 0.8) // Luz dourada
        });

        // Floresta de Tollon
        this.zones.set("tollon", {
            name: "Floresta de Tollon",
            center: new Vector3(50, 0, 0),
            radius: 25,
            climate: "úmido",
            description: "Floresta ancestral protegida pelos druidas",
            ambientSound: "forest_ambient",
            lightingColor: new Color(0.6, 0.9, 0.6) // Luz verde
        });

        // Montanhas Uivantes
        this.zones.set("montanhas", {
            name: "Montanhas Uivantes",
            center: new Vector3(0, 0, 50),
            radius: 30,
            climate: "frio",
            description: "Picos gelados onde dragões fazem seus ninhos",
            ambientSound: "mountain_wind",
            lightingColor: new Color(0.8, 0.8, 1.0) // Luz azulada
        });

        // Adicionar outras zonas...
    }

    public checkPlayerZone(player: Player): void {
        const playerPos = player.getPosition();
        
        for (const [zoneId, zoneData] of this.zones) {
            const distance = Vector3.distance(playerPos, zoneData.center);
            
            if (distance <= zoneData.radius) {
                if (this.currentZone !== zoneId) {
                    this.enterZone(player, zoneId, zoneData);
                }
                return;
            }
        }
        
        // Fora de todas as zonas
        if (this.currentZone !== "") {
            this.exitZone(player);
        }
    }

    private enterZone(player: Player, zoneId: string, zoneData: ZoneData): void {
        this.currentZone = zoneId;
        
        // Mostrar notificação
        this.showZoneNotification(player, zoneData);
        
        // Aplicar efeitos ambientais
        this.applyZoneEffects(zoneData);
        
        console.log(`${player.name} entrou em ${zoneData.name}`);
    }

    private showZoneNotification(player: Player, zoneData: ZoneData): void {
        // Implementar notificação visual
        const notification = Scene.createRootEntity();
        notification.setPosition(player.getPosition().add(new Vector3(0, 2, 0)));
        
        const text = notification.createComponent("engine:text", {
            text: `${zoneData.name}\n${zoneData.description}`,
            fontSize: 0.5,
            color: zoneData.lightingColor || new Color(1, 1, 1),
            billboard: true
        });
        
        // Remover após 5 segundos
        setTimeout(() => notification.destroy(), 5000);
    }

    private applyZoneEffects(zoneData: ZoneData): void {
        // Aplicar som ambiente
        if (zoneData.ambientSound) {
            this.playAmbientSound(zoneData.ambientSound);
        }
        
        // Aplicar iluminação
        if (zoneData.lightingColor) {
            this.setAmbientLighting(zoneData.lightingColor);
        }
    }
}
```

### Passo 2.3: Construir as Zonas Visualmente

1. **Use o Scene Graph** para organizar por zona
2. **Khalmyr:**
   - Adicione modelos de castelo/cidade
   - Use texturas de pedra
   - Adicione torres e muralhas
3. **Floresta de Tollon:**
   - Posicione árvores grandes
   - Adicione névoa com particle effects
   - Use iluminação verde filtrada
4. **Montanhas Uivantes:**
   - Modelos de montanha com neve
   - Particle effects de neve caindo
   - Iluminação azul fria

---

## 👥 **Fase 3: NPCs Interativos (90 min)**

### Passo 3.1: Sistema Base de NPCs

```typescript
// scripts/npc/NPCBase.ts
export interface NPCData {
    name: string;
    title: string;
    description: string;
    zone: string;
    position: Vector3;
    modelId: string;
    dialogues: string[];
    quests?: string[];
    personality: 'friendly' | 'neutral' | 'hostile' | 'mysterious';
}

export class NPCBase {
    protected data: NPCData;
    protected entity: any;
    protected isInteracting: boolean = false;

    constructor(data: NPCData) {
        this.data = data;
        this.createEntity();
        this.setupInteraction();
    }

    protected createEntity(): void {
        this.entity = Scene.createRootEntity();
        this.entity.setPosition(this.data.position.x, this.data.position.y, this.data.position.z);
        this.entity.name = `NPC_${this.data.name}`;

        // Modelo 3D
        const model = this.entity.createComponent("engine:mesh", {
            assetId: this.data.modelId
        });

        // Collider para interação
        const collider = this.entity.createComponent("engine:trigger", {
            shape: "sphere",
            radius: 2.0
        });

        // Nome flutuante
        this.createNameTag();
    }

    protected createNameTag(): void {
        const nameTag = Scene.createRootEntity();
        nameTag.setParent(this.entity);
        nameTag.setPosition(0, 2.5, 0);

        const text = nameTag.createComponent("engine:text", {
            text: `${this.data.name}\n${this.data.title}`,
            fontSize: 0.3,
            color: this.getNameColor(),
            billboard: true,
            backgroundColor: new Color(0, 0, 0, 0.7)
        });
    }

    protected getNameColor(): Color {
        switch (this.data.personality) {
            case 'friendly': return new Color(0.2, 1.0, 0.2); // Verde
            case 'hostile': return new Color(1.0, 0.2, 0.2);  // Vermelho
            case 'mysterious': return new Color(0.8, 0.2, 0.8); // Roxo
            default: return new Color(1.0, 1.0, 0.2); // Amarelo
        }
    }

    protected setupInteraction(): void {
        const trigger = this.entity.getComponent("engine:trigger");
        
        trigger.onTriggerEnter.add((player: Player) => {
            this.onPlayerApproach(player);
        });

        trigger.onTriggerExit.add((player: Player) => {
            this.onPlayerLeave(player);
        });
    }

    protected onPlayerApproach(player: Player): void {
        if (!this.isInteracting) {
            this.showInteractionPrompt(player);
        }
    }

    protected onPlayerLeave(player: Player): void {
        this.hideInteractionPrompt(player);
    }

    public interact(player: Player): void {
        if (this.isInteracting) return;
        
        this.isInteracting = true;
        this.startDialogue(player);
    }

    protected startDialogue(player: Player): void {
        const randomDialogue = this.data.dialogues[
            Math.floor(Math.random() * this.data.dialogues.length)
        ];
        
        this.showDialogue(player, randomDialogue);
    }

    protected showDialogue(player: Player, text: string): void {
        // Implementar sistema de diálogo
        const dialogueBox = Scene.createRootEntity();
        dialogueBox.setPosition(player.getPosition().add(new Vector3(0, 1.5, 2)));

        const background = dialogueBox.createComponent("engine:mesh", {
            assetId: "dialogue_box_model"
        });

        const dialogueText = dialogueBox.createComponent("engine:text", {
            text: `${this.data.name}:\n"${text}"`,
            fontSize: 0.4,
            color: new Color(1, 1, 1),
            width: 4,
            height: 2
        });

        // Remover após 8 segundos
        setTimeout(() => {
            dialogueBox.destroy();
            this.isInteracting = false;
        }, 8000);
    }
}
```

### Passo 3.2: NPCs Específicos de Arton

```typescript
// scripts/npc/ArtonNPCs.ts
import { NPCBase, NPCData } from './NPCBase';

export class Lisandra extends NPCBase {
    constructor() {
        const data: NPCData = {
            name: "Lisandra",
            title: "Druida de Allihanna",
            description: "Protetora da Floresta de Tollon",
            zone: "tollon",
            position: new Vector3(45, 0, 5),
            modelId: "druid_female_model",
            dialogues: [
                "A natureza está em perigo. Você sente isso também?",
                "As árvores sussurram sobre uma escuridão crescente...",
                "Allihanna me mostrou sua chegada em sonhos.",
                "A floresta precisa de heróis como você."
            ],
            quests: ["purificar_floresta", "encontrar_sementes_sagradas"],
            personality: 'friendly'
        };
        super(data);
    }

    protected startDialogue(player: Player): void {
        // Diálogo especial baseado no progresso do jogador
        const playerLevel = this.getPlayerLevel(player);
        
        if (playerLevel < 3) {
            this.showDialogue(player, "Você ainda é jovem, mas vejo potencial em seus olhos.");
        } else if (playerLevel < 7) {
            this.showDialogue(player, "Sua força cresce. Talvez esteja pronto para uma missão importante.");
        } else {
            this.showDialogue(player, "Você se tornou um verdadeiro protetor da natureza!");
        }
    }
}

export class Rodleck extends NPCBase {
    constructor() {
        const data: NPCData = {
            name: "Rodleck",
            title: "Guardião das Catacumbas",
            description: "Protetor dos segredos subterrâneos",
            zone: "khalmyr",
            position: new Vector3(-5, 0, -8),
            modelId: "guardian_male_model",
            dialogues: [
                "As catacumbas guardam segredos que poucos podem compreender.",
                "Você tem coragem para enfrentar os mortos-vivos?",
                "Minha lâmina já cortou mil criaturas das trevas.",
                "Os túmulos antigos não devem ser profanados."
            ],
            quests: ["limpar_catacumbas", "recuperar_reliquia"],
            personality: 'neutral'
        };
        super(data);
    }
}

export class Beluhga extends NPCBase {
    constructor() {
        const data: NPCData = {
            name: "Beluhga",
            title: "Rainha dos Dragões",
            description: "Soberana das Montanhas Uivantes",
            zone: "montanhas",
            position: new Vector3(0, 10, 45),
            modelId: "dragon_queen_model",
            dialogues: [
                "Mortal insignificante, por que ousa me incomodar?",
                "Poucos têm coragem de subir até meu domínio.",
                "Prove seu valor ou seja consumido pelas chamas!",
                "Os dragões governavam antes dos deuses caminharem por Arton."
            ],
            quests: ["desafio_dragao", "recuperar_ovo_perdido"],
            personality: 'hostile'
        };
        super(data);
    }

    protected startDialogue(player: Player): void {
        // Beluhga é mais agressiva
        const playerPower = this.getPlayerPower(player);
        
        if (playerPower < 50) {
            this.showDialogue(player, "Você é fraco demais para estar aqui. SAIA!");
            this.breatheFire(player);
        } else {
            this.showDialogue(player, "Interessante... você tem algum poder. Talvez seja digno de um desafio.");
        }
    }

    private breatheFire(player: Player): void {
        // Efeito visual de fogo
        const fireEffect = Scene.createRootEntity();
        fireEffect.setPosition(player.getPosition());
        
        const particles = fireEffect.createComponent("engine:persistentParticleEffect", {
            assetId: "dragon_fire_effect"
        });
        
        setTimeout(() => fireEffect.destroy(), 3000);
    }
}
```

---

## 🎮 **Fase 4: Sistema de Missões (60 min)**

### Passo 4.1: QuestSystem Base

```typescript
// scripts/quests/QuestSystem.ts
export interface Quest {
    id: string;
    title: string;
    description: string;
    giver: string;
    zone: string;
    objectives: QuestObjective[];
    rewards: QuestReward[];
    level: number;
    status: 'available' | 'active' | 'completed' | 'failed';
}

export interface QuestObjective {
    id: string;
    description: string;
    type: 'kill' | 'collect' | 'talk' | 'reach' | 'survive';
    target: string;
    current: number;
    required: number;
    completed: boolean;
}

export interface QuestReward {
    type: 'xp' | 'item' | 'gold';
    amount: number;
    itemId?: string;
}

export class QuestSystem {
    private activeQuests: Map<string, Quest[]> = new Map(); // playerId -> quests
    private questDatabase: Map<string, Quest> = new Map();

    constructor() {
        this.initializeQuests();
    }

    private initializeQuests(): void {
        // Missão da Lisandra
        this.questDatabase.set("purificar_floresta", {
            id: "purificar_floresta",
            title: "Purificar a Floresta",
            description: "Derrote o Corruptor que está envenenando as raízes sagradas de Tollon.",
            giver: "Lisandra",
            zone: "tollon",
            level: 3,
            status: 'available',
            objectives: [
                {
                    id: "find_corruption",
                    description: "Encontre a fonte da corrupção",
                    type: 'reach',
                    target: "corruption_source",
                    current: 0,
                    required: 1,
                    completed: false
                },
                {
                    id: "defeat_corruptor",
                    description: "Derrote o Corruptor",
                    type: 'kill',
                    target: "corruptor",
                    current: 0,
                    required: 1,
                    completed: false
                }
            ],
            rewards: [
                { type: 'xp', amount: 500 },
                { type: 'item', amount: 1, itemId: "blessing_of_allihanna" }
            ]
        });

        // Missão do Rodleck
        this.questDatabase.set("limpar_catacumbas", {
            id: "limpar_catacumbas",
            title: "Limpar as Catacumbas",
            description: "Elimine os mortos-vivos que infestam as catacumbas de Khalmyr.",
            giver: "Rodleck",
            zone: "khalmyr",
            level: 2,
            status: 'available',
            objectives: [
                {
                    id: "kill_skeletons",
                    description: "Derrote 5 esqueletos",
                    type: 'kill',
                    target: "skeleton",
                    current: 0,
                    required: 5,
                    completed: false
                },
                {
                    id: "kill_zombie",
                    description: "Derrote o zumbi líder",
                    type: 'kill',
                    target: "zombie_leader",
                    current: 0,
                    required: 1,
                    completed: false
                }
            ],
            rewards: [
                { type: 'xp', amount: 300 },
                { type: 'gold', amount: 100 }
            ]
        });
    }

    public giveQuest(playerId: string, questId: string): boolean {
        const quest = this.questDatabase.get(questId);
        if (!quest) return false;

        if (!this.activeQuests.has(playerId)) {
            this.activeQuests.set(playerId, []);
        }

        const playerQuests = this.activeQuests.get(playerId)!;
        
        // Verificar se já tem a quest
        if (playerQuests.find(q => q.id === questId)) {
            return false;
        }

        // Clonar quest e adicionar
        const newQuest = { ...quest, status: 'active' as const };
        playerQuests.push(newQuest);

        this.showQuestNotification(playerId, newQuest);
        return true;
    }

    public updateObjective(playerId: string, objectiveType: string, target: string, amount: number = 1): void {
        const playerQuests = this.activeQuests.get(playerId);
        if (!playerQuests) return;

        playerQuests.forEach(quest => {
            if (quest.status !== 'active') return;

            quest.objectives.forEach(objective => {
                if (objective.type === objectiveType && 
                    objective.target === target && 
                    !objective.completed) {
                    
                    objective.current = Math.min(objective.current + amount, objective.required);
                    
                    if (objective.current >= objective.required) {
                        objective.completed = true;
                        this.checkQuestCompletion(playerId, quest);
                    }
                }
            });
        });
    }

    private checkQuestCompletion(playerId: string, quest: Quest): void {
        const allCompleted = quest.objectives.every(obj => obj.completed);
        
        if (allCompleted) {
            quest.status = 'completed';
            this.giveRewards(playerId, quest.rewards);
            this.showQuestCompletedNotification(playerId, quest);
        }
    }

    private showQuestNotification(playerId: string, quest: Quest): void {
        // Implementar notificação visual de nova quest
        console.log(`Nova missão para ${playerId}: ${quest.title}`);
    }

    private showQuestCompletedNotification(playerId: string, quest: Quest): void {
        // Implementar notificação de quest completa
        console.log(`Missão completa para ${playerId}: ${quest.title}`);
    }

    private giveRewards(playerId: string, rewards: QuestReward[]): void {
        rewards.forEach(reward => {
            switch (reward.type) {
                case 'xp':
                    // Dar experiência
                    console.log(`${playerId} ganhou ${reward.amount} XP`);
                    break;
                case 'gold':
                    // Dar ouro
                    console.log(`${playerId} ganhou ${reward.amount} moedas`);
                    break;
                case 'item':
                    // Dar item
                    console.log(`${playerId} ganhou item: ${reward.itemId}`);
                    break;
            }
        });
    }
}
```

---

## 🔮 **Fase 5: Sistema de Magias (45 min)**

### Passo 5.1: Magias de Arton

```typescript
// scripts/spells/ArtonSpells.ts
export class ArtonSpells {
    
    // Bola de Fogo - Magia clássica
    static castFireball(caster: Player, targetPosition: Vector3): void {
        const fireball = Scene.createRootEntity();
        fireball.setPosition(caster.getPosition().add(new Vector3(0, 1.5, 0)));
        
        // Efeito visual
        const particles = fireball.createComponent("engine:persistentParticleEffect", {
            assetId: "fireball_projectile"
        });
        
        // Som de lançamento
        const audio = fireball.createComponent("engine:audioSource", {
            assetId: "fireball_cast_sound",
            volume: 0.8
        });
        audio.play();
        
        // Animar até o alvo
        this.animateProjectile(fireball, caster.getPosition(), targetPosition, 2000);
        
        // Explodir no alvo
        setTimeout(() => {
            this.createFireballExplosion(targetPosition);
            fireball.destroy();
        }, 2000);
    }
    
    // Cura Divina - Magia de Allihanna
    static castDivineHeal(caster: Player, target?: Player): void {
        const healTarget = target || caster;
        const targetPos = healTarget.getPosition();
        
        // Efeito de cura
        const healEffect = Scene.createRootEntity();
        healEffect.setPosition(targetPos.x, targetPos.y + 1, targetPos.z);
        
        const particles = healEffect.createComponent("engine:persistentParticleEffect", {
            assetId: "divine_heal_effect"
        });
        
        // Som de cura
        const audio = healEffect.createComponent("engine:audioSource", {
            assetId: "heal_sound",
            volume: 0.6
        });
        audio.play();
        
        // Luz dourada
        const light = healEffect.createComponent("engine:light", {
            type: "point",
            color: new Color(1.0, 0.9, 0.6),
            intensity: 2.0,
            range: 5.0
        });
        
        // Remover após 3 segundos
        setTimeout(() => healEffect.destroy(), 3000);
        
        console.log(`${caster.name} curou ${healTarget.name} com Cura Divina`);
    }
    
    // Raio da Tempestade - Magia única de Arton
    static castStormBolt(caster: Player, targetPosition: Vector3): void {
        // Criar raio do céu
        const boltStart = new Vector3(targetPosition.x, targetPosition.y + 20, targetPosition.z);
        const bolt = Scene.createRootEntity();
        bolt.setPosition(boltStart.x, boltStart.y, boltStart.z);
        
        // Efeito de raio
        const lightning = bolt.createComponent("engine:persistentParticleEffect", {
            assetId: "lightning_bolt_effect"
        });
        
        // Som de trovão
        const thunder = bolt.createComponent("engine:audioSource", {
            assetId: "thunder_sound",
            volume: 1.0
        });
        thunder.play();
        
        // Flash de luz
        const flash = Scene.createRootEntity();
        flash.setPosition(targetPosition.x, targetPosition.y, targetPosition.z);
        
        const flashLight = flash.createComponent("engine:light", {
            type: "point",
            color: new Color(0.8, 0.9, 1.0),
            intensity: 10.0,
            range: 15.0
        });
        
        // Animar raio descendo
        this.animateLightning(bolt, boltStart, targetPosition, 500);
        
        // Remover efeitos
        setTimeout(() => {
            bolt.destroy();
            flash.destroy();
        }, 1000);
        
        console.log(`${caster.name} invocou um Raio da Tempestade!`);
    }
    
    // Bênção de Allihanna - Buff de proteção
    static castBlessingOfAllihanna(caster: Player): void {
        const casterPos = caster.getPosition();
        
        // Aura de proteção
        const aura = Scene.createRootEntity();
        aura.setPosition(casterPos.x, casterPos.y, casterPos.z);
        aura.setParent(caster); // Seguir o jogador
        
        const auraEffect = aura.createComponent("engine:persistentParticleEffect", {
            assetId: "blessing_aura_effect"
        });
        
        // Som de bênção
        const blessSound = aura.createComponent("engine:audioSource", {
            assetId: "blessing_sound",
            volume: 0.5
        });
        blessSound.play();
        
        // Durar 30 segundos
        setTimeout(() => aura.destroy(), 30000);
        
        console.log(`${caster.name} recebeu a Bênção de Allihanna!`);
    }
    
    // Funções auxiliares
    private static animateProjectile(projectile: any, start: Vector3, end: Vector3, duration: number): void {
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentPos = Vector3.lerp(start, end, progress);
            projectile.setPosition(currentPos.x, currentPos.y, currentPos.z);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    private static createFireballExplosion(position: Vector3): void {
        const explosion = Scene.createRootEntity();
        explosion.setPosition(position.x, position.y, position.z);
        
        // Efeito de explosão
        const particles = explosion.createComponent("engine:persistentParticleEffect", {
            assetId: "fireball_explosion"
        });
        
        // Som de explosão
        const audio = explosion.createComponent("engine:audioSource", {
            assetId: "explosion_sound",
            volume: 1.0
        });
        audio.play();
        
        // Luz da explosão
        const light = explosion.createComponent("engine:light", {
            type: "point",
            color: new Color(1.0, 0.5, 0.2),
            intensity: 5.0,
            range: 10.0
        });
        
        // Remover após 3 segundos
        setTimeout(() => explosion.destroy(), 3000);
    }
    
    private static animateLightning(bolt: any, start: Vector3, end: Vector3, duration: number): void {
        // Animação rápida do raio descendo
        const startTime = Date.now();
        
        const animate = () => {
            const elapsed = Date.now() - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const currentPos = Vector3.lerp(start, end, progress);
            bolt.setPosition(currentPos.x, currentPos.y, currentPos.z);
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
}
```

---

## 📱 **Fase 6: Interface de Usuário (30 min)**

### Passo 6.1: HUD Principal

```typescript
// scripts/ui/HUDSystem.ts
export class HUDSystem {
    private hudElements: Map<string, any> = new Map();
    
    public createPlayerHUD(player: Player): void {
        const playerId = player.id;
        
        // Painel principal
        const mainPanel = Scene.createRootEntity();
        mainPanel.name = `HUD_${playerId}`;
        
        // Informações do jogador
        this.createPlayerInfo(mainPanel, player);
        
        // Minimapa
        this.createMinimap(mainPanel);
        
        // Barra de missões
        this.createQuestTracker(mainPanel);
        
        // Botões de magia
        this.createSpellButtons(mainPanel, player);
        
        this.hudElements.set(playerId, mainPanel);
    }
    
    private createPlayerInfo(parent: any, player: Player): void {
        const infoPanel = Scene.createRootEntity();
        infoPanel.setParent(parent);
        
        // Nome e nível
        const nameText = infoPanel.createComponent("engine:text", {
            text: `${player.name} - Nível 1`,
            fontSize: 0.4,
            color: new Color(1, 1, 0),
            position: new Vector3(-4, 3, 0)
        });
        
        // Zona atual
        const zoneText = infoPanel.createComponent("engine:text", {
            text: "Zona: Khalmyr",
            fontSize: 0.3,
            color: new Color(0.8, 0.8, 0.8),
            position: new Vector3(-4, 2.5, 0)
        });
    }
    
    private createMinimap(parent: any): void {
        const minimap = Scene.createRootEntity();
        minimap.setParent(parent);
        
        // Fundo do minimapa
        const background = minimap.createComponent("engine:mesh", {
            assetId: "minimap_background",
            position: new Vector3(3, 2, 0),
            scale: new Vector3(1, 1, 1)
        });
        
        // Indicador do jogador
        const playerDot = minimap.createComponent("engine:mesh", {
            assetId: "player_dot",
            position: new Vector3(3, 2, 0.1),
            scale: new Vector3(0.1, 0.1, 0.1)
        });
    }
    
    private createQuestTracker(parent: any): void {
        const questPanel = Scene.createRootEntity();
        questPanel.setParent(parent);
        
        const questTitle = questPanel.createComponent("engine:text", {
            text: "Missões Ativas:",
            fontSize: 0.3,
            color: new Color(1, 1, 0),
            position: new Vector3(-4, 1, 0)
        });
        
        // Lista de missões (será atualizada dinamicamente)
        const questList = questPanel.createComponent("engine:text", {
            text: "• Nenhuma missão ativa",
            fontSize: 0.25,
            color: new Color(0.8, 0.8, 0.8),
            position: new Vector3(-4, 0.5, 0)
        });
    }
    
    private createSpellButtons(parent: any, player: Player): void {
        const spellPanel = Scene.createRootEntity();
        spellPanel.setParent(parent);
        
        const spells = [
            { name: "Bola de Fogo", key: "F" },
            { name: "Cura Divina", key: "H" },
            { name: "Raio da Tempestade", key: "L" },
            { name: "Bênção", key: "B" }
        ];
        
        spells.forEach((spell, index) => {
            const button = spellPanel.createComponent("engine:mesh", {
                assetId: "spell_button",
                position: new Vector3(-2 + index * 0.8, -3, 0),
                scale: new Vector3(0.5, 0.5, 0.5)
            });
            
            const label = spellPanel.createComponent("engine:text", {
                text: `${spell.key}\n${spell.name}`,
                fontSize: 0.2,
                color: new Color(1, 1, 1),
                position: new Vector3(-2 + index * 0.8, -3.5, 0)
            });
        });
    }
    
    public updatePlayerInfo(playerId: string, level: number, zone: string): void {
        // Atualizar informações do HUD
        const hud = this.hudElements.get(playerId);
        if (hud) {
            // Implementar atualização dos elementos
        }
    }
    
    public updateQuestTracker(playerId: string, quests: string[]): void {
        // Atualizar lista de missões
        const hud = this.hudElements.get(playerId);
        if (hud) {
            // Implementar atualização das missões
        }
    }
}
```

---

## 🎯 **Fase 7: Integração e Testes (30 min)**

### Passo 7.1: GameManager Principal

```typescript
// scripts/core/GameManager.ts
import { ZoneManager } from '../zones/ZoneManager';
import { NPCSpawner } from '../npc/NPCSpawner';
import { QuestSystem } from '../quests/QuestSystem';
import { HUDSystem } from '../ui/HUDSystem';
import { ArtonSpells } from '../spells/ArtonSpells';

export class GameManager {
    private zoneManager: ZoneManager;
    private questSystem: QuestSystem;
    private hudSystem: HUDSystem;
    private npcSpawner: NPCSpawner;
    
    constructor() {
        this.initializeSystems();
        this.setupEventListeners();
        this.spawnNPCs();
        
        console.log("🌍 Arton: O Despertar da Tempestade carregado com sucesso!");
    }
    
    private initializeSystems(): void {
        this.zoneManager = new ZoneManager();
        this.questSystem = new QuestSystem();
        this.hudSystem = new HUDSystem();
        this.npcSpawner = new NPCSpawner();
    }
    
    private setupEventListeners(): void {
        // Eventos de jogador
        Players.onPlayerJoined.add((player: Player) => {
            this.onPlayerJoined(player);
        });
        
        Players.onPlayerLeft.add((player: Player) => {
            this.onPlayerLeft(player);
        });
        
        // Eventos de input (magias)
        Input.onKeyPressed.add((player: Player, key: string) => {
            this.handleSpellCast(player, key);
        });
    }
    
    private spawnNPCs(): void {
        // Spawn dos NPCs principais
        this.npcSpawner.spawnLisandra();
        this.npcSpawner.spawnRodleck();
        this.npcSpawner.spawnBeluhga();
        this.npcSpawner.spawnThelos();
        
        console.log("📍 NPCs de Arton spawned com sucesso!");
    }
    
    private onPlayerJoined(player: Player): void {
        console.log(`🎮 ${player.name} entrou em Arton`);
        
        // Criar HUD
        this.hudSystem.createPlayerHUD(player);
        
        // Mensagem de boas-vindas
        this.showWelcomeMessage(player);
        
        // Iniciar detecção de zona
        setInterval(() => {
            this.zoneManager.checkPlayerZone(player);
        }, 2000);
    }
    
    private showWelcomeMessage(player: Player): void {
        const welcome = Scene.createRootEntity();
        welcome.setPosition(player.getPosition().add(new Vector3(0, 3, 0)));
        
        const text = welcome.createComponent("engine:text", {
            text: `Bem-vindo a Arton, ${player.name}!\nO Despertar da Tempestade começou...`,
            fontSize: 0.6,
            color: new Color(1, 0.8, 0),
            billboard: true
        });
        
        setTimeout(() => welcome.destroy(), 8000);
    }
    
    private handleSpellCast(player: Player, key: string): void {
        const targetPos = player.getPosition().add(new Vector3(0, 0, 5));
        
        switch (key.toUpperCase()) {
            case 'F':
                ArtonSpells.castFireball(player, targetPos);
                break;
            case 'H':
                ArtonSpells.castDivineHeal(player);
                break;
            case 'L':
                ArtonSpells.castStormBolt(player, targetPos);
                break;
            case 'B':
                ArtonSpells.castBlessingOfAllihanna(player);
                break;
        }
    }
    
    public getQuestSystem(): QuestSystem {
        return this.questSystem;
    }
    
    public getZoneManager(): ZoneManager {
        return this.zoneManager;
    }
}

// Inicializar o jogo
export const gameManager = new GameManager();
```

### Passo 7.2: Arquivo Principal

```typescript
// scripts/main.ts
import { GameManager } from './core/GameManager';

// Inicializar quando o mundo carregar
const artonWorld = new GameManager();

// Exportar para uso global
(globalThis as any).ArtonWorld = artonWorld;

console.log("🌩️ Arton: O Despertar da Tempestade - Iniciado!");
```

---

## 🔄 **Fase 8: Tornando Remixável (15 min)**

### Passo 8.1: Documentação

Crie um arquivo `REMIX_GUIDE.md`:

```markdown
# 🔄 Guia de Remix - Arton: O Despertar da Tempestade

## Como Adicionar Novos NPCs

1. Estenda a classe `NPCBase`
2. Defina os dados do NPC
3. Adicione ao `NPCSpawner`

## Como Criar Novas Zonas

1. Adicione ao `ZoneManager.initializeZones()`
2. Configure coordenadas e efeitos
3. Construa visualmente no editor

## Como Adicionar Magias

1. Implemente em `ArtonSpells`
2. Adicione ao `handleSpellCast`
3. Configure tecla de atalho
```

### Passo 8.2: Assets Públicos

Publique na biblioteca pública:
- `arton_npc_base` - Modelo base de NPC
- `fireball_effect` - Efeito de bola de fogo
- `healing_light` - Efeito de cura
- `lightning_bolt` - Efeito de raio
- `zone_notification` - Template de notificação

---

## 📤 **Fase 9: Publicação e Submissão (15 min)**

### Passo 9.1: Publicar o Mundo

1. No editor, clique em **Publish**
2. Configure:
   - **Título:** "Arton: O Despertar da Tempestade"
   - **Descrição:** "RPG baseado em Tormenta 20 com NPCs interativos"
   - **Tags:** "RPG", "Tormenta", "Fantasy", "Remixable"
   - ✅ **Public**
   - ✅ **Remixable**

### Passo 9.2: Submeter na Competição

1. Acesse [Devpost - Open Source Champions](https://devpost.com)
2. Crie nova submissão
3. Preencha:
   - **Título:** "Arton: O Despertar da Tempestade - RPG de Tormenta 20"
   - **Descrição:** Use o template do README
   - **Links:** Mundo + Tutorial + Assets
   - **Categorias:** Main + Tutorial Mini-Challenge

---

## 🏆 **Resultados Esperados**

Ao final deste tutorial, você terá:

✅ **Mundo completo** baseado em Arton  
✅ **6 zonas** com ambientação única  
✅ **4 NPCs** interativos com diálogos  
✅ **Sistema de missões** funcional  
✅ **4 magias** com efeitos visuais  
✅ **HUD** informativo e funcional  
✅ **Código remixável** bem documentado  
✅ **Assets públicos** para a comunidade  

---

## 📚 **Recursos Adicionais**

- [Meta Horizon Docs](https://developers.meta.com/horizon)
- [TypeScript Guide](https://www.typescriptlang.org/docs/)
- [Tormenta 20 SRD](https://tormenta20.com.br)
- [Discord da Comunidade](#)

---

**Criado para a competição Open Source Champions 2025**  
*Que Allihanna abençoe sua jornada em Arton!* 🌟⚡
