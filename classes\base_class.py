"""
Storm RPG - Classe Base para Classes de Personagem
Define a estrutura básica para todas as classes
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from enum import Enum
from core.attributes import AttributeType


class SpellcastingType(Enum):
    """Tipos de conjuração"""
    NONE = "none"
    ARCANE = "arcane"
    DIVINE = "divine"
    SPONTANEOUS = "spontaneous"
    PREPARED = "prepared"


class SaveType(Enum):
    """Tipos de resistência"""
    FORTITUDE = "fortitude"  # Baseado em Constituição
    REFLEX = "reflex"        # Baseado em Destreza
    WILL = "will"            # Baseado em Sabedoria


@dataclass
class ClassFeature:
    """Representa uma característica de classe"""
    name: str
    description: str
    level_gained: int
    feature_type: str = "passive"  # passive, active, spell-like
    uses_per_day: Optional[int] = None
    
    def apply_to_character(self, character):
        """Aplica a característica ao personagem"""
        character.add_feature(f"{self.name}: {self.description}")


@dataclass
class ClassLevel:
    """Representa um nível de classe"""
    level: int
    hit_die: int
    base_attack_bonus: int
    fortitude_save: int
    reflex_save: int
    will_save: int
    features: List[ClassFeature]
    spells_per_day: Dict[int, int] = None  # Nível da magia: quantidade por dia
    
    def get_total_hp_gain(self, constitution_modifier: int) -> int:
        """Calcula ganho de HP no nível"""
        return self.hit_die + constitution_modifier


class CharacterClass(ABC):
    """Classe base abstrata para todas as classes de personagem"""
    
    def __init__(self):
        self.name = self.get_name()
        self.description = self.get_description()
        self.hit_die = self.get_hit_die()
        self.skill_points_per_level = self.get_skill_points_per_level()
        self.class_skills = self.get_class_skills()
        self.weapon_proficiencies = self.get_weapon_proficiencies()
        self.armor_proficiencies = self.get_armor_proficiencies()
        self.spellcasting_type = self.get_spellcasting_type()
        self.spellcasting_attribute = self.get_spellcasting_attribute()
        self.levels = self.get_class_levels()
    
    @abstractmethod
    def get_name(self) -> str:
        """Nome da classe"""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Descrição da classe"""
        pass
    
    @abstractmethod
    def get_hit_die(self) -> int:
        """Dado de vida da classe"""
        pass
    
    @abstractmethod
    def get_skill_points_per_level(self) -> int:
        """Pontos de perícia por nível"""
        pass
    
    @abstractmethod
    def get_class_skills(self) -> List[str]:
        """Lista de perícias de classe"""
        pass
    
    @abstractmethod
    def get_weapon_proficiencies(self) -> List[str]:
        """Proficiências com armas"""
        pass
    
    @abstractmethod
    def get_armor_proficiencies(self) -> List[str]:
        """Proficiências com armaduras"""
        pass
    
    @abstractmethod
    def get_spellcasting_type(self) -> SpellcastingType:
        """Tipo de conjuração"""
        pass
    
    @abstractmethod
    def get_spellcasting_attribute(self) -> Optional[AttributeType]:
        """Atributo usado para conjuração"""
        pass
    
    @abstractmethod
    def get_class_levels(self) -> Dict[int, ClassLevel]:
        """Dicionário com informações de cada nível"""
        pass
    
    def apply_to_character(self, character):
        """Aplica a classe ao personagem"""
        character.character_class = self
        
        # Aplicar HP do 1º nível
        level_1 = self.levels[1]
        constitution_mod = character.attributes.get_modifier(AttributeType.CONSTITUTION)
        max_hp = level_1.hit_die + constitution_mod
        character.health_manager.max_hp = max_hp
        character.health_manager.current_hp = max_hp
        
        # Aplicar pontos de mana se for conjurador
        if self.spellcasting_type != SpellcastingType.NONE:
            self.apply_spellcasting(character, 1)
        
        # Aplicar características do 1º nível
        for feature in level_1.features:
            feature.apply_to_character(character)
        
        # Marcar perícias de classe como treinadas
        for skill_name in self.class_skills:
            if skill_name in character.skills:
                character.skills[skill_name].trained = True
    
    def level_up(self, character):
        """Aplica benefícios de subir de nível"""
        new_level = character.level + 1
        if new_level > 20:
            return False
        
        # Atualizar nível
        character.level_manager.level = new_level
        
        # Aplicar benefícios do novo nível
        if new_level in self.levels:
            level_info = self.levels[new_level]
            
            # Ganhar HP
            constitution_mod = character.attributes.get_modifier(AttributeType.CONSTITUTION)
            hp_gain = level_info.get_total_hp_gain(constitution_mod)
            character.health_manager.max_hp += hp_gain
            character.health_manager.current_hp += hp_gain
            
            # Aplicar características do nível
            for feature in level_info.features:
                feature.apply_to_character(character)
            
            # Atualizar conjuração se aplicável
            if self.spellcasting_type != SpellcastingType.NONE:
                self.apply_spellcasting(character, new_level)
        
        return True
    
    def apply_spellcasting(self, character, level: int):
        """Aplica habilidades de conjuração"""
        if self.spellcasting_type == SpellcastingType.NONE:
            return
        
        # Calcular pontos de mana baseado no atributo de conjuração
        if self.spellcasting_attribute:
            attr_mod = character.attributes.get_modifier(self.spellcasting_attribute)
            base_mana = level * 2  # Base de 2 PM por nível
            bonus_mana = attr_mod * level  # Bônus do atributo
            character.max_mana_points = max(0, base_mana + bonus_mana)
            character.mana_points = character.max_mana_points
    
    def get_level_info(self, level: int) -> Optional[ClassLevel]:
        """Retorna informações de um nível específico"""
        return self.levels.get(level)
    
    def get_base_attack_bonus(self, level: int) -> int:
        """Retorna bônus de ataque base para um nível"""
        level_info = self.get_level_info(level)
        return level_info.base_attack_bonus if level_info else 0
    
    def get_save_bonus(self, save_type: SaveType, level: int) -> int:
        """Retorna bônus de resistência para um nível"""
        level_info = self.get_level_info(level)
        if not level_info:
            return 0
        
        if save_type == SaveType.FORTITUDE:
            return level_info.fortitude_save
        elif save_type == SaveType.REFLEX:
            return level_info.reflex_save
        elif save_type == SaveType.WILL:
            return level_info.will_save
        
        return 0
    
    def get_features_at_level(self, level: int) -> List[ClassFeature]:
        """Retorna características ganhas em um nível"""
        level_info = self.get_level_info(level)
        return level_info.features if level_info else []
    
    def __str__(self) -> str:
        return f"{self.name} - {self.description}"
    
    def get_summary(self) -> str:
        """Retorna um resumo da classe"""
        lines = [
            f"=== {self.name} ===",
            f"Descrição: {self.description}",
            f"Dado de Vida: d{self.hit_die}",
            f"Pontos de Perícia: {self.skill_points_per_level} por nível",
            f"Conjuração: {self.spellcasting_type.value}",
            "",
            "Perícias de Classe:",
            f"  {', '.join(self.class_skills)}",
            "",
            "Proficiências:",
            f"  Armas: {', '.join(self.weapon_proficiencies)}",
            f"  Armaduras: {', '.join(self.armor_proficiencies)}",
            "",
            "Características por Nível:"
        ]
        
        for level in range(1, 6):  # Mostra apenas os primeiros 5 níveis
            if level in self.levels:
                level_info = self.levels[level]
                features = [f.name for f in level_info.features]
                features_str = ", ".join(features) if features else "Nenhuma"
                lines.append(f"  Nível {level}: {features_str}")
        
        return "\n".join(lines)


# Funções auxiliares para criar progressões comuns
def create_full_bab_progression() -> Dict[int, int]:
    """Cria progressão de BAB completa (guerreiros)"""
    return {level: level for level in range(1, 21)}

def create_medium_bab_progression() -> Dict[int, int]:
    """Cria progressão de BAB média (clérigos, druidas)"""
    return {level: (level * 3) // 4 for level in range(1, 21)}

def create_low_bab_progression() -> Dict[int, int]:
    """Cria progressão de BAB baixa (magos)"""
    return {level: level // 2 for level in range(1, 21)}

def create_good_save_progression() -> Dict[int, int]:
    """Cria progressão de resistência boa"""
    return {level: 2 + (level // 2) for level in range(1, 21)}

def create_poor_save_progression() -> Dict[int, int]:
    """Cria progressão de resistência ruim"""
    return {level: level // 3 for level in range(1, 21)}


# Teste de exemplo
if __name__ == "__main__":
    print("=== Teste do Sistema Base de Classes ===")
    
    # Testar progressões
    print("BAB Completo (nível 5):", create_full_bab_progression()[5])
    print("BAB Médio (nível 5):", create_medium_bab_progression()[5])
    print("BAB Baixo (nível 5):", create_low_bab_progression()[5])
    
    print("Resistência Boa (nível 5):", create_good_save_progression()[5])
    print("Resistência Ruim (nível 5):", create_poor_save_progression()[5])
